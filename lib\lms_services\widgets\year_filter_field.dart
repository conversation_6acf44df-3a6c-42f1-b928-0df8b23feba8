import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';

class YearFilterField extends StatefulWidget {
  final Color borderColor;
  final Color iconColor;
  final double iconSize;
  final String label;
  final Function(int?) onYearSelected;
  final int? initialYear;

  const YearFilterField({
    Key? key,
    this.borderColor = borderLightColor,
    this.iconColor = secondTextColor,
    this.iconSize = 20,
    required this.onYearSelected,
    required this.label,
     this.initialYear,
  }) : super(key: key);

  @override
  State<YearFilterField> createState() => _YearFilterFieldState();
}

class _YearFilterFieldState extends State<YearFilterField> {
  final TextEditingController _controller = TextEditingController();
  int? selectedYear;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialYear;
    if (selectedYear != null) {
      _controller.text = selectedYear.toString();
    }
  }

  void _updateControllerText() {
    if (selectedYear != null) {
      _controller.text = selectedYear.toString();
    } else {
      _controller.text = AppLocalizations.of(context).translate('All');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Update the controller text after the context is fully available
    if (selectedYear == null) {
      _controller.text = AppLocalizations.of(context).translate('All');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _showYearPicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return YearPickerBottomSheet(
          selectedYear: selectedYear,
          onYearSelected: (year) {
            setState(() {
              selectedYear = year;
              _updateControllerText();
            });
            widget.onYearSelected(year);
            Navigator.pop(context);
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      readOnly: true,
      onTap: _showYearPicker,
      decoration: InputDecoration(
        label: Text(
          widget.label,
          style: FontUtilities.getTextStyle(
            TextType.regular,
            size: 12.sp,
            textColor: mainTextColor,
            fontWeight: FontWeight.w400,
          ),
        ),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (selectedYear != null && _controller.text.isNotEmpty)
              IconButton(
                icon: Icon(
                  getIconFromCss("fa-solid fa-xmark"),
                  color: widget.iconColor,
                  size: widget.iconSize.sp,
                ),
                onPressed: () {
                  setState(() {
                    selectedYear = null;
                    _updateControllerText();
                  });
                  widget.onYearSelected(null);
                },
              ),
            SizedBox(width: 5.w),
            Icon(
              getIconFromCss("fa-regular fa-calendar"),
              color: widget.iconColor,
              size: widget.iconSize.sp,
            ),
          ],
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: widget.borderColor, width: 1.5),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: widget.borderColor, width: 1.5),
        ),
        border: UnderlineInputBorder(
          borderSide: BorderSide(color: widget.borderColor, width: 1.5),
        ),
      ),
    );
  }
}

class YearPickerBottomSheet extends StatelessWidget {
  final int? selectedYear;
  final Function(int?) onYearSelected;

  const YearPickerBottomSheet({
    Key? key,
    this.selectedYear,
    required this.onYearSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentYear = DateTime.now().year;
    final startYear = 2008;
    final years = List.generate(
      currentYear - startYear + 1,
      (index) => currentYear - index
    );

    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocalizations.of(context).translate('Select Year'),
                style:FontUtilities.getTextStyle(
                  TextType.medium,
                  size: 18.sp,
                  fontWeight: FontWeight.bold,
                  textColor: mainTextColor,
               ),
              ),
              TextButton(
                onPressed: () {
                  onYearSelected(null);
                },
                child: Text(AppLocalizations.of(context).translate('Clear'),
                  style: FontUtilities.getTextStyle(
                    TextType.regular,
                    size: 14.sp,
                    textColor: alertColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4, 
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 20.h,
                childAspectRatio: 2.0, 
              ),
              itemCount: years.length,
              itemBuilder: (context, index) {
                final year = years[index];
                final isSelected = year == selectedYear;

                return InkWell(
                  onTap: () {
                    onYearSelected(year);
                  },
                  borderRadius: BorderRadius.circular(8.r),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? secondTextColor.withValues(alpha: 0.1)
                          : Colors.white,
                      border: Border.all(
                        color: isSelected
                            ? secondTextColor
                            :borderLightColor,
                        width: isSelected ? 2 : 1,
                      ),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Text(
                        year.toString(),
                        style: FontUtilities.getTextStyle(
                          TextType.regular,
                          size: 14.sp,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          textColor: isSelected ? secondTextColor : mainTextColor,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
