// import 'package:eeh/my_team_timesheet_service/presentation/bloc/my_team_time_sheet_bloc.dart';
// import 'package:eeh/my_team_timesheet_service/widgets/team_time_sheet_items_card.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// import '../../l10n/app_localizations.dart';
// import '../../shared/styles/colors.dart';
// import '../../shared/utility/font_utility.dart';
// import '../../timesheet_service/view/widget/day_time_widget.dart';
// // import '../data/models/my_team_time_sheet_response_model/teamtimesheet.dart';

// class EmployeeCard extends StatefulWidget {
//   const EmployeeCard(
//       {super.key,
//       // required this.teamtimesheetModel,
//       required this.startDate,
//       required this.endDate,
//       this.hasMissingHours = false});
//   // final TeamtimesheetModel teamtimesheetModel;
//   final String startDate;
//   final String endDate;
//   final bool hasMissingHours;

//   @override
//   State<StatefulWidget> createState() {
//     return EmployeeCardState();
//   }
// }

// class EmployeeCardState extends State<EmployeeCard> {
//   late MyTeamTimeSheetBloc bloc;
//   @override
//   Widget build(BuildContext context) {
//     bloc = context.read<MyTeamTimeSheetBloc>();
//     return Container(
//       width: MediaQuery.of(context).size.width,
//       padding: EdgeInsets.all(16.sp),
//       margin: EdgeInsets.symmetric(horizontal: 16.w),
//       decoration: ShapeDecoration(
//         color: Colors.white,
//         shape: RoundedRectangleBorder(
//           side: BorderSide(
//             width: 1.w,
//             color: widget.hasMissingHours ? alertError : borderCardColor,
//           ),
//           borderRadius: BorderRadius.circular(8),
//         ),
//       ),
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           getCardHeader(),
//           getTeamTimesheetItemCardWidget(context),
//         ],
//       ),
//     );
//   }

//   TeamTimesheetItemCardWidget getTeamTimesheetItemCardWidget(
//       BuildContext context) {
//     return TeamTimesheetItemCardWidget(
//       empId: "123",
//       endDate: widget.endDate,
//       startDate: widget.startDate,
//       bloc: bloc,
//       totalDaysWidget: DayAndTimeWidget(
//         title: AppLocalizations.of(context).translate("available_annual_leave"),
//         value: widget.teamtimesheetModel.availableannualleave.toString(),
//         titleTextStyle: getOutOfOfficeTitleStyle(),
//         valueTextStyle: getOutOfOfficeValueStyle(),
//       ),
//       gridViewItemWidget: [
//         DayAndTimeWidget(
//           title: AppLocalizations.of(context).translate("Clock in"),
//           value: widget.teamtimesheetModel.biometricattendancehours.toString(),
//           hasIcon: false,
//           titleTextStyle: getOutOfOfficeTitleStyle(),
//           valueTextStyle: getOutOfOfficeValueStyle(),
//         ),
//         DayAndTimeWidget(
//           title: AppLocalizations.of(context).translate("Out of Office"),
//           value:
//               widget.teamtimesheetModel.outofofficeattendancehours.toString(),
//           hasIcon: false,
//           titleTextStyle: getOutOfOfficeTitleStyle(),
//           valueTextStyle: getOutOfOfficeValueStyle(),
//         ),
//         DayAndTimeWidget(
//           title: AppLocalizations.of(context).translate("Leaves"),
//           value: widget.teamtimesheetModel.leaves.toString(),
//           hasIcon: false,
//           titleTextStyle: getOutOfOfficeTitleStyle(),
//           valueTextStyle: getOutOfOfficeValueStyle(),
//         ),
//         DayAndTimeWidget(
//           title: AppLocalizations.of(context).translate("project_hours"),
//           value: widget.teamtimesheetModel.projecthours.toString(),
//           hasIcon: false,
//           titleTextStyle: getOutOfOfficeTitleStyle(),
//           valueTextStyle: getOutOfOfficeValueStyle(),
//         ),
//         DayAndTimeWidget(
//           title: AppLocalizations.of(context).translate("missing_hours"),
//           value: widget.teamtimesheetModel.missinghours.toString(),
//           hasIcon: false,
//           titleTextStyle: getOutOfOfficeTitleStyle(),
//           valueTextStyle: getOutOfOfficeValueStyle(),
//         ),
//       ],
//     );
//   }

//   Row getCardHeader() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Expanded(
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             mainAxisAlignment: MainAxisAlignment.start,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               SizedBox(
//                 width: 44.w,
//                 height: 44.h,
//                 child: ClipRect(
//                   child: Align(
//                     alignment: Alignment.topCenter,
//                     heightFactor: 0.5,
//                     child: CircleAvatar(
//                       // backgroundImage:
//                       //     NetworkImage("https://placehold.co/44x44"),
//                       child: Text(
//                         AppLocalizations.appLang == 'en'
//                             ? widget.teamtimesheetModel.empNameInitials ?? ""
//                             : widget.teamtimesheetModel.empNameInitialsAr ?? "",
//                         style: FontUtilities.getTextStyle(
//                           TextType.regular,
//                           size: 14.sp,
//                           fontWeight: FontWeight.w500,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//               SizedBox(
//                 width: 8.w,
//               ),
//               Expanded(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       AppLocalizations.appLang == 'en'
//                           ? widget.teamtimesheetModel.employeenameen ?? ""
//                           : widget.teamtimesheetModel.employeenamear ?? "",
//                       style: FontUtilities.getTextStyle(
//                         TextType.regular,
//                         size: 14.sp,
//                         fontWeight: FontWeight.w500,
//                       ),
//                       overflow: TextOverflow.ellipsis,
//                     ),
//                     Text(
//                       AppLocalizations.appLang == 'en'
//                           ? widget.teamtimesheetModel.employeepositionen ?? ""
//                           : widget.teamtimesheetModel.employeepositionar ?? "",
//                       style: FontUtilities.getTextStyle(
//                         TextType.regular,
//                         size: 12.sp,
//                         fontWeight: FontWeight.w400,
//                       ),
//                     ),
//                     Text(
//                       '${AppLocalizations.of(context).translate("wfh")} : ${widget.teamtimesheetModel.wfhconsumed ?? 0} ${AppLocalizations.of(context).translate("Out of")} ${widget.teamtimesheetModel.wfhbalance ?? 0}',
//                       style: FontUtilities.getTextStyle(
//                         TextType.regular,
//                         size: 12.sp,
//                         fontWeight: FontWeight.w400,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//         BlocBuilder<MyTeamTimeSheetBloc, MyTeamTimeSheetState>(
//           builder: (context, state) {
//             final bool isExpanded = bloc.expandedEmployeeId ==
//                 widget.teamtimesheetModel.employeeid.toString();

//             return IconButton(
//               icon: Icon(
//                 isExpanded
//                     ? Icons.keyboard_arrow_down
//                     : Icons.keyboard_arrow_right,
//               ),
//               onPressed: () {
//                 if (isExpanded) {
//                   bloc.add(CollapseEmployeeTimeSheetDetailsEvent(
//                     employeeId: widget.teamtimesheetModel.employeeid.toString(),
//                   ));
//                 } else {
//                   // If collapsed, expand it
//                   bloc.add(ExpandEmployeeTimeSheetDetailsEvent(
//                     employeeId: widget.teamtimesheetModel.employeeid.toString(),
//                   ));
//                 }
//               },
//             );
//           },
//         )
//       ],
//     );
//   }

//   getOutOfOfficeTitleStyle() => FontUtilities.getTextStyle(
//         TextType.regular,
//         fontWeight: FontWeight.w500,
//         size: 12.sp,
//       );

//   getOutOfOfficeValueStyle() => FontUtilities.getTextStyle(
//         TextType.disable,
//         fontWeight: FontWeight.w500,
//         size: 12.sp,
//       );
// }
