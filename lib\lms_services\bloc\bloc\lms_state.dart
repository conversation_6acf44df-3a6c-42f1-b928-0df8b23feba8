part of 'lms_bloc.dart';

sealed class LmsState extends Equatable {
  const LmsState();

  @override
  List<Object> get props => [];
}

final class LmsInitial extends LmsState {}
final class GetLmsSheetIdLoadingState extends LmsState {}


final class GetLmsSheetIdSuccessState extends LmsState {
  final GetLmsSheetIdResponseModel model;
  const GetLmsSheetIdSuccessState(this.model);

  @override
  List<Object> get props => [model];
}

final class GetLmsSheetIdErrorState extends LmsState {
  final String message;
  const GetLmsSheetIdErrorState(this.message);

  @override
  List<Object> get props => [message];
}

final class GetLmsMyLearningLoadingState extends LmsState {}
final class GetLmsMyLearningSuccessState extends LmsState {
  final MyLearningResponseModel myLearningResponseModel;
  const GetLmsMyLearningSuccessState(this.myLearningResponseModel);
  @override
  List<Object> get props => [myLearningResponseModel];
}

final class GetLmsMyLearningErrorState extends LmsState {
  final String message;
  const GetLmsMyLearningErrorState(this.message);

  @override
  List<Object> get props => [message];
}
final class GetLmsMyTeamLearningLoadingState extends LmsState {}
final class GetLmsMyTeamLearningSuccessState extends LmsState {
  final MyLearningResponseModel myLearningResponseModel;
  const GetLmsMyTeamLearningSuccessState(this.myLearningResponseModel);
  @override
  List<Object> get props => [myLearningResponseModel];
}

final class GetLmsMyTeamLearningErrorState extends LmsState {
  final String message;
  const GetLmsMyTeamLearningErrorState(this.message);

  @override
  List<Object> get props => [message];
}
