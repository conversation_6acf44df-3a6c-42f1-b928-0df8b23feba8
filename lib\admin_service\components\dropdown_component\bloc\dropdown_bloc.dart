import 'package:eeh/admin_service/components/dropdown_component/bloc/dropdown_event.dart';
import 'package:eeh/admin_service/components/dropdown_component/bloc/dropdown_state.dart';
import 'package:eeh/admin_service/components/dropdown_component/model/dropdown_list_response.dart';
import 'package:eeh/admin_service/components/dropdown_component/repo/dropdown_repo.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DropDownBloc extends Bloc<DropDownEvent, DropDownState> {
  late DropDownRepo repo;
  DropdownListResponse ? dropdownListResponse; 
  List<GetData> filterList = [];
  DropDownBloc() : super(DropdownInitialState()) {
    on<DropDownInitialEvent>((event, emit) {
      repo = DropDownRepo(tableName: event.tableName??'');
      emit(DropdownInitialState());
    });

    on<SearchEvent>((event, emit) {
      emit(SearchState());
      onSearch(event.value, emit);
    });

    on<FetchDropDownListEvent>((event,emit)async{
      emit(DropDownListLoadingState());
      await fetchDropDownList(emit,event.key);
    });
    
    on<UpdateHeightEvent>((event, emit) {
     emit(UpdateHeightState()); 
    });

  }

  onSearch(String text, emitter) {
    if (text.isEmpty) {
      filterList = dropdownListResponse?.getdata ?? [];
    } else {
      filterList = [];
      for (var item in dropdownListResponse?.getdata ?? []) {
        if ((AppLocalizations.appLang == 'en' ? item.nameen : item.namear)
            .toString()
            .toLowerCase()
            .contains(text.toLowerCase())) {
          filterList.add(item);
        }
      }
    }         
       emitter(SearchSuccessState());
  }

   fetchDropDownList(emitter,String key) async {
    await repo
        .fetchDropDownList(key:key)
        .then((response) => onFetchDropDownList(response, emitter));
  }

  void onFetchDropDownList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchDropDownListSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(DropDownListErrorState());
    });
  }

  void onFetchDropDownListSuccess(data, emitter) {
    dropdownListResponse = DropdownListResponse.fromJson(data);
    filterList = dropdownListResponse?.getdata??[];
    emitter(DropDownListSuccessState());
  }
}
