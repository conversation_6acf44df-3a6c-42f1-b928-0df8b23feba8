import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/widgets/success_screen_list_item.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../model/ceo_meetings_submit_response.dart';

class CEOMeetingsSuccessScreenContentWidget extends StatelessWidget {
  final CEOMeetingsSubmitResponse? requestData;

  const CEOMeetingsSuccessScreenContentWidget(
      {super.key, required this.requestData});

  @override
  Widget build(BuildContext context) {
    DateTime date = DateTime.parse(requestData?.meetingDate ?? '');
    String formattedDate = DateFormat("d MMMM yyyy").format(date);
    return requestData == null
        ? SizedBox.shrink()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Request Type"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.requestType ?? ''
                    : requestData?.requestTypeAr ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate('Request ID'),
                value: requestData?.displayRecid.toString() ?? '',
              ),
              SuccessScreenListItem(
                title:
                    AppLocalizations.of(context).translate("CEO / CEO Groups"),
                value:(requestData?.ceoGroups?.length ?? 0).toString()
              ),
              SuccessScreenListItem(
                title:
                    AppLocalizations.of(context).translate("Meeting category"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.meetCategoryEn ?? ''
                    : requestData?.meetCategoryAr ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context)
                    .translate("Required from the meeting"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.requiredMeetEn ?? ''
                    : requestData?.requiredMeetAr ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context)
                    .translate("Purpose of the meeting"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.purposeMeetEn ?? ''
                    : requestData?.purposeMeetAr ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Meeting type"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.meetingTypeEn ?? ''
                    : requestData?.meetingTypeAr ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context)
                    .translate("related_to_board_of_directors"),
                value: requestData?.relatedBoardD.toString() ?? '',
              ),
              SuccessScreenListItem(
                title:
                    AppLocalizations.of(context).translate("Meeting Subject"),
                value: requestData?.meetingSubject ?? '',
              ),
              SuccessScreenListItem(
                title:
                    AppLocalizations.of(context).translate("Meeting Requester"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.mRequesterEn ?? ''
                    : requestData?.mRequesterAr ?? '',
              ),
              SuccessScreenListItem(
                title:
                    AppLocalizations.of(context).translate("Meeting Attendees"),
                value: (requestData?.meetingAttendees?.length ?? 0).toString(),
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Note Taker"),
                value: AppLocalizations.appLang == 'en'
                    ? requestData?.noteTakerEn ?? ''
                    : requestData?.noteTakerAr ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Meeting Date"),
                value: formattedDate,
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Time"),
                value:
                    'From ${requestData?.startTime} To ${requestData?.endTime}',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Agenda"),
                value: requestData?.agenda ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context)
                    .translate("expect_after_the_meeting"),
                value: requestData?.whatDoWeExpect ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("note"),
                value: requestData?.notes == null ? '' : requestData?.notes ?? '',
              ),
            ],
          );
  }
}
