import '../../services_screen/models/services_response.dart';
import '../../shared/utility/methods.dart';
import '../models/esm_request_data.dart';
import '../models/groups_and_members.dart';
import '../models/service_list_models/services_list.dart';

abstract class TasksState {}

class TasksInitialState extends TasksState {}

class PendingTasksLoadingState extends TasksState {}

class PendingTasksSuccessState extends TasksState {}

class PendingTasksErrorState extends TasksState {}

class HistoryTasksLoadingState extends TasksState {}

class HistoryTasksSuccessState extends TasksState {}

class HistoryTasksErrorState extends TasksState {}

class AllTasksLoadingState extends TasksState {}

class AllTasksSuccessState extends TasksState {}

class AllTasksErrorState extends TasksState {}

class SearchInTasksLoadingState extends TasksState {}

class SearchInTasksSuccessState extends TasksState {}

class SearchInTasksErrorState extends TasksState {}

class UpdateEsmLoadingState extends TasksState {}

class UpdateEsmSuccessState extends TasksState {
  final String mutableContext;
  UpdateEsmSuccessState({required this.mutableContext});
}

class UpdateEsmErrorState extends TasksState {}

class CheckInCheckOutLoadingState extends TasksState {}

class CheckInCheckOutSuccessState extends TasksState {
  String route;
  String mutableContext;
  CheckInCheckOutSuccessState(this.route,this.mutableContext){
    performSuccessVibration();
  }
}

class CheckInCheckOutErrorState extends TasksState {
  CheckInCheckOutErrorState(){
    performFailureVibration();
  }
}

class ESMServiceListLoadingState extends TasksState {}

class ESMServiceListSuccessState extends TasksState {
  dynamic servicesResponse;
  ESMServiceListSuccessState({this.servicesResponse});
}
class ESMServiceListErrorState extends TasksState {}

class ESMServiceDataLoadingState extends TasksState {}

class ESMServiceDataSuccessState extends TasksState {
  EsmRequestData? esmRequestData;
  ESMServiceDataSuccessState({this.esmRequestData});
}
class ESMServiceDataErrorState extends TasksState {}

class GroupsAndMembersLoadingState extends TasksState {}

class GroupsAndMembersSuccessState extends TasksState {
  GroupsAndMembersResponse? groupsAndMembersResponse;
  GroupsAndMembersSuccessState({this.groupsAndMembersResponse});
}
class GroupsAndMembersErrorState extends TasksState {}

class TaskActionLoadingState extends TasksState {}

class TaskActionSuccessState extends TasksState {
  String mutableContext;
  String route;
  TaskActionSuccessState(this.mutableContext, this.route){
    performSuccessVibration();
  }
}

class TaskActionErrorState extends TasksState {
  TaskActionErrorState(){
    performFailureVibration();
  }
}

class CheckFilesState extends TasksState {}

class UploadAttachmentLoadingState extends TasksState {}

class UploadAttachmentFilesSuccessState extends TasksState {}

class SendInternalDiscussionMessageLoadingState extends TasksState {}

class SendInternalDiscussionMessageSuccessState extends TasksState {}

class SendInternalDiscussionMessageErrorState extends TasksState {}

class SubmitLoadingState extends TasksState {}

class SubmitSuccessState extends TasksState {}

class SubmitErrorState extends TasksState {
  SubmitErrorState() {
    performFailureVibration();
  }
}
class TemplatesLoadingState extends TasksState{}

class TemplatesErrorState extends TasksState{}

class TemplatesSuccessState extends TasksState{}

class SubmitTemplatesLoadingState extends TasksState{}

class SubmitTemplatesErrorState extends TasksState{}

class SubmitTemplatesSuccessState extends TasksState {
  String mutableContext;
  String route;
  SubmitTemplatesSuccessState(this.mutableContext, this.route) {
    performSuccessVibration();
  }
}
