const mainLogin = "MainFunciton/login";
const genericObject = "integration/AddGenericObject";
const versionEndpoint = "UDAs/dictionaryData/1692/APPLICATION_VERSION";
const navigationBarColorEndpoint = "integration/repository?name=themeColor&filter";
const getGenericObject = "integration/GetGenericObject";
const deleteSchoolAllowanceAttachment =
    "GenericObject/deleteAttachment/HRS_AT_SCHOOLATTACHM/HRS_AP_SCHOOLATTACHM";
const homeEndPoint = "mobileObjects/checkLoadingRules/befor/1026/2/en";
const leaveTypeBalancesChart = "mobileObjects/getChartUdaDataMobile/1501";
const slideViewChart = "mobileObjects/getChartUdaDataMobile/1297";
const countPicChart = "mobileObjects/getChartUdaDataMobile/1503";
const multipleSave = 'integration/upload/addAttachment/multiPart';
const delegates = 'integrationRepo/excuteQuery/1505';
const leaveType = 'integrationRepo/excuteQuery/1484';
const profileInfo = 'MainFunciton/getEmpInfo';
const sendOTP = 'MainFunciton/sendTwoFactorAuthOTP';
const verifyOtpCode = 'MainFunciton/checkCode/v2';
const getGenaricOpject = 'integration/GetGenericObject';
const gridAttachment = 'integration/upload/grid/addAttachment';
const deleteDepLunchAttach =
    'GenericObject/deleteAttachment/DEP_AT_ATTACHMENTS/DEP_AP_ATTACHMENTS/1';
const deleteLeaveAttachment =
    "GenericObject/deleteAttachment/HRS_AT_LEAVEATTACHME/HRS_AP_LEAVEATTACHME";
const deleteExpenseAttachment =
    "GenericObject/deleteAttachment/HRS_AT_ATTACHMENTS/HRS_AP_ATTACHMENTS";
const deleteMazayaAttachment =
    "GenericObject/deleteAttachment/HRS_AT_SELFATTACHMEN/HRS_AP_SELFATTACHMEN";
const deleteIbanNumAttach =
    'GenericObject/deleteAttachment/BAN_AT_ATTACHMENTS/BAN_AP_ATTACHMENTS/1';
const deletePassportAttach =
    'GenericObject/deleteAttachment/PAS_AT_ATTACHMENT/PAS_AP_ATTACHMENT/1';
const deleteLoanPreCAttach = "GenericObject/deleteAttachment/LPC_AT_ATTACHMENTS/LPC_AP_ATTACHMENTS/98";
const deleteTenderAttach  = "GenericObject/deleteAttachment/ADS_AT_ATTACHMENTS/ADS_AP_ATTACHMENTS/1/1461";
const deleteLetterOfGuarantee = "GenericObject/deleteAttachment/ADS_AT_ATTACHMENTS/ADS_AP_ATTACHMENTS/1/1461";
const signOut = "MainFunciton/signOut";
const titles = "UDAs/dictionaryData/2284/LIST_OF_TITLES";
const reason = "UDAs/dictionaryData/3349/REASON";
const deleteEducationAttach =
    "GenericObject/deleteAttachment/EDU_AT_ATTACHMENTS/EDU_AP_ATTACHMENTS/1";
const deleteESMAttach =
    "GenericObject/deleteAttachment/ESM_AT_ATTACHMENTS/ESM_AP_ATTACHMENTS/1";
const getTimeSheetRecordsEndPoint = "integration/GetGenericObject";
const getTimeSheetDropDownListDataEndPoint = "integration/AddGenericObject";
const getLmsMyLearning = "integration/repository";
