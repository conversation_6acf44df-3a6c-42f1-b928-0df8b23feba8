import 'package:eeh/loan_preClosure/model/submit_request_model.dart';

import '../../shared/network/api_helper.dart';
import '../../shared/network/api_utilities.dart';
import '../../shared/network/endpoints.dart';
import '../../shared/utility/secure_storage.dart';

abstract class ILoanPreClosurRepo {
  Future<NetworkResponse> fetchEmployeeInfo({
    required String employeeid,
  });
  Future<NetworkResponse> deleteAttachmentRequest(String recId);

  Future<NetworkResponse> submitRequest(  String loanId,String remainingAmount,String totalLoan,String paid,String loanPreclosurea ,String settlementDate);

}

class  LoanPreClosurRepo implements ILoanPreClosurRepo{
  @override
  Future<NetworkResponse> fetchEmployeeInfo({required String employeeid}) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'employeeid': employeeid},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
    headers: {
    "formName": "Employee Loan Info Form",
    "moduleName": "Employee Loan Info",
    "appKey": "LPC",
    "Content-Type": "application/json",
    });

    return response;

  }

  @override
  Future<NetworkResponse> submitRequest(  String loanId,String remainingAmount,String totalLoan,String paid,String loanPreclosurea ,String settlementDate)
     async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"loanid":loanId,
          "remainingamount":remainingAmount,
          "totalloan":totalLoan,
          "paid":paid,
          "loanpreclosurea":loanPreclosurea,
          "settlementdate":settlementDate,},
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          'appKey': 'LPC',
          'Content-Type': 'application/json'
        });
    return response;
  }
  @override
  Future<NetworkResponse> deleteAttachmentRequest(String attachId) async {
    Map<String, String> headers = {
      'TimeOffset': '10800000',
      'Accept': 'application/json, text/plain, */*',
      'Referer': 'https://projects.ntgapps.com/',
      'Content-Type': 'application/json;charset=UTF-8',
    };
    return ApiHelper().apiCall(
      "$deleteLoanPreCAttach/$attachId",
      requestType: RequestType.delete,
      sessionToken:
      await SecureStorageService().readSecureData(key: "user_session") ?? '',
      headers: headers,
    );
  }

}