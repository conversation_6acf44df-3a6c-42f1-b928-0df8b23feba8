import 'package:eeh/admin_service/components/date_component/widget/date_component.dart';
import 'package:eeh/admin_service/components/time_component/widget/time_component.dart';
import 'package:eeh/my_tasks_revamp/widgets/action_manger/actions.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:eeh/shared/widgets/toast.dart';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../../../../admin_service/utils/dynamic_screen_utils.dart';
import '../../../../admin_service/utils/field_type_constant.dart';
import '../../../../l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import '../../../../services_screen/widgets/elm_service_list_item.dart';
import '../../../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../../../shared/attachment_uda/widget/attachment_component.dart';
import '../../../../shared/utility/font_utility.dart';
import '../../../../shared/utility/navigation_utility.dart';
import '../../../../shared/widgets/custom_elevated_button.dart';
import '../../../../shared/work_widget/utility_widgets/withdraw_sheet.dart';

import '../../../bloc/my_task_bloc_revamp.dart';
import '../../../bloc/tasks_states.dart';
import '../../../bloc/tasks_events.dart';
import 'generic_action_bloc.dart';
import 'generic_action_events.dart';
import 'generic_action_states.dart';
import 'generic_popup_metaData_model.dart';

class GenericDoneAction extends IAdminAction {
  GenericActionBloc? genericActionBloc;
   GenericActionMetaData? genericActionMetaData;
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  bool isAllFieldsValid() {
    return true;
  }

  GenericDoneAction(super.actionData, super.context, super.workModel);



  @override
  mapTaskActionPayload() {
    super.mapTaskActionPayload();
    Navigation.popScreen(context);
  }

  @override
  onActionTapped() {
    AttachBloc.instance(context).files.clear();
    Color actionBtnColor = primaryColor.withOpacity(0.4);
    genericActionsSheet(
      context,
      // actionBtnText: 'Assign',
      isCancelable: false,
      actionBtnColor: actionBtnColor,
      shouldUseEndContentOnly: true,
      /*    headerTitle: title??'',
      headerDescription:subTitle??'',*/
      // headerIcon: FontAwesomeIcons.circleCheck,
      endContent: [
        MultiBlocProvider(
          providers: [
            BlocProvider<GenericActionBloc>(
              create: (context) =>
                  GenericActionBloc()..add(GenericActionInitEvent()),
            ),
            BlocProvider<AttachBloc>(
              create: (BuildContext context) {
                return AttachBloc()..add(AttachInitialEvent());
              },
            ),
          ],
          child: StatefulBuilder(
            builder: (context,setState) {
              return BlocConsumer<GenericActionBloc, GenericActionState>(
                  listener: (context, state) {
                if (state is GenericActionInitState) {
                  genericActionBloc?.add(GenericActionContentEvent(
                      actionRoute: actionData.actionRoute ?? '',
                      mutableContext: workModel.mutableContext ?? ''));
                }
                if (state is GenericActionContentSuccessState) {
                  genericActionMetaData = state.genericActionMetaData;
              }
            }, builder: (context, state) {
                genericActionBloc = context.read<GenericActionBloc>();
                 TasksBlocRevamp.instance(context).ctx = context;

                return state is GenericActionContentLoadingState
                  ? Center(
                      child: CircularProgressIndicator(),
                    )
                  : Form(
                      onChanged: () {
                      if (isAllFieldsValid()) {
                        actionBtnColor = primaryColor;
                      } else {
                        actionBtnColor = primaryColor.withOpacity(.40);
                      }
                      setState(() {});
                    },
                    key: formKey,
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 14.w, vertical: 2.h),
                          child: ListView.builder(
                            addAutomaticKeepAlives: true,
                            physics: const BouncingScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (context, index) =>
                               Padding(padding: EdgeInsets.only(bottom: 4.h),child:  buildWidget(index),),
                            itemCount: genericActionMetaData
                                ?.actionMetadata
                                ?.popupMetaData
                                ?.length ??
                                0,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            AppLocalizations.of(context)
                                .translate('Cancel'),
                            style: TextStyle(
                              color: rejectionColor,
                              fontSize: 18.sp,
                            ),
                          ),
                        ),
                          BlocConsumer<TasksBlocRevamp, TasksState>(
                                  listener: (context, state) {},
                                  builder: (context, state) {
                                    return Transform.translate(
                                      offset: const Offset(0, 4),
                                      child:
                                      BlocConsumer<GenericActionBloc, GenericActionState>(
                                          listener: (context, genericActionState) {

                                            if(genericActionState is SubmitGenericActionSuccessState||genericActionState is SubmitGenericActionDoneState ){

                                                TasksBlocRevamp.instance(context)
                                                    .add(UploadAttachmentFilesEvent(
                                                    requestId: int.tryParse(workModel.requestID) ??-1));
                                                SchedulerBinding.instance.addPostFrameCallback((_) {
                                                  mapTaskActionPayload();
                                                });
                                          /*      Future.delayed(Duration.zero, () {
                                                  mapTaskActionPayload();
                                                });*/

                                            }
                                          },
                                          builder: (context, genericActionState) {
                                          return CustomElevatedButton(
                                            fontSize: 18.sp,
                                            isLoading: state is TaskActionLoadingState||genericActionState is SubmitGenericActionLoadingState,
                                            backgroundColor:getActionBackgroundColor(),
                                            titleColor:getActionTextColor(),
                                            width: double.infinity,
                                            title: (AppLocalizations.appLang=="en"?actionData.labelEn:actionData.labelAr)??'',
                                            onPressed: () {
                                            _onActionPressed(state, genericActionState,context);
                                          },
                                        );
                                        }
                                      ),
                                    );
                                  }),
                      ],
                    ))
                    ;
              });
            }
          ),
        )
      ],
    );
  }
  _onActionPressed(state,genericActionState,context){
    if(state is !TaskActionLoadingState||genericActionState is !SubmitGenericActionLoadingState){
      if(isValidRequest(context)) {
        genericActionBloc?.add(SubmitGenericActionEvent(workModel.requestID));
      }else{
        showMessage(AppLocalizations.of(context).translate("missing_mandatoryField"), MessageType.error);
      }
    }
  }

  isValidRequest(context) {
    return shouldCheckAttachment()?(formKey.currentState!
        .validate() && AttachBloc.instance(context).files.isNotEmpty):formKey.currentState!
        .validate();
  }
  bool shouldCheckAttachment()=>(genericActionMetaData?.attachmentKey??'').isNotEmpty;
  Widget buildWidget(int index) {
    DynamicScreenUtils dynamicScreenUtils =DynamicScreenUtils();

    final item = genericActionMetaData?.actionMetadata?.popupMetaData?[index];
    switch (item?.filedType) {
      case FieldType.textfield:
        return dynamicScreenUtils.getTextField(item, context, (v) {

          genericActionBloc
              ?.add(UpdateInputFieldsEvent(value: v, serviceFieldItem: item));        });

      case FieldType.note:
        return dynamicScreenUtils.getNote(item, context, (v) {
          if((item?.parameterName??'')=="rejectionreason") {
            taskActionPayload.rejectionreason = v;
          }else{
            genericActionBloc?.updateFieldBlocData(item, v);
          }
        });

      case FieldType.numeric:
      case FieldType.number:
        return dynamicScreenUtils.getNumircTextField(item, context, (v) {
          genericActionBloc
              ?.add(UpdateInputFieldsEvent(value: v, serviceFieldItem: item));
        });

      case FieldType.dropDown:
        return dynamicScreenUtils.getDropDown(item,context, (selectedValue) {
          genericActionBloc?.updateDropDownBlocData(item, selectedValue);
        }, genericActionMetaData?.srvsId??'');

      case FieldType.attchment:
        return AttachUI(
          endPoint: "",
          isRequired: item?.mandatory??false,
          serviceType: genericActionMetaData?.attachmentKey??'',
          label:AppLocalizations.appLang=="en"?  item?.labelEn : item?.labelAr,
          onFilesUpdated: () {
           // _bloc.add(MazayaFilesUpdatedEvent());
          },
        );
      case FieldType.date:
         return DateTextField(
      labelText:AppLocalizations.appLang == 'en' ? item?.labelEn ??'' : item?.labelAr ??'',
      onChanged: (v) {genericActionBloc?.updateFieldBlocData(item, v);},
      isMandatory: item?.mandatory??false,
      validator: (v)=> genericActionBloc!.dateValidation(item,v,context),
    );

      case FieldType.time:
        return  TimeTextField(
      labelText:AppLocalizations.appLang == 'en' ? item?.labelEn ??'' : item?.labelAr ??'',
      onChanged:(v) {genericActionBloc?.updateFieldBlocData(item, v);},
      isMandatory: item?.mandatory??false,
      validator: (v)=> genericActionBloc!.timeValidation(item,v,context),
    );

     /* case FieldType.template:
        return dynamicScreenUtils.getAttachedFiles(
            item, widget.bloc.genericServiceDetailsResponse?.esmAttachments ?? []);

      case FieldType.instruction:
        return dynamicScreenUtils.getInstruction(item);

      
      case FieldType.terms:
        widget.bloc.isTermsMandatory = item?.mandatory??false;
        return dynamicScreenUtils.getTermsConditions(widget.bloc.termSwitchValue??false,
                (v) {widget.bloc.add(ChangeSwitchEvent(item: item, value: v));}, item);
    */

// case FieldType.toggle:
      // case FieldType.disclaimer:
      //   return dynamicScreenUtils
      //       .getSwitch(genericActionBloc?.switchValue ?? false, (v) {
      //     genericActionBloc?.add(ChangeSwitchEvent(item: item, value: v));
      //   }, item);

      case FieldType.icon:
        return (item?.mobileStyleEnabler?.iconClass??'').isNotEmpty?Padding(
          padding:  EdgeInsets.only(bottom: 4.0.h),
          child: Icon(
            getIconFromCss(item?.mobileStyleEnabler?.iconClass??''),
            size: 40.sp,
            color: getColorFromString(item?.mobileStyleEnabler?.iconColor??''),
          ),
        ):SizedBox.shrink();
      case FieldType.title:
        return Center(
          child: Text(
            (AppLocalizations.appLang == "en" ? item?.labelEn : item?.labelAr) ??
                '',
            style: FontUtilities.getTextStyle(TextType.medium,
                size: 16.sp,
                fontWeight: FontWeight
                    .bold),
          ),
        );
      case FieldType.subtitle:
        return Text(
          (AppLocalizations.appLang == "en" ? item?.labelEn : item?.labelAr) ??
              '',
          style: FontUtilities.getTextStyle(TextType.medium,
              size: 14.sp, fontWeight: FontWeight.normal),
          textAlign: TextAlign.center,
        );
      default:
        return SizedBox();
    }
  }
}
