import 'package:eeh/allowance_services/model/allawance_list_response_model.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/my_tasks_revamp/widgets/search_text_field.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/currency_field_component.dart';
import 'package:eeh/shared/widgets/important_note.dart';
import 'package:eeh/shared/widgets/new_custom_note_widget.dart';
import 'package:eeh/shared/widgets/new_textformfield_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class AllowanceUtils {
  Widget getBeneficialEmployeeWidget(
      {content, context, void Function(dynamic)? onSelect, controller}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: NewCustomTextFieldComponent(
        type: (content.isNotEmpty)
            ? TextFieldType.bottomSheet
            : TextFieldType.normal,
        controller: controller,
        labelText:
            AppLocalizations.of(context).translate('Beneficial Employee'),
        isMandatory: true,
        validationText: AppLocalizations.of(context)
            .translate("must select Beneficial Employee"),
        bottomSheetHeight: 0.5,
        bottomSheetText:
            AppLocalizations.of(context).translate('Beneficial Employee'),
        haveTrailing: true,
        trailingTitle: AppLocalizations.of(context).translate('name'),
        trailingSubTitle:
            AppLocalizations.of(context).translate('Eligible Amount'),
        bottomSheetContentList: content,
        onSelect: (value) {
          onSelect?.call(value);
        },
      ),
    );
  }

  getEligableAmountWidget(context, controller) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: CurrencyTextFieldComponent(
        controller: controller,
        isSARComponentHasBrackets: true,
        labelText: AppLocalizations.of(context).translate('Eligible Amount'),
        isMandatory: true,
        isDemmed: true,
        isReadOnly: true,
        validationText: AppLocalizations.of(context)
            .translate("must select Eligable Amount"),
      ),
    );
  }

  Widget getNumberOfMonthsWidget(
      {context,
      void Function(dynamic)? onSelect,
      controller,
      String? label,
      String? title}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 8.0),
      child: NewCustomTextFieldComponent(
        type: (numberOfMonths.isNotEmpty)
            ? TextFieldType.bottomSheet
            : TextFieldType.normal,
        controller: controller,
        labelText:
            label ?? AppLocalizations.of(context).translate('Number Of Months'),
        isMandatory: true,
        validationText:
            ((label?.isNotEmpty ?? false) && (title?.isNotEmpty ?? false))
                ? AppLocalizations.of(context)
                    .translate("must select Extended Months")
                : AppLocalizations.of(context)
                    .translate("must select Number Of Months"),
        bottomSheetHeight: 0.5,
        bottomSheetText:
            title ?? AppLocalizations.of(context).translate('Number Of Months'),
        bottomSheetContentList: numberOfMonths,
        onSelect: (value) {
          onSelect?.call(value);
        },
      ),
    );
  }

  Widget getStartDate(
      {context,
      textController,
      dateController,
      dynamic Function()? onDateChange}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: NewCustomTextFieldComponent(
        validationText:
            AppLocalizations.of(context).translate("must select date"),
        type: TextFieldType.calendar,
        labelText: AppLocalizations.of(context).translate('Start Date'),
        isMandatory: true,
        controller: textController,
        minTime: DateTime.now(),
        isWeekendEnable: true,
        onDateChange: () {
          onDateChange?.call();
        },
        dateRangePickerController: dateController,
      ),
    );
  }

  Widget getEndDate(
      {context,
      textController,
      dateController,
      dynamic Function()? onDateChange}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: NewCustomTextFieldComponent(
        validationText:
            AppLocalizations.of(context).translate("must select date"),
        type: TextFieldType.calendar,
        labelText: AppLocalizations.of(context).translate('End Date'),
        isMandatory: true,
        controller: textController,
        isDemmed: true,
        isReadOnly: true,
        onDateChange: () {
          onDateChange?.call();
        },
        dateRangePickerController: dateController,
      ),
    );
  }

  Widget getCommentsWidget(context, controller) {
    return Padding(
      padding: const EdgeInsets.only(
          bottom: 0.0, left: 15.0, right: 15.0, top: 15.0),
      child: CustomNoteWidget(
        hintText: AppLocalizations.of(context).translate("Write a Comments"),
        labelText: AppLocalizations.of(context).translate("Comments:"),
        noteController: controller,
      ),
    );
  }

  Widget getImportantNotes(context) {
    return Padding(
      padding: const EdgeInsets.only(
          bottom: 0.0, left: 15.0, right: 15.0, top: 15.0),
      child: ImportantNote(
        title: AppLocalizations.of(context).translate("Important Note:"),
        notes: [
          AppLocalizations.of(context).translate(
              "Start Date will default to the request creation date."),
          AppLocalizations.of(context).translate(
              'If the request is submitted on or before the 15th, the eligible amount will be applied starting this month otherwise will be next month.'),
        ],
      ),
    );
  }

  getEmplyeeAllowanceNumber(context, number) {
    return Padding(
      padding: const EdgeInsets.only(
          bottom: 0.0, left: 15.0, right: 15.0, top: 15.0),
      child: Container(
        padding: EdgeInsets.all(8.sp),
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.black.withOpacity(0.1), width: 1.sp),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            AppLocalizations.of(context).translate('Employee Allowances'),
            style: FontUtilities.getTextStyle(
              TextType.medium,
            ),
          ),
          Container(
              padding: EdgeInsets.all(8.sp),
              color: borderLightColor.withOpacity(0.1),
              child: Text(
                  '${AppLocalizations.of(context).translate('Number of Employees :')} $number',
                  style: FontUtilities.getTextStyle(TextType.regular,
                      size: 14.sp))),
        ]),
      ),
    );
  }

  searchTextField(
      controller, context, dynamic Function(String)? onTextChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: SearchBarTextField(
        onChanged: () {},
        controller: controller,
        haveIconButton: true,
        hint: (AppLocalizations.appLang == 'en' ? 'Search By Employee Name': "البحث باسم الموظف" ),
        onTextChanged: (v) {
          onTextChanged?.call(v);
        },
      ),
    );
  }

  emptyView(bool isVisible, context, bool isFilter,) {
    return Visibility(
      visible: isVisible,
      child: Padding(
        padding: EdgeInsets.all(25.sp),
        child: Center(
          child: Column(
            spacing: 8.h,
            children: [
              Container(
                width: 50.w,
                height: 50.h,
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: primaryColor.withOpacity(0.2)),
                child: Icon(FontAwesomeIcons.lightPeopleGroup,size: 25.sp,color: Colors.white,),
              ),
              Text(
                isFilter
                    ? (AppLocalizations.appLang == 'en' ? 'No employees were found !' : "لم يتم العثور على أي موظفين!")
                    : (AppLocalizations.appLang == 'en' ? 'You don’t have any employees yet' : "ليس لديك أي موظفين حتى الآن"),
                style: FontUtilities.getTextStyle(TextType.regular,size: 14.sp),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<NumOfMonths> numberOfMonths = [
    NumOfMonths(nameEn: '1Months', nameAr: '1 شهر'),
    NumOfMonths(nameEn: '2Months', nameAr: '2 شهور'),
    NumOfMonths(nameEn: '3Months', nameAr: '3 شهور'),
    NumOfMonths(nameEn: '4Months', nameAr: '4 شهور'),
    NumOfMonths(nameEn: '5Months', nameAr: '5 شهور'),
    NumOfMonths(nameEn: '6Months', nameAr: '6 شهور'),
  ];
}
