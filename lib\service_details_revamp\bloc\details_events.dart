import 'package:eeh/shared/work_widget/work_item_layout.dart';

abstract class DetailsEvent {}

class DetailsInitialEvent extends DetailsEvent {}

class LoadDetailsEvent extends DetailsEvent {
  final String mutableContext;
  String? requestType;
  String? requestId;
  String? taskId;
  String? taskTableName;
  bool isNotification = false;
  WorkType workType = WorkType.requestDetails;
  LoadDetailsEvent(this.mutableContext,
      {this.requestType,
      this.requestId,
      this.taskId,
      this.taskTableName,
      this.isNotification = false,
      this.workType = WorkType.requestDetails});
}

class PreviewAttachEvent extends DetailsEvent {
  final String url;
  final String name;
  String attachmentId;
  String udaId;
  PreviewAttachEvent(this.url, this.name, this.attachmentId, this.udaId);
}

class ChangeDelegateEvent extends DetailsEvent {
  final int delegateIndex;
  ChangeDelegateEvent(this.delegateIndex);
}

class SearchDelegateEvent extends DetailsEvent {
  final String query;
  SearchDelegateEvent(this.query);
}

class LoadServiceTimeLineEvent extends DetailsEvent {
  final String mutableContext;
  LoadServiceTimeLineEvent({
    required this.mutableContext,
  });
}


class RefreshDetailsEvent extends DetailsEvent {}
/*class UpdateMutableContextEvent extends DetailsEvent {
  final String mutableContext;
  UpdateMutableContextEvent(this.mutableContext);
}*/

class RetrieveTimeSheetEvent extends DetailsEvent{
  String weekId;
  RetrieveTimeSheetEvent({required this.weekId});
}

class GetProjectHoursRecordsEvent extends DetailsEvent {
  String weekId;
  GetProjectHoursRecordsEvent({required this.weekId});
}
