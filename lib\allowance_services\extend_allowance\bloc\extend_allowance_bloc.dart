import 'package:eeh/allowance_services/extend_allowance/bloc/extend_allowance_event.dart';
import 'package:eeh/allowance_services/extend_allowance/bloc/extend_allowance_state.dart';
import 'package:eeh/allowance_services/model/allawance_list_response_model.dart';
import 'package:eeh/allowance_services/model/submit_allowance_request_body.dart';
import 'package:eeh/allowance_services/model/submit_allowance_response_model.dart';
import 'package:eeh/allowance_services/repo/allowance_repo.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class ExtendAllowanceBloc
    extends Bloc<ExtendAllowanceEvent, ExtendAllowanceState> {
  late AllowanceRepo repo;
  final TextEditingController numberOfMonthsController =
      TextEditingController();
  final TextEditingController commentController = TextEditingController();
  final TextEditingController searchController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  AllowanceListResponse? allowanceListResponse;
  List<Employee> extendedList = [];
  List<Employee> filterList = [];
  bool isGas = false;
  String extendedEndDate = '';
  SubmitAllowanceResponse? submitResponse;
  ExtendAllowanceBloc() : super(ExtendAllowanceInitialState()) {
    on<ExtendAllowanceInitialEvent>((event, emit) {
      repo = AllowanceRepo(isGas: event.isGas);
      isGas = event.isGas;
      emit(ExtendAllowanceInitialState());
    });

    on<NumberOfMonthsValueChangelEvent>((event, emit) {
      numberOfMonthsController.text = AppLocalizations.appLang == 'en'
          ? event.value.nameEn
          : event.value.nameAr;
      emit(NumberOfMonthsValueChangelState());
    });

    on<SavedButtonActionEvent>((event, emit) {
      onSaveonPressed(event.context, event.index);
      clearForm();
      emit(SavedButtonActionState());
    });

    on<EmployeeAllowanceEvent>((event, emit) async {
      emit(EmployeeAllowanceLoadingState());
      await fetchEmployeeAllowance(emit);
    });

    on<SubmitAllowanceEvent>((event, emit) async {
      emit(SubmitAllowanceLoadingState());
      await submitAllowance(emit);
    });

    on<SearchEmployeeEvent>((event, emit) {
      searchEmployee(event.value);
      emit(SearchEmployeeState());
    });
  }

  onSaveonPressed(context, int index) {
    final item =filterList[index];
    // final emptyItem = Employee();
    if (formKey.currentState!.validate()) {
      item.extendedMonths = numberOfMonthsController.text;
      item.comment = commentController.text;
      addOrUpdateEmployee(item);
      Navigator.pop(context);
    }
  }

  void addOrUpdateEmployee(Employee newEmp) {
    int index = extendedList.indexWhere((e) => e.recid == newEmp.recid);
    if (index != -1) {
      extendedList[index] = newEmp;
    } else {
      extendedList.add(newEmp);
    }
  }

  clearForm() {
    numberOfMonthsController.clear();
    commentController.clear();
  }

  fetchEmployeeAllowance(emitter) async {
    await repo
        .fetchAllownceList(requestType: 'extend')
        .then((response) => onFetchEmployeeAllowance(response, emitter));
  }

  onFetchEmployeeAllowance(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchEmployeeAllowanceSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(EmployeeAllowanceErrorState());
    });
  }

  onFetchEmployeeAllowanceSuccess(data, emitter) {
    allowanceListResponse = AllowanceListResponse.fromJson(data);
    filterList = allowanceListResponse?.employees ?? [];
    emitter(EmployeeAllowanceSuccessState());
  }

  submitAllowance(emitter) async {
    await repo
        .supmitAllowance(
            employeesAllowa: getRequestBody(), appKey: isGas ? 'EGA' : 'ECA')
        .then((response) => onSubmitAllowance(response, emitter));
  }

  onSubmitAllowance(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitAllowanceSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitAllowanceErrorState());
    });
  }

  onSubmitAllowanceSuccess(data, emitter) {
    submitResponse = SubmitAllowanceResponse.fromJson(data);
    emitter(SubmitAllowanceSuccessState());
  }

  getRequestBody() {
    List<Employees> body = extendedList.map((emp) {
      return Employees(
        employeeId: getNumofMon(emp.employeeid ?? ''),
        employeeNameAr: emp.employeenamear,
        employeeNameEn: emp.employeenameen,
        startDate: emp.enddate,
        endDate: calculateEndDate(emp.enddate ?? '', emp.extendedMonths ?? ''),
        eligibleAmount: emp.eligibleamount,
        numberOfMonths: getNumofMon(emp.extendedMonths ?? ''),
        comments: emp.comment,
      );
    }).toList();
    return SupmitAllowanceBody(
        employeesallowa: body, numOfEmps: extendedList.length.toString());
  }

  getNumofMon(String num) {
    final match = RegExp(r'\d+').firstMatch(num);
    int? numofMon = match != null ? int.tryParse(match.group(0)!) : null;
    return numofMon;
  }

  calculateEndDate(String startdate, String numofMon) {
    DateTime startDate = DateTime.parse(startdate);
    int numberOfMonths =
        int.tryParse((RegExp(r'\d+').firstMatch(numofMon)?.group(0)) ?? '') ??
            0;
    DateTime endDate = DateTime(
        startDate.year, startDate.month + numberOfMonths, startDate.day);
    extendedEndDate = DateFormat('yyyy-MM-dd').format(endDate);
    return extendedEndDate;
  }

  searchEmployee(String v) {
    if (v.isEmpty) {
      filterList = allowanceListResponse?.employees ?? [];
    } else {
      filterList = [];
      for (var item in allowanceListResponse?.employees ?? []) {
        if ((AppLocalizations.appLang == 'en'
                ? item.employeenameen
                : item.employeenamear)
            .toString()
            .toLowerCase()
            .contains(v.toLowerCase())) {
          filterList.add(item);
        }
      }
    }
  }

}
