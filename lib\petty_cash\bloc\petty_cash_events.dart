import 'package:flutter/material.dart';

abstract class PettyCashRequestEvent {}

class PettyCashInitialEvent extends PettyCashRequestEvent {}

class ChangeBudgetTypeEvent extends PettyCashRequestEvent {}

class ProjectWbsListEvent extends PettyCashRequestEvent {}

class ChangeValidatorValueEvent extends PettyCashRequestEvent {
  bool value;
  ChangeValidatorValueEvent({required this.value});
}

class UpdatefieldValueEvent extends PettyCashRequestEvent {}

class UploadAttachmentFilesEvent extends PettyCashRequestEvent {
  BuildContext context;
  UploadAttachmentFilesEvent(this.context);
}

class SubmitRequestEvent extends PettyCashRequestEvent {}

class FilesUpdatedEvent extends PettyCashRequestEvent {}
