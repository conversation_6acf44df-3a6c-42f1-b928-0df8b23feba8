import '../../shared/widgets/general_text_form_field.dart';

class CustomerNameResponse {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final String employeeId;
  final String authKey;
  final List<CustomerInfo> customers;

  CustomerNameResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeId,
    required this.authKey,
    required this.customers,
  });

  factory CustomerNameResponse.fromJson(Map<String, dynamic> json) {
    return CustomerNameResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      employeeId: json['employeeid'],
      authKey: json['authkey'],
      customers: (json['customerinforma'] as List)
          .map((e) => CustomerInfo.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'employeeid': employeeId,
      'authkey': authKey,
      'customerinforma': customers.map((e) => e.toJson()).toList(),
    };
  }
}

class CustomerInfo extends GeneralSheetContent {
  final String nameAr;
  final String moiId;
  final String id;
  final String nameEn;
  final int recId;

  CustomerInfo({
    required this.nameAr,
    required this.moiId,
    required this.id,
    required this.nameEn,
    required this.recId,
  }) {
    // Initialize GeneralSheetContent fields
    nameen = nameEn;
    extraContent = [];
    // extraContent = [
    extraContent?.add(SheetExtraContent(
        keyEn: 'Name ',
        keyAR: 'الاسم',
        valueEn: nameEn,
        valueAR: nameAr));
    extraContent?.add(SheetExtraContent(
        keyEn: 'EmployeeId',
        keyAR: 'EmployeeId',
        valueEn: id,
        valueAR: id));
  }

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      nameAr: json['namear'],
      moiId: json['moiid'],
      id: json['id'],
      nameEn: json['nameen'],
      recId: json['recid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'namear': nameAr,
      'moiid': moiId,
      'id': id,
      'nameen': nameEn,
      'recid': recId,
    };
  }
}
