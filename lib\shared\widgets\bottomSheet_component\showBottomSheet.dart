import 'package:flutter/material.dart';

import 'bottomSheet_contant.dart';

void showBottomSheets({
  required BuildContext context,
  required String exitText,
  required String submitText,
   String? subText,
  required String headerText,
  required Function() onTap,
  required bool enableDrag,
  required bool isDismissible,
  required bool isScrollControlled,
  final Function()? tapsheet,
  bool showSubmitButton = true,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: isScrollControlled, // This allows full-height bottom sheet
    isDismissible: isDismissible,
    enableDrag:enableDrag ,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
    ),
    backgroundColor: Colors.white,
    builder: (BuildContext context) {
      ModalRoute.of(context)?.addScopedWillPopCallback(() async => false);
      return BottomsheetContant(
        exitText: exitText,
        submitText: submitText,
        subText: subText,
        headerText: headerText,
        showSubmitButton: showSubmitButton,
        onTap: onTap,
      );
    },
  );
}
