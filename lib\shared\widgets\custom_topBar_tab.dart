import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTopBarTab extends StatelessWidget {
  const CustomTopBarTab({
    super.key,
    required this.title,
  });
  final String title;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Tab(
        child: Center(
          child: Text(
            textAlign: TextAlign.center,
             style: TextStyle(
               fontSize: 14.sp,
               fontFamily: appFontFamily
             ),
            maxLines: 2,
            title,
          ),
        ),
      ),
    );
  }
}
