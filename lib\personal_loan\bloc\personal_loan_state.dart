part of 'personal_loan_bloc.dart';

sealed class PersonalLoanState extends Equatable {
  const PersonalLoanState();

  @override
  List<Object> get props => [];
}

final class PersonalLoanInitial extends PersonalLoanState {}

final class PersonalLoanInitialFinishState extends PersonalLoanState {}

final class AcceptTermsAndConditionsInitialState extends PersonalLoanState {}

final class AcceptTermsAndConditionsChangeState extends PersonalLoanState {}

final class GetPersonalLoanInitialDataLoadingState extends PersonalLoanState {}

final class GetPersonalLoanInitialDataSuccessState extends PersonalLoanState {
  final EmployeeLoanInfoResponseModel employeeLoanInfoResponseModel;
  const GetPersonalLoanInitialDataSuccessState(
      {required this.employeeLoanInfoResponseModel});
}

final class GetPersonalLoanInitialDataErrorState extends PersonalLoanState {}

final class SubmitPersonalLoanLoadingState extends PersonalLoanState {}

final class SubmitPersonalLoanSuccessState extends PersonalLoanState {
  final SubmitPersonalLoanResponseModel submitPersonalLoanResponseModel;

  const SubmitPersonalLoanSuccessState(
      {required this.submitPersonalLoanResponseModel});

  @override
  List<Object> get props => [submitPersonalLoanResponseModel];
}

final class SubmitPersonalLoanErrorState extends PersonalLoanState {}
