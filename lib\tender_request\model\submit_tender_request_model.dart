class SubmitTenderRequestModel {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final Map<String, dynamic>? meetingParameters;
  final String? paymentTypeAr;
  final String? exception;
  final String? employeeId;
  final String? resultMessageEn;
  final String? bankNameAr;
  final String? rejectedByAr;
  final List<dynamic>? attachments;
  final String? projectNameAr;
  final String? resultMessage;
  final String? customerNameE;
  final String? requestTypeAr;
  final String? costCenterAr;
  final String? sadadNumber;
  final String? authKey;
  final String? ibanNumber;
  final String? pendingRequest;
  final List<ApprovalProcess>? approvalProce;
  final List<dynamic>? nextApprovalLis;
  final String? resultMessageAr;
  final String? tenderNumber;
  final String? notes;
  final String? requestStatus;
  final String? approvedByAr;
  final String? rejectionReason;
  final String? paymentTypeId;
  final String? customerNameA;
  final String? budgetTypeEn;
  final String? bankNameEn;
  final String? rejectedByEn;
  final String? comments;
  final String? requesterEmail;
  final String? requesterNameAr;
  final String? withdrawFlag;
  final String? elmWebUrl;
  final String? serviceKey;
  final String? createddate;
  final String? requesterNameEn;
  final String? approvedByEn;
  final String? sapAccountDoc;
  final String? tableName;
  final String? userId;
  final String? requestType;
  final String? withdrawReason;
  final String? customerId;
  final String? bankNameId;
  final String? projectNameEn;
  final String? budgetTypeAr;
  final String? costCenterEn;
  final String? code;
  final String? paymentTypeEn;
  final String? displayRecId;
  final String? projectNameId;
  final String? taskWebUrl;
  final String? reqStatusAr;
  final String? requestDetails;
  final String? beneficiaryName;
  final String? reqCardData;
  final String? tenderName;
  final String? costCenterId;
  final String? myTypeId;
  final String? tenderValue;

  SubmitTenderRequestModel({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.meetingParameters,
    this.paymentTypeAr,
    this.exception,
    this.employeeId,
    this.resultMessageEn,
    this.bankNameAr,
    this.rejectedByAr,
    this.attachments,
    this.projectNameAr,
    this.resultMessage,
    this.customerNameE,
    this.requestTypeAr,
    this.costCenterAr,
    this.sadadNumber,
    this.authKey,
    this.ibanNumber,
    this.pendingRequest,
    this.approvalProce,
    this.nextApprovalLis,
    this.resultMessageAr,
    this.tenderNumber,
    this.notes,
    this.requestStatus,
    this.approvedByAr,
    this.rejectionReason,
    this.paymentTypeId,
    this.customerNameA,
    this.budgetTypeEn,
    this.bankNameEn,
    this.rejectedByEn,
    this.comments,
    this.requesterEmail,
    this.requesterNameAr,
    this.withdrawFlag,
    this.elmWebUrl,
    this.serviceKey,
    this.createddate,
    this.requesterNameEn,
    this.approvedByEn,
    this.sapAccountDoc,
    this.tableName,
    this.userId,
    this.requestType,
    this.withdrawReason,
    this.customerId,
    this.bankNameId,
    this.projectNameEn,
    this.budgetTypeAr,
    this.costCenterEn,
    this.code,
    this.paymentTypeEn,
    this.displayRecId,
    this.projectNameId,
    this.taskWebUrl,
    this.reqStatusAr,
    this.requestDetails,
    this.beneficiaryName,
    this.reqCardData,
    this.tenderName,
    this.costCenterId,
    this.myTypeId,
    this.tenderValue,
  });

  factory SubmitTenderRequestModel.fromJson(Map<String, dynamic> json) {
    return SubmitTenderRequestModel(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      meetingParameters: json['meetingParameters'],
      paymentTypeAr: json['payment_type_ar'],
      exception: json['exception'],
      employeeId: json['employeeid'],
      resultMessageEn: json['resultmessageen'],
      bankNameAr: json['bank_name_ar'],
      rejectedByAr: json['rejectedbyar'],
      attachments: json['attachments'],
      projectNameAr: json['project_name_ar'],
      resultMessage: json['resultmessage'],
      customerNameE: json['customer_name_e'],
      requestTypeAr: json['request_type_ar'],
      costCenterAr: json['cost_center_ar'],
      sadadNumber: json['sadadnumber'],
      authKey: json['authkey'],
      ibanNumber: json['ibannumber'],
      pendingRequest: json['pending_request'],
      nextApprovalLis: json['nextapprovallis'],
      approvalProce: (json['approvalproce'] as List?)
          ?.map((e) => ApprovalProcess.fromJson(e))
          .toList(),
      resultMessageAr: json['resultmessagear'],
      tenderNumber: json['tendernumber'],
      notes: json['notes'],
      requestStatus: json['requeststatus'],
      approvedByAr: json['approved_by_ar'],
      rejectionReason: json['rejectionreason'],
      paymentTypeId: json['paymenttype_id'],
      customerNameA: json['customer_name_a'],
      budgetTypeEn: json['budget_type_en'],
      bankNameEn: json['bank_name_en'],
      rejectedByEn: json['rejectedbyen'],
      comments: json['comments'],
      requesterEmail: json['requesteremail'],
      requesterNameAr: json['requesternamear'],
      withdrawFlag: json['withdrawflag'],
      elmWebUrl: json['elm_web_url'],
      serviceKey: json['service_key'],
      createddate: json['createddate'],
      requesterNameEn: json['requesternameen'],
      approvedByEn: json['approved_by_en'],
      sapAccountDoc: json['sap_account_doc'],
      tableName: json['table_name'],
      userId: json['userid'],
      requestType: json['request_type'],
      withdrawReason: json['withdrawreason'],
      customerId: json['customer_id'],
      bankNameId: json['bank_name_id'],
      projectNameEn: json['project_name_en'],
      budgetTypeAr: json['budget_type_ar'],
      costCenterEn: json['cost_center_en'],
      code: json['code'],
      paymentTypeEn: json['payment_type_en'],
      displayRecId: json['display_recid'],
      projectNameId: json['project_name_id'],
      taskWebUrl: json['task_web_url'],
      reqStatusAr: json['req_status_ar'],
      requestDetails: json['request_details'],
      beneficiaryName: json['beneficiaryname'],
      reqCardData: json['req_card_data'],
      tenderName: json['tendername'],
      costCenterId: json['cost_center_id'],
      myTypeId: json['mytypeid'],
      tenderValue: json['tendervalue'],
    );
  }
}

class ApprovalProcess {
  final Template? template;
  final int? taskSeq;
  final String? taskTypeName;
  final String? plannedEndDate;
  final int? duration;
  final int? taskTypeId;
  final int? urgency;
  final int? processId;
  final String? processName;
  final String? plannedStartDate;
  final int? plannedEfforts;
  final String? taskName;
  final int? recId;

  ApprovalProcess({
    this.template,
    this.taskSeq,
    this.taskTypeName,
    this.plannedEndDate,
    this.duration,
    this.taskTypeId,
    this.urgency,
    this.processId,
    this.processName,
    this.plannedStartDate,
    this.plannedEfforts,
    this.taskName,
    this.recId,
  });

  factory ApprovalProcess.fromJson(Map<String, dynamic> json) {
    return ApprovalProcess(
      template: json['template'] != null
          ? Template.fromJson(json['template'])
          : null,
      taskSeq: json['taskSeq'],
      taskTypeName: json['taskTypeName'],
      plannedEndDate: json['plannedEndDate'],
      duration: json['duration'],
      taskTypeId: json['taskTypeId'],
      urgency: json['urgency'],
      processId: json['processId'],
      processName: json['processName'],
      plannedStartDate: json['plannedStartDate'],
      plannedEfforts: json['plannedEfforts'],
      taskName: json['taskName'],
      recId: json['recId'],
    );
  }
}

class Template {
  final int? createdById;
  final String? createdBy;
  final int? createdDate;
  final String? companyName;
  final String? uuid;
  final int? recId;
  final String? templateName;
  final int? typeId;

  Template({
    this.createdById,
    this.createdBy,
    this.createdDate,
    this.companyName,
    this.uuid,
    this.recId,
    this.templateName,
    this.typeId,
  });

  factory Template.fromJson(Map<String, dynamic> json) {
    return Template(
      createdById: json['createdById'],
      createdBy: json['createdBy'],
      createdDate: json['createdDate'],
      companyName: json['companyName'],
      uuid: json['uuid'],
      recId: json['recId'],
      templateName: json['templateName'],
      typeId: json['typeId'],
    );
  }
}
