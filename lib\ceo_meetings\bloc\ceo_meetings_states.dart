import 'package:eeh/ceo_meetings/model/employee_model.dart';
import 'package:eeh/shared/utility/methods.dart';

import '../model/CEOMembers.dart';

abstract class CEOMeetingsState {}

class CEOMeetingsInitialState extends CEOMeetingsState {}

class CEOMeetingsInitialFinishState extends CEOMeetingsState {}

class ChangeFavoriteStatusState extends CEOMeetingsState {}

class SubmitRequestLoadingState extends CEOMeetingsState {}

class SubmitRequestSuccessState extends CEOMeetingsState {}

class SubmitRequestErrorState extends CEOMeetingsState {
  SubmitRequestErrorState() {
    performFailureVibration();
  }
}

class ChangeMandatoryFieldValueState extends CEOM<PERSON>tingsInitialState {}

class GetElmCEOMembersRequestLoadingState extends CEOMeetingsState {}

class GetElmCEOMembersRequestSuccessState extends CEOMeetingsState {
  CEOM<PERSON>bers ceoRetrieveData;

  GetElmCEOMembersRequestSuccessState({required this.ceoRetrieveData});
}

class GetElmCEOMembersRequestErrorState extends CEO<PERSON>eetingsState {}

class GetDropdownListDataRequestLoadingState extends CEOMee<PERSON>sState {}

class GetDropdownListDataRequestSuccessState extends CEOMeetingsState {}

class GetDropdownListDataRequestErrorState extends CEOMeetingsState {}

class UpdateCEOGroupValueState extends CEOMeetingsState {}

class GetEmployeesListLoadingState extends CEOMeetingsState {}

class GetEmployeesListSuccessState extends CEOMeetingsState {
  EmployeeResponse employeeResponse;
  GetEmployeesListSuccessState({required this.employeeResponse});
}

class GetEmployeesListErrorState extends CEOMeetingsState {}

class GetEmployeesListNextPageLoadingState extends CEOMeetingsState {}

class ChangeFieldValueState extends CEOMeetingsState {}

class MoveToScreenState extends CEOMeetingsState {}

class ChangeSearchTextFieldTypeState extends CEOMeetingsState {}

class UploadAttachmentLoadingState extends CEOMeetingsState {}

class UploadAttachmentFilesSuccessState extends CEOMeetingsState {}

class ChangeSelectedDateTextFieldTypeState extends CEOMeetingsState {}

class ChangeFromTimeTextFieldTypeState extends CEOMeetingsState {}
