import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/petty_cash/model/petty_cash_request_response.dart';
import 'package:eeh/shared/widgets/success_screen_list_item.dart';
import 'package:flutter/material.dart';

import '../../../success_view/success_screen_view.dart';

class PettyCashSuccessScreenContentWidget extends StatelessWidget {
  final PettyCashRequest? requestData;
  const PettyCashSuccessScreenContentWidget(
      {super.key, required this.requestData});

  @override
  Widget build(BuildContext context) {
    return requestData == null
        ? SizedBox.shrink()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Request Type"),
                value: AppLocalizations.appLang=="en"?requestData?.requestType.toString() ?? '':requestData?.requestTypeAr.toString() ?? '',
              ), SuccessScreenListItem(
                title: AppLocalizations.of(context).translate('Request ID'),
                value: requestData?.displayRecId.toString() ?? '',
              ),
              SuccessScreenListItem(
                  title: AppLocalizations.of(context)
                      .translate('petty_cash_usage'),
                  value: AppLocalizations.appLang == 'en'
                      ? requestData?.cashUsageNameEn ?? ''
                      : requestData?.cashUsageNameAr ?? ''),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("budget_type"),
                value: AppLocalizations.appLang == 'en' ? 
                requestData?.budgetType ?? ''
                    : requestData?.budgetTypeAr ?? '',
              ),
              Visibility(
                visible: requestData?.projectId != null,
                child: SuccessScreenListItem(
                    title:
                        AppLocalizations.of(context).translate("Project Name"),
                    value: AppLocalizations.appLang == 'en'
                        ? requestData?.projectNameEn ?? ''
                        : requestData?.projectNameAr ?? ''),
              ),
              buildHeaderText(AppLocalizations.of(context).translate("cash_amount")),
              buildCurrencyDetailsText(
                  requestData?.cashAmount ?? ''),
              buildDivider(),
              /* SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("cash_amount"),
                value: requestData?.cashAmount ?? '',
              ),*/
              SuccessScreenListItem(
                title: AppLocalizations.of(context)
                    .translate("expected_closing_date"),
                value: requestData?.closedDate ?? '',
              ),
              SuccessScreenListItem(
                title: AppLocalizations.of(context).translate("Purpose"),
                value: requestData?.purpose ?? '',
              ),
            ],
          );
  }
}
