import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/style.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/utility/methods.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TimeTextField extends StatefulWidget {
  final String labelText;
  final Function(String)? onChanged;
  final bool isMandatory;
  final FormFieldValidator? validator;
  const TimeTextField(
      {super.key,
      required this.labelText,
      this.onChanged,
      this.validator,
      this.isMandatory = false});
  @override
  TimePickerTextFieldState createState() => TimePickerTextFieldState();
}

class TimePickerTextFieldState extends State<TimeTextField>
    with AutomaticKeepAliveClientMixin {
  TimeOfDay? fromSelectedTime;
  TextEditingController timeController = TextEditingController();

  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return TextFormField(
      controller: timeController,
      onChanged: widget.onChanged,
      style: FontUtilities.getTextStyle(TextType.regular,
          size: 12.sp, fontWeight: FontWeight.w400),
      decoration: InputDecoration(
        enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Color(0xff949494))),
        label: Text.rich(TextSpan(
          children: [
            TextSpan(
              text: widget.labelText,
              style: FontUtilities.getTextStyle(TextType.medium,
                  size: 12.sp, fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: widget.isMandatory ? ' *' : '',
              style: mandatoryStyle,
            ),
          ],
        )),
        suffixIcon: Container(
            width: 4.w,
            alignment: AppLocalizations.appLang == 'en'
                ? Alignment.bottomRight
                : Alignment.bottomLeft,
            padding: EdgeInsets.only(bottom: 4.w),
            margin: EdgeInsets.zero,
            child: Icon(
              Icons.keyboard_arrow_down,
              size: 22.sp,
            )),
      ),
      readOnly: true,
      onTap: () => onTapFunction(context: context),
      validator: widget.validator,
    );
  }

  onTapFunction({required BuildContext context}) async {
    showTimePickerBottomSheet(
      context: context,
      initialTime: fromSelectedTime,
      onTimeSelected: (selectedTime) {
        if (selectedTime != fromSelectedTime) {
          timeController.text = formatTime24Hour(selectedTime);
          fromSelectedTime = selectedTime;
          widget.onChanged!(timeController.text);
        }
      },
    );
  }

  String formatTime24Hour(TimeOfDay time) {
    final String hour = time.hour.toString().padLeft(2, '0');
    final String minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
