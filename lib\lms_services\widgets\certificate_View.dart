import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class WebViewPDFViewer extends StatefulWidget {
  final String? certificateId;
  
  const WebViewPDFViewer({Key? key, this.certificateId}) : super(key: key);
  
  @override
  _WebViewPDFViewerState createState() => _WebViewPDFViewerState();
}

class _WebViewPDFViewerState extends State<WebViewPDFViewer> {
  InAppWebViewController? webViewController;
  bool isLoading = true;
  bool isDownloading = false;
  double progress = 0;
  String? errorMessage;
  final Dio _dio = Dio();

  @override
  void initState() {
    super.initState();
    debugPrint('Initializing WebViewPDFViewer with certificate ID: ${widget.certificateId}');
  }

  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      return true; // iOS doesn't need explicit permission for downloads
    }
    return false;
  }

  Future<void> _downloadCertificate() async {
    if (!(await _requestPermissions())) {
      showMessage(
        AppLocalizations.of(context).translate('Storage permission denied'),
        MessageType.error,
      );
      return;
    }

    setState(() {
      isDownloading = true;
    });

    try {
      // Get certificate ID
      final String certificateId = widget.certificateId ?? '1cb2f40e-30ff-4855-8600-1f086fd0b7e4';
      final String encodedId = Uri.encodeComponent(certificateId);
      final String url = 'https://api.365.systems/odata/v2/Certificates($encodedId)/Content';
      
      // Auth credentials
      const String username = 'elme_full';
      const String password = 'f414bbe5-88c6-4ee1-939c-4813cbb7e0a3';
      String basicAuth = base64Encode(utf8.encode('$username:$password'));

      // Get download directory
      final directory = Platform.isAndroid 
          ? await getExternalStorageDirectory() 
          : await getApplicationDocumentsDirectory();
      
      if (directory == null) {
        throw Exception('Could not access storage directory');
      }

      final fileName = 'certificate_${certificateId.substring(0, 8)}.pdf';
      final filePath = '${directory.path}/$fileName';

      // Download file
      await _dio.download(
        url,
        filePath,
        options: Options(
          headers: {
            'Authorization': 'Basic $basicAuth',
          },
        ),
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              progress = received / total;
            });
          }
        },
      );

      showMessage(
        AppLocalizations.of(context).translate('Certificate downloaded successfully'),
        MessageType.success,
      );
    } catch (e) {
      debugPrint('Download error: $e');
      showMessage(
        AppLocalizations.of(context).translate('Error downloading certificate'),
        MessageType.error,
      );
    } finally {
      setState(() {
        isDownloading = false;
        progress = 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the provided certificate ID or fallback to the default one
    final String certificateId = widget.certificateId ?? '1cb2f40e-30ff-4855-8600-1f086fd0b7e4';
    
    // final String encodedId = Uri.encodeComponent(certificateId);
    final String url = 'https://api.365.systems/odata/v2/Certificates($certificateId)/Content';
    
    debugPrint('Certificate URL: $url');
    
    const String username = 'elme_full';
    const String password = 'f414bbe5-88c6-4ee1-939c-4813cbb7e0a3';
    
    // Create Basic Auth
    String basicAuth = base64Encode(utf8.encode('$username:$password'));

    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context).translate('Certificate Viewer'),
          style: FontUtilities.getTextStyle(TextType.medium, fontWeight: FontWeight.bold),
        ),
        backgroundColor: primaryColor,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () {
              if (webViewController != null) {
                setState(() {
                  errorMessage = null;
                  isLoading = true;
                });
                webViewController!.reload();
              }
            },
          ),
          IconButton(
            icon: isDownloading ? Icon(Icons.hourglass_bottom) : Icon(Icons.download),
            onPressed: isDownloading ? null : _downloadCertificate,
          ),
        ],
      ),
      body: Stack(
        children: [
          if (errorMessage == null)
            InAppWebView(
              initialUrlRequest: URLRequest(
                url: WebUri.uri(Uri.parse(url)),
                headers: {
                  'Authorization': 'Basic $basicAuth',
                },
              ),
              initialOptions: InAppWebViewGroupOptions(
                crossPlatform: InAppWebViewOptions(
                  useShouldOverrideUrlLoading: true,
                  mediaPlaybackRequiresUserGesture: false,
                  javaScriptEnabled: true,
                  cacheEnabled: true,
                ),
                android: AndroidInAppWebViewOptions(
                  useHybridComposition: true,
                  useShouldInterceptRequest: true,
                  useWideViewPort: true,
                ),
                ios: IOSInAppWebViewOptions(
                  allowsInlineMediaPlayback: true,
                ),
              ),
              onWebViewCreated: (controller) {
                webViewController = controller;
              },
              onLoadStart: (controller, url) {
                setState(() {
                  isLoading = true;
                });
              },
              onLoadStop: (controller, url) {
                setState(() {
                  isLoading = false;
                });
              },
              onProgressChanged: (controller, progress) {
                setState(() {
                  this.progress = progress / 100;
                });
              },
              onReceivedError: (controller, request, error) {
                debugPrint('WebView error: ${error.description}');
                
                // Only set error message if it's a critical error
                // Some errors are non-critical and the content might still load
                if (error.type == WebResourceErrorType.UNKNOWN ||
                    error.type == WebResourceErrorType.HOST_LOOKUP ||
                    error.type == WebResourceErrorType.TIMEOUT) {
                  setState(() {
                    errorMessage = error.description;
                    isLoading = false;
                  });
                  
                  showMessage(
                    '${AppLocalizations.of(context).translate('Error loading file')}: ${error.description}',
                    MessageType.error,
                  );
                }
              },
            ),
          if (errorMessage != null)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, color: alertError, size: 48),
                  SizedBox(height: 16),
                  Text(
                    '${AppLocalizations.of(context).translate('Error')}: $errorMessage',
                    style: FontUtilities.getTextStyle(TextType.medium),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        errorMessage = null;
                        isLoading = true;
                      });
                      webViewController?.reload();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                    ),
                    child: Text(AppLocalizations.of(context).translate('Try Again')),
                  ),
                ],
              ),
            ),
          if (isLoading && errorMessage == null)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  ),
                  SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context).translate('Loading certificate...'),
                    style: FontUtilities.getTextStyle(TextType.medium),
                  ),
                ],
              ),
            ),
          if (isDownloading)
            Container(
              color: Colors.black54,
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      value: progress > 0 ? progress : null,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context).translate('Downloading certificate...'),
                      style: FontUtilities.getTextStyle(
                        TextType.medium,
                      ),
                    ),
                    if (progress > 0)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          '${(progress * 100).toStringAsFixed(0)}%',
                          style: FontUtilities.getTextStyle(
                            TextType.medium,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
