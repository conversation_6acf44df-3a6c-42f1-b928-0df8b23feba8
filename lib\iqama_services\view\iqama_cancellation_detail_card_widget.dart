import 'package:eeh/iqama_services/model/iqama_details_response.dart';
import 'package:eeh/iqama_services/view/iqama_info_widget.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:flutter/material.dart';

class iqama_cancellation_detail_card_widget extends StatelessWidget {
  final Iqamadetails selectedUser;
  final String? issueDate;
  final String? expireDate;
  const iqama_cancellation_detail_card_widget({
    super.key,
    required this.selectedUser,
    required this.issueDate,
    required this.expireDate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(color: lightGreyColor),
      child: Column(
        children: [
          SizedBox(height: 10),
          IqamaInfo(
            title: AppLocalizations.of(context).translate("Employee ID"),
            value: selectedUser.employeeid ?? '-'
            // AppLocalizations.appLang == 'en' ? iqamaDetailsResponse.iqamadetails?.first.nameen ?? ''
            //     :iqamaDetailsResponse.iqamadetails?.first.namear ?? '',
          ),
          SizedBox(height: 10),
          IqamaInfo(
            title: AppLocalizations.of(context).translate("Iqama Number: "),
            value: selectedUser.iqamano ?? '-',
          ),
          SizedBox(height: 10),
          IqamaInfo(
            title: AppLocalizations.of(context).translate("Issue Date: "),
            value: issueDate ??
                selectedUser.issuedate?.replaceAll('-', '/') ??
                '-',
          ),
          SizedBox(height: 10),
          IqamaInfo(
            title: AppLocalizations.of(context).translate("Expiry Date: "),
            value: expireDate ??
                selectedUser.expirydate?.replaceAll('-', '/') ??
                '-',
          ),
          SizedBox(height: 10),
          IqamaInfo(
            title: AppLocalizations.of(context).translate("cost center :"),
            value: selectedUser.costcenter ?? '-',
          ),
          SizedBox(height: 10),
        ],
      ),
    );
  }
}
