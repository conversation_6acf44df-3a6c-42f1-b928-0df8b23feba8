import 'package:equatable/equatable.dart';

class GetLmsSheetIdResponseModel extends Equatable {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final int? reportRuleId;
  final List<dynamic>? myTeam;
  final String? sheetId;
  final String? vYear;
  final dynamic authcode;
  final dynamic teamSheetId;
  final String? type;
  final String? employeeId;

  const GetLmsSheetIdResponseModel({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.reportRuleId,
    this.myTeam,
    this.sheetId,
    this.vYear,
    this.authcode,
    this.teamSheetId,
    this.type,
    this.employeeId,
  });

  factory GetLmsSheetIdResponseModel.fromJson(Map<String, dynamic> json) {
    return GetLmsSheetIdResponseModel(
      createdBy: json['createdBy'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      recId: json['recId'] as int?,
      reportRuleId: json['reportRuleId'] as int?,
      myTeam: json['my_team'] as List<dynamic>?,
      sheetId: json['sheet_id'] as String?,
      vYear: json['v_year'] as String?,
      authcode: json['authcode'] as dynamic,
      teamSheetId: json['team_sheet_id'] as dynamic,
      type: json['type'] as String?,
      employeeId: json['employee_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'createdBy': createdBy,
        'createdById': createdById,
        'createdDate': createdDate,
        'recId': recId,
        'reportRuleId': reportRuleId,
        'my_team': myTeam,
        'sheet_id': sheetId,
        'v_year': vYear,
        'authcode': authcode,
        'team_sheet_id': teamSheetId,
        'type': type,
        'employee_id': employeeId,
      };

  @override
  List<Object?> get props {
    return [
      createdBy,
      createdById,
      createdDate,
      recId,
      reportRuleId,
      myTeam,
      sheetId,
      vYear,
      authcode,
      teamSheetId,
      type,
      employeeId,
    ];
  }
}
