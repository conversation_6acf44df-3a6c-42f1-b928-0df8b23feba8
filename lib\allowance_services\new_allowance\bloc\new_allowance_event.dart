import 'package:eeh/allowance_services/model/allawance_list_response_model.dart';

abstract class NewAllowanceEvent {}

class NewAllowanceInitialEvent extends NewAllowanceEvent {
  final bool isGas;
  NewAllowanceInitialEvent({required this.isGas});
}

class BeneficialEmployeeValueChangelEvent extends NewAllowanceEvent {
  final Employee value;
  BeneficialEmployeeValueChangelEvent(this.value);
}

class NumberOfMonthsValueChangelEvent extends NewAllowanceEvent {
  final NumOfMonths value;
  NumberOfMonthsValueChangelEvent(this.value);
}

class StartDateValueChangelEvent extends NewAllowanceEvent {}

class EndDateValueChangelEvent extends NewAllowanceEvent {}

class CalculateEndDateEvent extends NewAllowanceEvent {}

class AddEmployeeEvent extends NewAllowanceEvent {}

class ActionTappedEvent extends NewAllowanceEvent {
  int index;
  ActionTappedEvent(this.index);
}

class EmployeeAllowanceEvent extends NewAllowanceEvent {}

class SubmitAllowanceEvent extends NewAllowanceEvent {}

