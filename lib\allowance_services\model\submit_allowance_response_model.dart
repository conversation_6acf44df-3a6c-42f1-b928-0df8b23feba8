class SubmitAllowanceResponse {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  Map<String, dynamic>? meetingParameters;
  String? requesterEmail;
  String? nxtAprNameAr;
  String? myTypeId;
  String? resultMessage;
  String? reqStatusAr;
  String? userId;
  String? resultMessageAr;
  String? groupsEmails;
  String? approvedByEn;
  List<dynamic>? nextApprovalLis;
  String? approvedByAr;
  bool? withdrawFlag;
  String? employeeNames;
  String? rejectedByAr;
  String? tableName;
  String? sapSimulation;
  List<ApprovalProcess>? approvalProce;
  String? listOfEmployees;
  String? elmWebUrl;
  String? displayRecId;
  String? authKey;
  String? rejectionReason;
  List<dynamic>? attachments;
  String? pendingRequest;
  String? resultMessageEn;
  String? requestType;
  String? withdrawReason;
  String? reqCardData;
  String? requesterNameEn;
  String? employeeId;
  String? numOfEmps;
  String? exception;
  String? requestStatus;
  String? requestDetails;
  String? numberOfMonths;
  String? requestTypeAr;
  String? nxtAprNameEn;
  String? startDates;
  String? createdDatee;
  List<EmployeeAllowa>? employeesAllowa;
  String? groupId;
  String? eligibleAmounts;
  String? notes;
  String? code;
  String? taskWebUrl;
  String? requesterNameAr;
  String? serviceKey;
  String? rejectedByEn;

  SubmitAllowanceResponse({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.meetingParameters,
    this.requesterEmail,
    this.nxtAprNameAr,
    this.myTypeId,
    this.resultMessage,
    this.reqStatusAr,
    this.userId,
    this.resultMessageAr,
    this.groupsEmails,
    this.approvedByEn,
    this.nextApprovalLis,
    this.approvedByAr,
    this.withdrawFlag,
    this.employeeNames,
    this.rejectedByAr,
    this.tableName,
    this.sapSimulation,
    this.approvalProce,
    this.listOfEmployees,
    this.elmWebUrl,
    this.displayRecId,
    this.authKey,
    this.rejectionReason,
    this.attachments,
    this.pendingRequest,
    this.resultMessageEn,
    this.requestType,
    this.withdrawReason,
    this.reqCardData,
    this.requesterNameEn,
    this.employeeId,
    this.numOfEmps,
    this.exception,
    this.requestStatus,
    this.requestDetails,
    this.numberOfMonths,
    this.requestTypeAr,
    this.nxtAprNameEn,
    this.startDates,
    this.createdDatee,
    this.employeesAllowa,
    this.groupId,
    this.eligibleAmounts,
    this.notes,
    this.code,
    this.taskWebUrl,
    this.requesterNameAr,
    this.serviceKey,
    this.rejectedByEn,
  });

  factory SubmitAllowanceResponse.fromJson(Map<String, dynamic> json) {
    return SubmitAllowanceResponse(
      createdBy: json['createdBy'] ,
      createdById: json['createdById'] ,
      createdDate: json['createdDate'] ,
      recId: json['recId'] ,
      meetingParameters: json['meetingParameters'] as Map<String, dynamic>?,
      requesterEmail: json['requesteremail'] ,
      nxtAprNameAr: json['nxt_apr_name_ar'] ,
      myTypeId: json['mytypeid'] ,
      resultMessage: json['resultmessage'] ,
      reqStatusAr: json['req_status_ar'] ,
      userId: json['userid'] ,
      resultMessageAr: json['resultmessagear'] ,
      groupsEmails: json['groups_emails'] ,
      approvedByEn: json['approved_by_en'] ,
      nextApprovalLis: json['nextapprovallis'] as List<dynamic>?,
      approvedByAr: json['approved_by_ar'] ,
      withdrawFlag: json['withdrawflag'] ,
      employeeNames: json['employeenames'] ,
      rejectedByAr: json['rejectedbyar'] ,
      tableName: json['table_name'] ,
      sapSimulation: json['sapsimulation'] ,
      approvalProce: (json['approvalproce'] as List<dynamic>?)
          ?.map((e) => ApprovalProcess.fromJson(e as Map<String, dynamic>))
          .toList(),
      listOfEmployees: json['listofemployees'] ,
      elmWebUrl: json['elm_web_url'] ,
      displayRecId: json['display_recid'] ,
      authKey: json['authkey'] ,
      rejectionReason: json['rejectionreason'] ,
      attachments: json['attachments'] as List<dynamic>?,
      pendingRequest: json['pending_request'] ,
      resultMessageEn: json['resultmessageen'] ,
      requestType: json['request_type'] ,
      withdrawReason: json['withdrawreason'] ,
      reqCardData: json['req_card_data'] ,
      requesterNameEn: json['requesternameen'] ,
      employeeId: json['employeeid'] ,
      numOfEmps: json['numofemps'] ,
      exception: json['exception'] ,
      requestStatus: json['requeststatus'] ,
      requestDetails: json['request_details'] ,
      numberOfMonths: json['numberofmonths'] ,
      requestTypeAr: json['request_type_ar'] ,
      nxtAprNameEn: json['nxt_apr_name_en'] ,
      startDates: json['startdates'] ,
      createdDatee: json['createddate'] ,
      employeesAllowa: (json['employeesallowa'] as List<dynamic>?)
          ?.map((e) => EmployeeAllowa.fromJson(e as Map<String, dynamic>))
          .toList(),
      groupId: json['group_id'] ,
      eligibleAmounts: json['eligibleamounts'] ,
      notes: json['notes'] ,
      code: json['code'] ,
      taskWebUrl: json['task_web_url'] ,
      requesterNameAr: json['requesternamear'] ,
      serviceKey: json['service_key'] ,
      rejectedByEn: json['rejectedbyen'] ,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'meetingParameters': meetingParameters,
      'requesteremail': requesterEmail,
      'nxt_apr_name_ar': nxtAprNameAr,
      'mytypeid': myTypeId,
      'resultmessage': resultMessage,
      'req_status_ar': reqStatusAr,
      'userid': userId,
      'resultmessagear': resultMessageAr,
      'groups_emails': groupsEmails,
      'approved_by_en': approvedByEn,
      'nextapprovallis': nextApprovalLis,
      'approved_by_ar': approvedByAr,
      'withdrawflag': withdrawFlag,
      'employeenames': employeeNames,
      'rejectedbyar': rejectedByAr,
      'table_name': tableName,
      'sapsimulation': sapSimulation,
      'approvalproce': approvalProce?.map((e) => e.toJson()).toList(),
      'listofemployees': listOfEmployees,
      'elm_web_url': elmWebUrl,
      'display_recid': displayRecId,
      'authkey': authKey,
      'rejectionreason': rejectionReason,
      'attachments': attachments,
      'pending_request': pendingRequest,
      'resultmessageen': resultMessageEn,
      'request_type': requestType,
      'withdrawreason': withdrawReason,
      'req_card_data': reqCardData,
      'requesternameen': requesterNameEn,
      'employeeid': employeeId,
      'numofemps': numOfEmps,
      'exception': exception,
      'requeststatus': requestStatus,
      'request_details': requestDetails,
      'numberofmonths': numberOfMonths,
      'request_type_ar': requestTypeAr,
      'nxt_apr_name_en': nxtAprNameEn,
      'startdates': startDates,
      'createddate': createdDatee,
      'employeesallowa': employeesAllowa?.map((e) => e.toJson()).toList(),
      'group_id': groupId,
      'eligibleamounts': eligibleAmounts,
      'notes': notes,
      'code': code,
      'task_web_url': taskWebUrl,
      'requesternamear': requesterNameAr,
      'service_key': serviceKey,
      'rejectedbyen': rejectedByEn,
    };
  }
}

class ApprovalProcess {
  Template? template;
  dynamic predecessors;
  int? taskSeq;
  String? newPlannedStartDate;
  String? description;
  String? taskTypeName;
  String? plannedEndDate;
  int? duration;
  int? taskTypeId;
  dynamic parentSequence;
  int? urgency;
  int? processId;
  String? processName;
  dynamic ccList;
  dynamic dueDate;
  dynamic actualCost;
  int? recId;
  dynamic msPriorityValue;
  bool? noWait;
  dynamic minStartDate;
  dynamic ccListId;
  String? plannedStartDate;
  int? plannedEfforts;
  List<dynamic>? assignedEmpsGrpsToTask;
  int? primeId;
  String? taskName;
  int? typeId;
  dynamic primeTypeId;
  dynamic assignee;
  int? objectId;

  ApprovalProcess({
    this.template,
    this.predecessors,
    this.taskSeq,
    this.newPlannedStartDate,
    this.description,
    this.taskTypeName,
    this.plannedEndDate,
    this.duration,
    this.taskTypeId,
    this.parentSequence,
    this.urgency,
    this.processId,
    this.processName,
    this.ccList,
    this.dueDate,
    this.actualCost,
    this.recId,
    this.msPriorityValue,
    this.noWait,
    this.minStartDate,
    this.ccListId,
    this.plannedStartDate,
    this.plannedEfforts,
    this.assignedEmpsGrpsToTask,
    this.primeId,
    this.taskName,
    this.typeId,
    this.primeTypeId,
    this.assignee,
    this.objectId,
  });

  factory ApprovalProcess.fromJson(Map<String, dynamic> json) {
    return ApprovalProcess(
      template: json['template'] != null
          ? Template.fromJson(json['template'] as Map<String, dynamic>)
          : null,
      predecessors: json['predecessors'],
      taskSeq: json['taskSeq'] ,
      newPlannedStartDate: json['new_planned_start_date'] ,
      description: json['description'] ,
      taskTypeName: json['taskTypeName'] ,
      plannedEndDate: json['plannedEndDate'] ,
      duration: json['duration'] ,
      taskTypeId: json['taskTypeId'] ,
      parentSequence: json['parentSequence'],
      urgency: json['urgency'] ,
      processId: json['processId'] ,
      processName: json['processName'] ,
      ccList: json['ccList'],
      dueDate: json['due_Date'],
      actualCost: json['actual_Cost'],
      recId: json['RECID'] ,
      msPriorityValue: json['msPriorityValue'],
      noWait: json['noWait'] ,
      minStartDate: json['min_Start_Date'],
      ccListId: json['ccListID'],
      plannedStartDate: json['plannedStartDate'] ,
      plannedEfforts: json['plannedEfforts'] ,
      assignedEmpsGrpsToTask: json['assignedEmpsGrpsToTask'] as List<dynamic>?,
      primeId: json['PRIME_ID'] ,
      taskName: json['taskName'] ,
      typeId: json['typeId'] ,
      primeTypeId: json['PRIME_TYPE_ID'],
      assignee: json['assignee'],
      objectId: json['objectid'] ,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'template': template?.toJson(),
      'predecessors': predecessors,
      'taskSeq': taskSeq,
      'new_planned_start_date': newPlannedStartDate,
      'description': description,
      'taskTypeName': taskTypeName,
      'plannedEndDate': plannedEndDate,
      'duration': duration,
      'taskTypeId': taskTypeId,
      'parentSequence': parentSequence,
      'urgency': urgency,
      'processId': processId,
      'processName': processName,
      'ccList': ccList,
      'due_Date': dueDate,
      'actual_Cost': actualCost,
      'RECID': recId,
      'msPriorityValue': msPriorityValue,
      'noWait': noWait,
      'min_Start_Date': minStartDate,
      'ccListID': ccListId,
      'plannedStartDate': plannedStartDate,
      'plannedEfforts': plannedEfforts,
      'assignedEmpsGrpsToTask': assignedEmpsGrpsToTask,
      'PRIME_ID': primeId,
      'taskName': taskName,
      'typeId': typeId,
      'PRIME_TYPE_ID': primeTypeId,
      'assignee': assignee,
      'objectid': objectId,
    };
  }
}

class Template {
  int? createdById;
  String? createdBy;
  int? createdDate;
  String? companyName;
  String? uuid;
  int? recId;
  String? templateName;
  int? typeId;

  Template({
    this.createdById,
    this.createdBy,
    this.createdDate,
    this.companyName,
    this.uuid,
    this.recId,
    this.templateName,
    this.typeId,
  });

  factory Template.fromJson(Map<String, dynamic> json) {
    return Template(
      createdById: json['createdById'] ,
      createdBy: json['createdBy'] ,
      createdDate: json['createdDate'] ,
      companyName: json['companyName'] ,
      uuid: json['uuid'] ,
      recId: json['recId'] ,
      templateName: json['templateName'] ,
      typeId: json['typeId'] ,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdById': createdById,
      'createdBy': createdBy,
      'createdDate': createdDate,
      'companyName': companyName,
      'uuid': uuid,
      'recId': recId,
      'templateName': templateName,
      'typeId': typeId,
    };
  }
}

class EmployeeAllowa {
  String? employeeNameEn;
  String? endDate;
  String? comments;
  double? eligibleAmount;
  String? employeeNameAr;
  String? startDate;
  int? numberOfMonths;
  int? employeeId;
  int? recId;

  EmployeeAllowa({
    this.employeeNameEn,
    this.endDate,
    this.comments,
    this.eligibleAmount,
    this.employeeNameAr,
    this.startDate,
    this.numberOfMonths,
    this.employeeId,
    this.recId,
  });

  factory EmployeeAllowa.fromJson(Map<String, dynamic> json) {
    return EmployeeAllowa(
      employeeNameEn: json['employeename_en'] ,
      endDate: json['enddate'] ,
      comments: json['comments'] ,
      eligibleAmount: (json['eligibleamount'] as num?)?.toDouble(),
      employeeNameAr: json['employeename_ar'] ,
      startDate: json['startdate'] ,
      numberOfMonths: json['numberofmonths'] ,
      employeeId: json['employeeid'] ,
      recId: json['recid'] ,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employeename_en': employeeNameEn,
      'enddate': endDate,
      'comments': comments,
      'eligibleamount': eligibleAmount,
      'employeename_ar': employeeNameAr,
      'startdate': startDate,
      'numberofmonths': numberOfMonths,
      'employeeid': employeeId,
      'recid': recId,
    };
  }
}