import 'package:eeh/shared/widgets/general_text_form_field.dart';

class AllowanceListResponse {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  Map<String, dynamic>? meetingParameters;
  String? extensioneligib;
  String? managerid;
  String? requesttype;
  String? message;
  String? authkey;
  String? code;
  List<Employee>? employees;
  String? maxallowancemon;

  AllowanceListResponse({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.meetingParameters,
    this.extensioneligib,
    this.managerid,
    this.requesttype,
    this.message,
    this.authkey,
    this.code,
    this.employees,
    this.maxallowancemon,
  });

  factory AllowanceListResponse.fromJson(Map<String, dynamic> json) {
    final dynamic rawData = json['employees'] ?? json['cas_employees'];
    return AllowanceListResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      meetingParameters: json['meetingParameters'] as Map<String, dynamic>?,
      extensioneligib: json['extensioneligib'],
      managerid: json['managerid'],
      requesttype: json['requesttype'],
      message: json['message'],
      authkey: json['authkey'],
      code: json['code'],
      employees: (rawData as List<dynamic>?)
          ?.map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),   
      maxallowancemon: json['maxallowancemon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'meetingParameters': meetingParameters,
      'extensioneligib': extensioneligib,
      'managerid': managerid,
      'requesttype': requesttype,
      'message': message,
      'authkey': authkey,
      'code': code,
      'employees': employees?.map((e) => e.toJson()).toList(),
      'maxallowancemon': maxallowancemon,
    };
  }
}

class Employee extends GeneralSheetContent {
  String? currentstatus;
  String? employeenamear;
  String? enddate;
  int? eligibleamount;
  String? employeenameen;
  String? startdate;
  String? numberofmonths;
  String? employeeid;
  int? recid;
  String? comment;
  String? extendedMonths;
  bool? isChecked;

  Employee({
    this.currentstatus,
    this.employeenamear,
    this.enddate,
    this.eligibleamount,
    this.employeenameen,
    this.startdate,
    this.numberofmonths,
    this.employeeid,
    this.recid,
    this.extendedMonths,
    this.comment,
    this.isChecked,
  }) {
    nameen = employeenameen;
    namear = employeenamear;
    tralingen = eligibleamount.toString();
  }

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      currentstatus: json['currentstatus'],
      employeenamear: json['employeenamear'],
      enddate: json['enddate'],
      eligibleamount: json['eligibleamount'],
      employeenameen: json['employeenameen'],
      startdate: json['startdate'],
      numberofmonths: json['numberofmonths'].toString(),
      employeeid: json['employeeid'].toString(),
      recid: json['recid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentstatus': currentstatus,
      'employeenamear': employeenamear,
      'enddate': enddate,
      'eligibleamount': eligibleamount,
      'employeenameen': employeenameen,
      'startdate': startdate,
      'numberofmonths': numberofmonths,
      'employeeid': employeeid,
      'recid': recid,
    };
  }
}

class NumOfMonths extends GeneralSheetContent {
  final String nameEn;
  final String nameAr;
  NumOfMonths({
    required this.nameEn,
    required this.nameAr,
  }) {
    nameen = nameEn;
    namear = nameAr;
  }
}
