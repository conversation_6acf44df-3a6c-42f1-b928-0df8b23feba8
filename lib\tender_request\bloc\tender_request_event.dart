import 'package:eeh/tender_request/model/tender_payment_model.dart';

import '../../letter_of_guarantee/models/Lists.dart';

abstract class TenderRequestEvent{}

class TenderRequestInitialEvent extends TenderRequestEvent{}

class PaymentTypeListEvent extends TenderRequestEvent{}

class ProjectWpsListEvent extends TenderRequestEvent{}

class BankListEvent extends TenderRequestEvent{}

class CustomerListEvent extends TenderRequestEvent{}

class UploadAttachmentFilesEvent extends TenderRequestEvent{}

class FilesUpdatedEvent extends TenderRequestEvent{}

class SubmitEvent extends TenderRequestEvent{}

class ChangeBudgetTypeEvent extends TenderRequestEvent{
  final BudgetTypeModel budgetTypeModel;
    ChangeBudgetTypeEvent(this.budgetTypeModel);
}

class ChangePaymentTypeEvent extends TenderRequestEvent{
  final TenderPayment tenderPayment;
  ChangePaymentTypeEvent(this.tenderPayment);
}

class ValidateFieldsEvent extends TenderRequestEvent{}