
import 'package:eeh/tender_request/model/projectWps_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../l10n/app_localizations.dart';
import '../../../letter_of_guarantee/models/Lists.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/widgets/currency_field_component.dart';
import '../../../shared/widgets/new_textformfield_component.dart';
import '../../bloc/tender_request_state.dart';
import '../../model/bank_model.dart';
import '../../model/static_data_model.dart';
import '../../model/tender_payment_model.dart';


class TenderUtils{
  getBudgetType(context, controller, Function(BudgetTypeModel)? onSelect ,key
      ){

    return NewCustomTextFieldComponent(
        labelText: AppLocalizations.of(context).translate("Budget Type"),
        fieldKey: key,
        controller: controller,
        keyboardType: TextInputType.none,
        isReadOnly: true,
        isMandatory: true,
        type: TextFieldType.bottomSheet,
         onSelect: (v) {
            onSelect?.call(v);
         },
        bottomSheetHeight:0.25 ,
        bottomSheetText:AppLocalizations.of(context).translate("Budget Type"),
        bottomSheetContentList: departmentList,
        validationText:AppLocalizations.of(context)
            .translate("must select Budget Type") ,

          );  }

  getCostCenter(context, controller,key, [Function(dynamic)? onChanged]
      ){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Cost Center"),
      fieldKey: key,
      controller: controller,
      onChange: onChanged,
      keyboardType: TextInputType.none,
      isReadOnly: true,
      isDemmed: true,
      validationText:AppLocalizations.of(context)
          .translate("must select Cost Center") ,

    );  }

  getProject(context, controller,key,bottomSheetContentList,isLoading, [Function(ProjectWps)? onSelect]
      ){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Project"),
      bottomSheetHasSearch: true,
      fieldKey: key,
      controller: controller,
      keyboardType: TextInputType.none,
      isReadOnly: true,
      isMandatory: true,
      type: TextFieldType.bottomSheet ,
      onSelect: (v) {
        onSelect?.call(v);
      },
      bottomSheetHeight:0.85 ,
      isLoading: isLoading,
      bottomSheetText:AppLocalizations.of(context)
          .translate("Project"),
      bottomSheetContentList: bottomSheetContentList,
      validationText:AppLocalizations.of(context)
          .translate("must select Project") ,

    );  }


  getTenderName(context, controller,key, [Function(dynamic)? onChanged]){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Tender Name"),
      fieldKey: key,
      controller: controller,
      keyboardType: TextInputType.name,
      isMandatory: true,
      type: TextFieldType.normal,
      onChange:onChanged ,
      validationText:AppLocalizations.of(context)
          .translate("must select Tender Name") ,

    );  }

  getTenderNumber(context, controller,key, [Function(dynamic)? onChanged]){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Tender Number"),
      controller: controller,
      fieldKey: key,
      keyboardType: TextInputType.number,
      isMandatory: true,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      type: TextFieldType.normal,
      onChange:onChanged ,
      validationText:AppLocalizations.of(context)
          .translate("must select Tender Number") ,

    );  }


  getTenderValue(context, controller,key, [Function(dynamic)? onChanged]){
    return  CurrencyTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Tender Value"),
      isMandatory: true,
      fieldKey: key,
      controller: controller,
      onChange: onChanged,
      sARComponentSize: 14.sp,
      validator: (value) {
        if (value!.isEmpty) {
          return AppLocalizations.of(context)
              .translate("must select Tender Value");
        }
        return null;
      },
      sarColor: secondTextColor,);
  }


  getPaymentType(context, controller,key,bottomSheetContentList,isLoading,Function(TenderPayment)? onSelect
      ){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Payment Type"),
      controller: controller,
      fieldKey: key,
      keyboardType: TextInputType.none,
      isReadOnly: true,
      isMandatory: true,
      type: TextFieldType.bottomSheet,
      onSelect: (v) {
        onSelect?.call(v);
      },
      isLoading: isLoading,
      bottomSheetHeight:0.3 ,
      bottomSheetText:AppLocalizations.of(context).translate("Payment Type"),
      bottomSheetContentList: bottomSheetContentList,
      validationText:AppLocalizations.of(context)
          .translate("must select Payment Type") ,

    );  }


  getSADADNumber(context, controller,key, [ Function(dynamic)? onChanged]){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("SADAD Number"),
      controller: controller,
      fieldKey: key,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      isMandatory: true,
      type: TextFieldType.normal,
      onChange:onChanged ,
      validationText:AppLocalizations.of(context)
          .translate("must enter SADAD Number"),

    );  }


  getBeneficiaryName(context, controller,key, [Function(dynamic)? onChanged]){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Beneficiary Name"),
      controller: controller,
      fieldKey: key,
      keyboardType: TextInputType.name,
      isMandatory: true,
      type: TextFieldType.normal,
      onChange:onChanged ,
      validationText:AppLocalizations.of(context)
          .translate("must enter Beneficiary Name") ,

    );  }

  getBank(context, controller,key,bottomSheetContentList,isLoading, Function(BankModel)? onSelect){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("Bank"),
      controller: controller,
      keyboardType: TextInputType.none,
      key: key,
      isMandatory: true,
      bottomSheetHeight:0.85 ,
      bottomSheetHasSearch: true,
      isLoading: isLoading,
      bottomSheetContentList:bottomSheetContentList,
      onSelect: (v) {
        onSelect?.call(v);
      },
      type: TextFieldType.bottomSheet,
        bottomSheetText:AppLocalizations.of(context).translate("Bank"),
      validationText:AppLocalizations.of(context)
          .translate("must select Bank") ,

    );  }


  getIBAN(context, controller,key, [Function(dynamic)? onChanged]){

    return NewCustomTextFieldComponent(
      labelText: AppLocalizations.of(context).translate("IBAN"),
      controller: controller,
      key:key,
      keyboardType: TextInputType.name,
      isMandatory: true,
      type: TextFieldType.normal,
      onChange:onChanged ,
      validationText:AppLocalizations.of(context)
          .translate("must enter IBAN"),

    );  }




}
