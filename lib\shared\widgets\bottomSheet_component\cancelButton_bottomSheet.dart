import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../styles/colors.dart';


class CancelButton extends StatefulWidget {
  final String? title;
  final Function() onTap;
  const CancelButton({super.key, this.title, required this.onTap});

  @override
  State<CancelButton> createState() => _CancelButtonState();
}

class _CancelButtonState extends State<CancelButton> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: widget.onTap,
        style: TextButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(0.0),
          ),
        ),
        child: Text(widget.title ?? AppLocalizations.of(context).translate('Cancel'),
            style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w400,
                color: darkbrone)),
      ),
    );
  }
}
