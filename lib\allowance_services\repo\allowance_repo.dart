import 'package:eeh/allowance_services/model/submit_allowance_request_body.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/secure_storage.dart';

abstract class IAllowanceRepo {
  Future<NetworkResponse> fetchAllownceList(
      { required String requestType});
  Future<NetworkResponse> supmitAllowance({required SupmitAllowanceBody employeesAllowa,required String appKey});
}

class AllowanceRepo implements IAllowanceRepo {
  final bool isGas;
  AllowanceRepo({this.isGas = false});
  @override
  Future<NetworkResponse> fetchAllownceList({ required String requestType}) async {
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'requesttype': requestType},
        requestType: RequestType.post,
        sessionToken:
            await SecureStorageService().readSecureData(key: "user_session") ??
                '',
        headers: getAllowanceListHeader());

    return response;
  }

  getAllowanceListHeader() {
    if (isGas == false) {
      return {
        "formName": "Get Communication Allowance Employees Form",
        "moduleName": "Get Communication Allowance Employees",
        "appKey": "ACM",
        "Content-Type": "application/json",
      };
    } else {
      return {
        "formName": "Get Gas Allowance Employees Form",
        "moduleName": "Get Gas Allowance Employees",
        "appKey": "ACM",
        "Content-Type": "application/json",
      };
    }
  }
  
  @override
  Future<NetworkResponse> supmitAllowance({required SupmitAllowanceBody employeesAllowa,required String appKey})async {
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: employeesAllowa.toJson(),
        requestType: RequestType.post,
        sessionToken:
            await SecureStorageService().readSecureData(key: "user_session") ??
                '',
        headers:{
        "formName": "Service Data Form",
        "moduleName": "Service Data",
        "appKey":appKey,
        "Content-Type": "application/json",
        });

    return response;
  }
}
