import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/education/models/country_response_model.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flame/widgets.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flame/sprite.dart';

import 'dart:typed_data'as tdata;
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:eeh/shared/styles/colors.dart';
class FlagsList extends StatefulWidget {
  final List contentList;
  final Function(dynamic)? onTap;
   
  const FlagsList({super.key,required this.contentList,this.onTap});

  @override
  State<FlagsList> createState() => _FlagsListState();
}

class _FlagsListState extends State<FlagsList> {
  SpriteSheet? _animationSpriteSheet;
  List filterList = [];
  @override
  void initState() {
    super.initState();
    filterList = widget.contentList;
    WidgetsBinding.instance.addPostFrameCallback((_){
      _loadAndDrawImage();
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        // height: 400.h,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: SizedBox(
                height: 40.h,
                child: TextFormField(
                  style:FontUtilities.getTextStyle(TextType.regular,
                      size:12.sp,fontWeight:FontWeight.w400),
                  onChanged: (text) {
                    if (text.isEmpty) {
                      filterList = widget.contentList;
                    } else {
                      filterList = [];
                      for (var item in widget.contentList) {
                        if ((AppLocalizations.appLang == 'en'
                                ? item.nameen
                                : item.namear)
                            .toString()
                            .toLowerCase()
                            .contains(text.toLowerCase())) {
                          filterList.add(item);
                        }
                      }
                    }
                    setState(() {});
                  },
                  decoration: InputDecoration(
                      contentPadding: EdgeInsets.all(5),
                      border: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(9),
                          ),
                          borderSide: BorderSide(color: Color(0xffdadada))),
                      prefixIcon: const Icon(
                        Icons.search,
                        color: Color(0xff949494),
                      ),
                      hintText:
                          '${AppLocalizations.of(context).translate("Search")} ',
                      hintStyle: FontUtilities.getTextStyle(TextType.disable,
                      size:12.sp,fontWeight:FontWeight.w400)),
                ),
              ),
            ),
            SizedBox(
              height: 350.h,
              child: ListView.separated(
                  separatorBuilder: (_, __) => SizedBox(
                        height: 2.h,
                      ),
                  itemCount: filterList.length,
                  itemBuilder: (ctx, index) =>
                      buildCountryAvatar(filterList[index])),
            ),
          ],
        ));
  }

  Widget buildCountryAvatar(Country item){
    final double  avatarSize=16.r;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap:()=> widget.onTap!(item),
        child: Row(
          children: [
            CircleAvatar(backgroundColor: primaryColor,
              radius: 18.r,
              child: CircleAvatar(
                radius: avatarSize,
                child:_animationSpriteSheet!=null?  ClipOval(
                  clipBehavior: Clip.hardEdge,
                  child: SizedBox(
                    width: avatarSize*2,
                    height: avatarSize*2,
                    child: FittedBox(
                        fit: BoxFit.fill,
                        child: (int.tryParse(item.flagindex ??'') ??0) < 239
                            ? SpriteWidget(
                          sprite: _animationSpriteSheet!
                              .getSpriteById(int.tryParse(item.flagindex ??'') ??0),
                        )
                            : Icon(
                          Icons.flag_circle,
                          size: 50.r,
                          color: Colors.white,
                        )),
                  ),
                ):SizedBox.shrink(),
              ),
            ),
            SizedBox(width: 8.w,),
            SizedBox(
              width: 250.w,
              child: Text(
                (AppLocalizations.appLang == 'en'
                        ? item.nameen
                        : item.namear) ??
                    '',
                style: FontUtilities.getTextStyle(TextType.medium,
                      size:14.sp,fontWeight:FontWeight.w500),
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadAndDrawImage() async {
    final tdata.ByteData data = await rootBundle.load('assets/images/flagSprite.png');
    final tdata.Uint8List bytes = data.buffer.asUint8List();
    final ui.Image originalImage = await decodeImageFromList(bytes);
    final ui.Image customPaintedImage = await drawOnCanvas(originalImage);
    setState(() {
      _animationSpriteSheet = SpriteSheet.fromColumnsAndRows(
          image:customPaintedImage,rows: 240,columns: 1,spacing:1,margin: 1
      );
    });
  }
  Future<ui.Image> drawOnCanvas(ui.Image image) async {
    final recorder = ui.PictureRecorder();

    final paint = Paint();
    final srcRect = Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    final int imageWidth =  image.width;
    final int imageHeight = image.height;;

    final double spriteWidth = imageWidth / 1;
    final double spriteHeight = imageHeight / 1;
    final canvas = Canvas(recorder, Rect.fromLTWH(0, 0, spriteWidth, spriteHeight));

    final dstRect = Rect.fromLTWH(0, 0, spriteWidth, spriteHeight);
    canvas.drawImageRect(image, srcRect, dstRect, paint);
    final picture = recorder.endRecording();
    return picture.toImage(spriteWidth.toInt(), spriteHeight.toInt());
  }


}



