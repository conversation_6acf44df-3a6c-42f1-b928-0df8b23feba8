abstract class CancelAllowanceEvent {}

class CancelAllowanceInitialEvent extends CancelAllowance<PERSON>vent {
  final bool isGas;
  CancelAllowanceInitialEvent(this.isGas);
}

class SelectAllItemEvent extends CancelAllowanceEvent {
  bool isSelected;
  SelectAllItemEvent({required this.isSelected});
}

class CheckItemEvent extends CancelAllowanceEvent {
  bool? value;
  int index;
  CheckItemEvent({required this.index, required this.value});
}

class EmployeeAllowanceEvent extends CancelAllowanceEvent {}

class SubmitAllowanceEvent extends CancelAllowanceEvent {}

class SearchEmployeeEvent extends CancelAllowanceEvent {
  final String value;
  SearchEmployeeEvent(this.value);
}
