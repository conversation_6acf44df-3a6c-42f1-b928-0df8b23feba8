import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:eeh/shared/flavors_config.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:flutter/foundation.dart';

/*
https://etxapiqa.elm.sa/digitalhub-test/rest/ => QA
https://projects.ntgapps.com/rest/ => Dev
https://elme.elm.sa/rest/ => Prod
sa.elm.myElm => elm bundle identifier
com.ntg.ntgapps => ntg bundle identifier
*/
String baseURL = FlavorConfig.instance.baseUrl;

class ApiHelper {
  final _dio = createDio();

  ApiHelper._internal();

  static final _singleton = ApiHelper._internal();

  factory ApiHelper() => _singleton;

  static Dio createDio() {
    const timeOut = Duration(seconds: 20);
    var dio = Dio(BaseOptions(
      baseUrl: baseURL,
      receiveTimeout: timeOut, // 20 seconds
      connectTimeout: timeOut,
      sendTimeout: timeOut,
      contentType: "application/json",
    ));

    dio.interceptors.addAll({
      AuthInterceptor(dio),
      ErrorInterceptors(dio),
      VersionInterceptor(dio),
    });
    dio.interceptors.addAll({
      APILogger(dio),
    });

    return dio;
  }

  Future<NetworkResponse> apiCall(String url,
      {Map<String, dynamic>? queryParameters,
      Object? body,
      String? sessionToken,
      required RequestType requestType,
      Map<String, String>? headers,
      CancelToken? cancelToken}) async {
    late Response result;
    try {

      switch (requestType) {
        case RequestType.get:
          {
            Options options =
                Options(headers:await EmpHeaders.getHeaders(sessionToken, headers));
            result = await _dio.get(url,
                queryParameters: queryParameters,
                options: options,
                cancelToken: cancelToken);
            break;
          }
        case RequestType.post:
          {
            Options options =
                Options(headers: await EmpHeaders.getHeaders(sessionToken, headers));
            result = await _dio.post(url,
                data: body, options: options, cancelToken: cancelToken);
            break;
          }
        case RequestType.delete:
          {
            Options options =
                Options(headers: await EmpHeaders.getHeaders(sessionToken, headers));
            result = await _dio.delete(url,
                data: queryParameters,
                options: options,
                cancelToken: cancelToken);
            break;
          }
        case RequestType.put:
          Options options = Options(headers:await EmpHeaders.getHeaders(sessionToken, headers));
          result = await _dio.put(url,
              data: queryParameters,
              options: options,
              cancelToken: cancelToken);
        case RequestType.patch:
          Options options = Options(headers:await EmpHeaders.getHeaders(sessionToken, headers));
          result = await _dio.patch(
            url,
            data: queryParameters,
            cancelToken: cancelToken,
            options: options,
          );
      }
      return NetworkResponse.success(result.data);
    } on DioException catch (error) {
      return NetworkResponse.error(error);
    } catch (error) {
      return NetworkResponse.error(error.toString());
    }
  }
}

class NetworkResponse<T> {
  T? data;
  dynamic error;
  NetworkResponse({this.data, this.error});
  NetworkResponse.success(this.data);
  NetworkResponse.error(this.error);
  NetworkResponse.loading();
  maybeWhen({
    required Function(T? data) ok,
    required Function(dynamic error) onError,
  }) {
    log("logged data:${data}");
    log("logged error:${error}");
    log("--------------");
    if (kDebugMode) {
      printFullMessage("printed data:${data}");
      printFullMessage("printed error:${data}");
    }
    if (isSuccess) {
      ok(data);
    }
    if (isError) {
      onError(error);
    }
  }

  void printFullMessage(String message) {
    // The print() function has a character limit, so we need to split long messages
    const int chunkSize =
        800; // A safe chunk size below the 1024-character limit
    for (int i = 0; i < message.length; i += chunkSize) {
      if (kDebugMode) {
        print(message.substring(
          i, i + chunkSize > message.length ? message.length : i + chunkSize));
      }
    }
  }

  bool get isLoading => error == null && data == null;
  bool get isSuccess => data != null;
  bool get isError => error != null;
}
