import '../../../../admin_service/Model/service details models/generic_service_details_model.dart';

abstract class GenericActionEvent{}
class GenericActionInitEvent extends GenericActionEvent{}
class GenericActionContentEvent extends GenericActionEvent{
  final String actionRoute;
  final String mutableContext;
  GenericActionContentEvent({required this.actionRoute,required this.mutableContext});
}
class SubmitGenericActionEvent extends GenericActionEvent{
  final String requestID;
  SubmitGenericActionEvent(this.requestID);
}
class UpdateInputFieldsEvent extends GenericActionEvent{
  ServiceField? serviceFieldItem;
  dynamic value;
  UpdateInputFieldsEvent({this.serviceFieldItem,this.value});
}

class ChangeSwitchEvent extends GenericActionEvent{
  dynamic item;
  dynamic value;
  ChangeSwitchEvent({required this.item,required this.value});
}
