import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class GenericEmployeeCard extends StatelessWidget {
  const GenericEmployeeCard({
    super.key,
    required this.employeeName,
    required this.employeePosition,
    required this.employeeDepartment,
    required this.employeeInitials,
    required this.idText,
    required this.gridViewItemWidget,
    this.hasError = false,
    this.isLoading = false,
    this.avatarBackgroundColor,
    this.borderColor,
    this.errorBorderColor,
    this.cardBackgroundColor,
    this.nameTextStyle,
    this.positionTextStyle,
    this.idTextStyle,
    this.initialsTextStyle,
    // this.teamMember,
  });

  // Required properties
  final String employeeName;
  final String employeePosition;
  final String employeeDepartment;
  final String employeeInitials;
  final String idText;

  // Optional properties
  final bool hasError;
  final bool isLoading;

  // Optional styling properties
  final Color? avatarBackgroundColor;
  final Color? borderColor;
  final Color? errorBorderColor;
  final Color? cardBackgroundColor;
  final TextStyle? nameTextStyle;
  final TextStyle? positionTextStyle;
  final TextStyle? idTextStyle;
  final TextStyle? initialsTextStyle;
  final List<Widget> gridViewItemWidget;
  // final TeamMemberWithCoursesModel? teamMember;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // Navigation.navigateToScreen(
        //   context, 
        //   // MyTeamMemberLearningOverviewScreen(teamMember: teamMember),
        // );
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.all(16.sp),
        decoration: ShapeDecoration(
          color: cardBackgroundColor ?? Colors.white,
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1.w,
              color: hasError
                  ? (errorBorderColor ?? Colors.red)
                  : (borderColor ?? Colors.grey.shade300),
            ),
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCardHeader(),
            SizedBox(height: 16.h),
            Text("Department"),
            Text(employeeDepartment),
            SizedBox(height: 8.h),
            Divider(thickness: 1.5.sp, color: dividerColor),
            GridView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisExtent: 65,
                crossAxisSpacing: 0,
                childAspectRatio: 1.7,
              ),
              itemBuilder: (context, i) => gridViewItemWidget[i],
              itemCount: gridViewItemWidget.length,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildAvatar(),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildEmployeeInfo(),
        ),
        SizedBox(width: 8.w),
        Icon(
          Icons.arrow_forward_ios_rounded,
          size: 16.sp,
          color: secondTextColor,
        )
      ],
    );
  }

  Widget _buildAvatar() {
    return SizedBox(
      width: 44.w,
      height: 44.h,
      child: ClipRect(
        child: Align(
          alignment: Alignment.topCenter,
          heightFactor: 0.5,
          child: CircleAvatar(
            backgroundColor: avatarBackgroundColor,
            child: Text(
              employeeInitials,
              style: initialsTextStyle ??
                  TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeInfo() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          employeeName,
          style: nameTextStyle ??
              TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
          overflow: TextOverflow.ellipsis,
        ),
        Text(
          employeePosition,
          style: positionTextStyle ??
              TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
              ),
        ),
        Text(
          idText,
          style: idTextStyle ??
              TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
              ),
        ),
      ],
    );
  }
}
