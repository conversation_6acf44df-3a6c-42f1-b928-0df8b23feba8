import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class IssueExitReEntryVisaTextField extends StatelessWidget {
  final Function? showBottomSheet;
  final TextEditingController controller;
  final String title;
  final bool? isReadOnly;
  final bool? haveBottomSheet;
  final Function? onChange;
  final Widget? suffixWidget;
  final TextInputType? textInputType;
  final Function(String)? validator;
  final FocusNode? focus;
  final Function? onEditingComplete;
  final Function? onTapOutside;
  final bool? isDemmed;

  const IssueExitReEntryVisaTextField({
    super.key,
    this.showBottomSheet,
    required this.controller,
    required this.title,
    this.isReadOnly,
    this.haveBottomSheet,
    this.onChange,
    this.suffixWidget,
    this.textInputType,
    this.validator,
    this.focus,
    this.onEditingComplete,
    this.onTapOutside,
    this.isDemmed,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      autovalidateMode: AutovalidateMode.onUserInteraction,
      focusNode: focus,
      key: Key(controller.text),
      keyboardType: textInputType ?? TextInputType.number,
      validator: (e) => validator != null ? validator!(e ?? '') : null,
      onEditingComplete: () =>
      onEditingComplete == null ? null : onEditingComplete!(),
      onTapOutside: (event) => onTapOutside == null ? null : onTapOutside!(),
      onChanged: (value) => onChange == null ? null : onChange!(),
      controller: controller,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly, // Allow only digits
      ],
      readOnly: isReadOnly ?? false,
      style:FontUtilities.getTextStyle(
                  TextType.medium,
                  size: 13.sp,
                ),
                
      //  FontUtility.getTextStyleForText(TextType.regular,
      //     textColor: isDemmed ?? false ? const Color(0xff939393) : Colors.black,
      //     size: 13),
      textAlignVertical: TextAlignVertical.bottom,
      decoration: InputDecoration(
        floatingLabelStyle: FontUtilities.getTextStyle(
                  TextType.regular,
                  size: 13.sp,
                ),
        labelStyle: FontUtilities.getTextStyle(
                  TextType.regular,
                  size: 13.sp,
                ),
        // FontUtility.getTextStyleForText(TextType.regular,
        //     textColor:
        //     isDemmed ?? false ? const Color(0xff939393) : Colors.black,
        //     size: 13),
        label: Text.rich(TextSpan(children: <TextSpan>[
          TextSpan(
            text: title,
          ),
          TextSpan(
              text: ' *',
              style: FontUtility.getTextStyleForText(TextType.regular,
                  textColor:
                  isDemmed ?? false ? const Color(0xff939393) : redColor,
                  size: 13)),
        ])),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color(0xFF949494),
          ),
        ),
        suffixIconConstraints: BoxConstraints(
          maxHeight: 24.h,
          maxWidth: 24.w,
          minHeight: 24.h,
          minWidth: 24.w,
        ),
        suffixIcon: suffixWidget,
        alignLabelWithHint: true,
        contentPadding: EdgeInsets.only(bottom: 7.w),
      ),
      onTap: () async => ((haveBottomSheet ?? false) && showBottomSheet != null)
          ? await showBottomSheet!()
          : null,
    );
  }
}
