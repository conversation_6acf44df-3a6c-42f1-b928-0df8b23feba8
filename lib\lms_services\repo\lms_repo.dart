import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/secure_storage.dart';

abstract class ILMSRepo {
  Future<NetworkResponse> getLmsSheetId(
      {required Map<String, dynamic> payload});
  Future<NetworkResponse> getMyLearning({required String sheetId});
  Future<NetworkResponse> getMyTeamLearning({required String sheetId});
  
}

class LMSRepo implements ILMSRepo {
  LMSRepo._internal();
  static final LMSRepo _instance = LMSRepo._internal();
  static LMSRepo get instance => _instance;

  static const _appKey = 'LMS';

  Future<String> _getSessionToken() async =>
      await SecureStorageService().readSecureData(key: 'user_session') ?? '';

  Future<NetworkResponse> _post({
    required Map<String, dynamic> body,
    required String formName,
    required String moduleName,
  }) async {
    final token = await _getSessionToken();
    final response = await <PERSON><PERSON><PERSON><PERSON><PERSON>().apiCall(
      genericObject,
      body: body,
      requestType: RequestType.post,
      sessionToken: token,
      headers: {
        'formName': formName,
        'moduleName': moduleName,
        'appKey': _appKey,
        'Content-Type': 'application/json',
      },
    );
    return response;
  }


  @override
  Future<NetworkResponse> getLmsSheetId(
      {required Map<String, dynamic> payload}) async {
   
    return _post(
        body: payload, formName: 'Get Data Form', moduleName: 'Get Data');
  }

  @override
  Future<NetworkResponse> getMyLearning(
      {required String sheetId}) async {
      final token = await _getSessionToken();
    final response = await ApiHelper().apiCall(
      getLmsMyLearning,
      requestType: RequestType.get,
      sessionToken: token,
      headers: {
        'export-csv': 'false',
      },
    queryParameters: {
      "name":"LMS_Get_My_Learning",
      // "filter":
      "sheet_id":"17"
    }
    );
    return response;
  }
  @override
  Future<NetworkResponse> getMyTeamLearning(
      {required String sheetId}) async {
      final token = await _getSessionToken();
    final response = await ApiHelper().apiCall(
      getLmsMyLearning,
      requestType: RequestType.get,
      sessionToken: token,
      headers: {
        'export-csv': 'false',
      },
    queryParameters: {
      "name":"LMS_Get_My_Team_Learning_Items",
      // "filter":
      "sheet_id":"17"
    }
    );
    return response;
  }

}
