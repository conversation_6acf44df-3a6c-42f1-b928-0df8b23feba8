import 'package:eeh/lms_services/utils/lms_utils.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'reminder_alert.dart';

class StatisticsCard extends StatelessWidget {
  final String title;
  final int count;
  final double percentage;
  final IconData? icon;
  final bool isSelected;
  final VoidCallback? onTap;
  final Color selectedBackgroundColor;
  final Color iconColor;
  final Color selectedBorderColor;
  final String? status;
  const StatisticsCard({
    Key? key,
    required this.title,
    required this.count,
    required this.percentage,
    this.icon,
    required this.isSelected,
    required this.onTap,
    this.selectedBackgroundColor = primaryColor,
    this.iconColor = secondTextColor,
    this.status = '',
    this.selectedBorderColor = primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4.r),
      child: Container(
        padding: const EdgeInsets.all(10).w,
        decoration: BoxDecoration(
          color: isSelected
              ? (selectedBackgroundColor.withValues(alpha: 0.15))
              : Colors.white,
          borderRadius: BorderRadius.circular(4),
          border: isSelected
              ? Border.all(
                  color: selectedBorderColor,
                  width: 1.5,
                )
              : Border.all(
                  color: borderCardColor,
                  width: 1.5,
                ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildCardData(),
            SizedBox(width: 16.w),
            _buildIcon(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCardData() {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w700,
              color: textMain,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: secondTextColor,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        status == LmsUtils.STATUS_OVERDUE
            ? IconButton(
                padding: EdgeInsets.zero,
                icon: Icon(
                  Icons.more_horiz,
                  color: primaryDarkBlue,
                ),
                onPressed: () {
                  ReminderAlert.show(context);
                },
              )
            : SizedBox.shrink(),
        Container(
          padding: EdgeInsets.all(8).w,
          margin: EdgeInsets.zero,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8).r,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ]),
          child: Icon(
            icon,
            color: iconColor,
          ),
        ),
      ],
    );
  }

}
