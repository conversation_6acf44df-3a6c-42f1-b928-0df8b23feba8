import 'package:eeh/discussion/bloc/discussion_bloc.dart';
import 'package:eeh/discussion/bloc/discussion_events.dart';
import 'package:eeh/discussion/bloc/discussion_states.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/widgets/pick_files_bottom_sheet.dart';
import 'package:eeh/shared/work_widget/work_item_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../shared/work_widget/utility_widgets/utility_methods.dart';

class MessageTextFieldWidget extends StatefulWidget {
  final DiscussionBloc bloc;
  WorkType? workType;
   MessageTextFieldWidget({required this.bloc, super.key,this.workType});

  @override
  State<MessageTextFieldWidget> createState() => _MessageTextFieldWidgetState();
}

class _MessageTextFieldWidgetState extends State<MessageTextFieldWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 264.w,
      // height: 50.h,
      child: TextFormField(
        keyboardType: TextInputType.multiline,
        minLines: 1,
        maxLines: 100,
        readOnly: widget.bloc.isRecordingMode,
        controller: widget.bloc.sendMessageController,
        onTap: () {
          widget.bloc.add(ChangeEmojiVisibilityEvent(true));
        },
        onChanged: (String value) {
          widget.bloc.add(ChangeSendIconEvent());
        },
        decoration: InputDecoration(
          hintStyle:
          FontUtilities.getTextStyle(TextType.regular, size: getMediumFontSize(widget.workType).sp),
          hintText: widget.bloc.isRecordingMode
              ? null
              : AppLocalizations.of(context).translate('addComment'),
          prefixIcon: widget.bloc.isRecordingMode
              ? getVoiceWidget()
              : IconButton(
                  onPressed: () {
                    FocusManager.instance.primaryFocus?.unfocus();

                    widget.bloc.add(ChangeEmojiVisibilityEvent(false));
                  },
                  icon:  Icon(Icons.emoji_emotions_outlined,size: getAvatarSize(widget.workType),)),
          suffixIcon: widget.bloc.isRecordingMode
              ? const SizedBox.shrink()
              : Container(width: 100,
                padding: EdgeInsets.only(right: 12,left: 4),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                    Transform.translate(
                      offset: AppLocalizations.appLang == 'en'? Offset(10, 3) : Offset(-10, 2),
                      child: SizedBox(
                        width: 25,
                        child: Center(
                          child: Transform.rotate(
                            angle: 100,
                            child: IconButton(
                              onPressed: () async {
                                showModalBottomSheet(
                                    isScrollControlled: true,
                                    context: context,
                                    backgroundColor: Colors.white,
                                    builder: (BuildContext context) {
                                      return PickFilesBottomSheet(
                                        onGalleryPressed: () => widget.bloc
                                            .pickFiles(
                                                attachmentType:
                                                    AttachmentType.fromGallery)
                                            .then((value) =>
                                                Navigation.popScreen(context)),
                                        onFilesPressed: () => widget.bloc
                                            .pickFiles(
                                                attachmentType:
                                                    AttachmentType.fromFile)
                                            .then((value) =>
                                                Navigation.popScreen(context)),
                                      );
                                    },
                                    showDragHandle: true);
                              },
                              icon:  Icon(
                                Icons.attach_file_rounded,
                                size: getAvatarSize(widget.workType),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 25,
                      child: Center(
                        child: IconButton(
                          onPressed: () async {
                            await widget.bloc.takePhoto();
                            widget.bloc.add(SendNewMessageLoadingEvent(
                                widget.bloc.sendMessageController.text,
                                DiscussionMessageType.attachMessage));
                          },
                          icon:  Icon(
                            Icons.camera_alt_outlined,
                            size: getAvatarSize(widget.workType),
                          ),
                        ),
                      ),
                    ),
                  ]),
              ),
          border: const OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(50))),
        ),
      ),
    );
  }

  Widget getVoiceWidget() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
            onPressed: () {
              widget.bloc.add(CancelRecordingEvent());
            },
            icon: Icon(
              Icons.delete_outline,
              color: Colors.red,
              size: 24.sp,
            )),
        IconButton(
            onPressed: () {
              widget.bloc.add(widget.bloc.isPause
                  ? ResumeRecordingEvent()
                  : PauseRecordingEvent());
            },
            icon: Icon(
              widget.bloc.isPause ? Icons.play_arrow_rounded : Icons.pause,
              size: 24.sp,
            )),
        SizedBox(
          width: 15.w,
        ),
        BlocBuilder<DiscussionBloc, DiscussionState>(
          bloc: widget.bloc,
          buildWhen: (previous, current) => current is UpdateRecordingTimeState,
          builder: (context, state) => Text(
            '${widget.bloc.formatNumber(widget.bloc.recordDuration! ~/ 60)} : ${widget.bloc.formatNumber(widget.bloc.recordDuration! % 60)}',
            style: FontUtility.getTextStyleForText(TextType.regular,
                size: 16.sp, textColor: greyColor),
          ),
        ),
      ],
    );
  }
}
