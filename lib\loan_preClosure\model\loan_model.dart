class Loan {
  final String installmentMonths;
  final String startDate;
  final String paidAmount;
  final String monthlyInstallmentAmount;
  final String endDate;
  final String totalAmount;
  final String remainingAmount;
  final String currency;
  final int recId;
  final String loanId;

  Loan({
    required this.installmentMonths,
    required this.startDate,
    required this.paidAmount,
    required this.monthlyInstallmentAmount,
    required this.endDate,
    required this.totalAmount,
    required this.remainingAmount,
    required this.currency,
    required this.recId,
    required this.loanId,
  });

  factory Loan.fromJson(Map<String, dynamic> json) {
    return Loan(
      installmentMonths: json['installmentmonths']?.toString().trim() ?? '',
      startDate: json['startdate']?.trim() ?? '',
      paidAmount: json['paidamount']?.trim() ?? '',
      monthlyInstallmentAmount: json['monthlyinstallmentamount']?.trim() ?? '',
      endDate: json['enddate']?.trim() ?? '',
      totalAmount: json['totalamount']?.trim() ?? '',
      remainingAmount: json['remainingamount']?.trim() ?? '',
      currency: json['currency']?.trim() ?? '',
      recId: json['recid'] ?? 0,
      loanId: json['loanid']?.trim() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'installmentmonths': installmentMonths,
      'startdate': startDate,
      'paidamount': paidAmount,
      'monthlyinstallmentamount': monthlyInstallmentAmount,
      'enddate': endDate,
      'totalamount': totalAmount,
      'remainingamount': remainingAmount,
      'currency': currency,
      'recid': recId,
      'loanid': loanId,
    };
  }
}
