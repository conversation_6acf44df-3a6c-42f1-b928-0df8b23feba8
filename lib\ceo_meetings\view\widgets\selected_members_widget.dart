import 'package:eeh/ceo_meetings/model/employee_model.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/utility/font_utility.dart';

class SelectedMembersWidget extends StatelessWidget {
  final List<Employee> selectedMembers;
  final Function(int) onRemove;

  const SelectedMembersWidget({
    super.key,
    required this.selectedMembers,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    if (selectedMembers.isEmpty) return SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(10.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: borderColor,
          width: 1.5.w,
        ),
        borderRadius: BorderRadius.circular(4.sp),
      ),
      child: ConstrainedBox(
        constraints:
            BoxConstraints(maxHeight: 180.h, minWidth: double.infinity),
        child: SingleChildScrollView(
          child: Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: List.generate(selectedMembers.length, (index) {
              final member = selectedMembers[index];
              final name = AppLocalizations.appLang == 'en'
                  ? _getFirstAndSecondName(member.nameEn ?? '')
                  : _getFirstAndSecondName(member.nameAr ?? '');

              return Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: borderColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.r),
                  border: Border.all(
                    color: borderColor,
                    width: 1.w,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      name,
                      style: FontUtilities.getTextStyle(
                        TextType.regular,
                        size: 13.sp,
                        textColor: mainTextColor,
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () => onRemove(index),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.sp, vertical: 3.sp),
                        child: Icon(
                          Icons.close,
                          size: 16.sp,
                          color: mainTextColor,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  static String _getFirstAndSecondName(String fullName) {
    final parts = fullName.trim().split(' ');
    if (parts.length >= 2) {
      return '${parts[0]} ${parts[1]}';
    } else if (parts.isNotEmpty) {
      return parts[0];
    } else {
      return '';
    }
  }
}
