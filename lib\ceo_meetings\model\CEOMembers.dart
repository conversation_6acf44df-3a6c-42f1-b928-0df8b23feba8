class CEOMembers {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  dynamic groupName;
  String? membersCount;
  List<ElmGroupMemeb>? elmGroupMemeb;
  String? authkey;
  String? memGroupName;

  CEOMembers(
      {this.createdBy,
        this.createdById,
        this.createdDate,
        this.recId,
        this.groupName,
        this.membersCount,
        this.elmGroupMemeb,
        this.authkey,
        this.memGroupName});

  CEOMembers.fromJson(Map<String, dynamic> json) {
    createdBy = json['createdBy'];
    createdById = json['createdById'];
    createdDate = json['createdDate'];
    recId = json['recId'];
    groupName = json['group_name'];
    membersCount = json['members_count'];
    if (json['elm_group_memeb'] != null) {
      elmGroupMemeb = <ElmGroupMemeb>[];
      json['elm_group_memeb'].forEach((v) {
        elmGroupMemeb!.add(new ElmGroupMemeb.fromJson(v));
      });
    }
    authkey = json['authkey'];
    memGroupName = json['mem_group_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdBy'] = this.createdBy;
    data['createdById'] = this.createdById;
    data['createdDate'] = this.createdDate;
    data['recId'] = this.recId;
    data['group_name'] = this.groupName;
    data['members_count'] = this.membersCount;
    if (this.elmGroupMemeb != null) {
      data['elm_group_memeb'] =
          this.elmGroupMemeb!.map((v) => v.toJson()).toList();
    }
    data['authkey'] = this.authkey;
    data['mem_group_name'] = this.memGroupName;
    return data;
  }
}

class ElmGroupMemeb {
  String? namear;
  String? nameen;
  String? employeeid;
  int? recid;
  String? email;
  bool? isSelected = false;

  ElmGroupMemeb(
      {this.namear, this.nameen, this.employeeid, this.recid, this.email, this.isSelected});

  ElmGroupMemeb.fromJson(Map<String, dynamic> json) {
    namear = json['namear'];
    nameen = json['nameen'];
    employeeid = json['employeeid'];
    recid = json['recid'];
    email = json['email'];
  }


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['namear'] = this.namear;
    data['nameen'] = this.nameen;
    data['employeeid'] = this.employeeid;
    data['recid'] = this.recid;
    data['email'] = this.email;
    return data;
  }
}

extension ElmGroupMemebCopy on ElmGroupMemeb {
  ElmGroupMemeb copyWith({
    String? namear,
  String? nameen,
  String? employeeid,
  int? recid,
  String? email,
  bool? isSelected,
  }) {
    return ElmGroupMemeb(
      namear: namear ?? this.namear,
      nameen: nameen ?? this.nameen,
      employeeid: employeeid ?? this.employeeid,
      recid: recid ?? this.recid,
      email: email ?? this.email,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}