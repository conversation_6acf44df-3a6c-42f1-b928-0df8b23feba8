import '../../shared/widgets/general_text_form_field.dart';

class ProjectWpsResponse {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final String employeeId;
  final String authKey;
  final List<ProjectWps> projectWps;
  ProjectWpsResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeId,
    required this.authKey,
    required this.projectWps,
  });
  factory ProjectWpsResponse.fromJson(Map<String, dynamic> json) {
    return ProjectWpsResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      employeeId: json['employeeid'],
      authKey: json['authkey'],
      projectWps: (json['projectwps'] as List)
          .map((e) => ProjectWps.fromJson(e))
          .toList(),
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'employeeid': employeeId,
      'authkey': authKey,
      'projectwps': projectWps.map((e) => e.toJson()).toList(),
    };
  }
}

class ProjectWps extends GeneralSheetContent {
  final String wbscode;
  final String ownerNameEn;
  final String projectNameEn;
  final int ownerId;
  final int recId;
  final String ownerNameAr;
  ProjectWps({
    required this.wbscode,
    required this.ownerNameEn,
    required this.projectNameEn,
    required this.ownerId,
    required this.recId,
    required this.ownerNameAr,
  }) {
    nameen = projectNameEn;
    extraContent = [];
    extraContent?.add(SheetExtraContent(
        keyEn: 'Project name',
        keyAR: 'اسم المشروع',
        valueAR: projectNameEn,
        valueEn: projectNameEn));
    extraContent?.add(SheetExtraContent(
        keyEn: 'WBS code',
        keyAR: 'كود WBS',
        valueAR: wbscode,
        valueEn: wbscode));
    extraContent?.add(SheetExtraContent(
        keyEn: 'Owner ID',
        keyAR: 'ID المالك',
        valueAR: ownerId.toString(),
        valueEn: ownerId.toString()));
    extraContent?.add(SheetExtraContent(
        keyEn: 'Owner Name',
        keyAR: 'اسم المالك',
        valueAR: ownerNameAr,
        valueEn: ownerNameEn));
  }
  factory ProjectWps.fromJson(Map<String, dynamic> json) {
    return ProjectWps(
      wbscode: json['wbscode'],
      ownerNameEn: json['ownernameen'],
      projectNameEn: json['projectnameen'],
      ownerId: json['ownerid'],
      recId: json['recid'],
      ownerNameAr: json['ownernamear'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'wbscode': wbscode,
      'ownernameen': ownerNameEn,
      'projectnameen': projectNameEn,
      'ownerid': ownerId,
      'recid': recId,
      'ownernamear': ownerNameAr,
    };
  }
}
