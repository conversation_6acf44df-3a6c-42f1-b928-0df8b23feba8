class Loan {
  String? installmentmonths;
  String? startdate;
  String? paidamount;
  String? monthlyinstallmentamount;
  String? enddate;
  String? totalamount;
  String? remainingamount;
  String? currency;
  int? recid;
  String? loanid;

  Loan({
    this.installmentmonths,
    this.startdate,
    this.paidamount,
    this.monthlyinstallmentamount,
    this.enddate,
    this.totalamount,
    this.remainingamount,
    this.currency,
    this.recid,
    this.loanid,
  });

  factory Loan.fromJson(Map<String, dynamic> json) => Loan(
        installmentmonths: json['installmentmonths'] as String?,
        startdate: json['startdate'] as String?,
        paidamount: json['paidamount'] as String?,
        monthlyinstallmentamount: json['monthlyinstallmentamount'] as String?,
        enddate: json['enddate'] as String?,
        totalamount: json['totalamount'] as String?,
        remainingamount: json['remainingamount'] as String?,
        currency: json['currency'] as String?,
        recid: json['recid'] as int?,
        loanid: json['loanid'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'installmentmonths': installmentmonths,
        'startdate': startdate,
        'paidamount': paidamount,
        'monthlyinstallmentamount': monthlyinstallmentamount,
        'enddate': enddate,
        'totalamount': totalamount,
        'remainingamount': remainingamount,
        'currency': currency,
        'recid': recid,
        'loanid': loanid,
      };
}
