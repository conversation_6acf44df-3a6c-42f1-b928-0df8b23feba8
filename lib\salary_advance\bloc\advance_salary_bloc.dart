
import 'dart:convert';
import 'package:eeh/login/models/get_emp_info_response.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../shared/utility/secure_storage.dart';
import '../../shared/widgets/toast.dart';
import '../models/salary_advance_history_model.dart';
import '../models/submit_response_model.dart';
import '../repo/advance_salary_repo.dart';
import 'advance_salary_event.dart';
import 'advance_salary_state.dart';

class AdvancedSalaryBloc extends Bloc<AdvancedSalaryEvent, AdvancedSalaryState> {
  final AdvanceSalaryRepo repo = AdvanceSalaryRepo();
  GetEmployeeInfoResponse? empInfo;
  SalaryAdvanceSubmitResponse? submitResponse;
  SalaryAdvanceHistoryModel? AdvanceSalaryHistory;
  TextEditingController reasonController = TextEditingController();

  AdvancedSalaryBloc() : super(AdvancedSalaryInitialState()) {
    on<AdvancedSalaryHistoryInitialEvent>((event, emit) async {
      await getEmpInfo();
      emit(AdvancedSalaryInitialState());
    //  await fetchSalaryHistory(emit);
    });

    on<LoadAdvancedSalaryHistoryEvent>((event, emit) async {
      emit(AdvancedSalaryLoadingState());
      await fetchSalaryHistory(emit);

    });

    on<SubmitRequestEvent>((event,emit) async{
      emit(SubmitAdvancedSalaryLoadingState());
      await submitAdvanceSalaryRequest(emit);
    });


  }

 fetchSalaryHistory(emitter) async{
    return repo.getAdvanceSalaryHistory(employeeid: empInfo?.employeeid ?? '').then((response)=> onGetAdvanceSalaryHistory(response, emitter));
 }

 void onGetAdvanceSalaryHistory(NetworkResponse response,emitter){
   response.maybeWhen(ok: (data) {
     onGetAdvanceSalaryHistorySuccess(data, emitter);
   },onError: (error){
   showMessage(error.toString(), MessageType.error);
   emitter(AdvancedSalaryHistoryErrorState());
   });
 }
 void onGetAdvanceSalaryHistorySuccess(data, emitter){
   AdvanceSalaryHistory = SalaryAdvanceHistoryModel.fromJson(data);
    emitter(AdvancedSalaryHistorySuccessState(AdvanceSalaryHistory!));
 }

  Future getEmpInfo() async {
    String? jsonString =
    await SecureStorageService().readSecureData(key: "emp_info_response");
    if (jsonString != null) {
      Map<String, dynamic> jsonMap = jsonDecode(jsonString);
      empInfo = GetEmployeeInfoResponse.fromJson(jsonMap);
    }
  }

  submitAdvanceSalaryRequest(emitter) async {
    await repo.submitSalaryAdvanceRequest(reason: reasonController.text).then((response)=> onSubmitRequest( response, emitter));
  }
  
  void onSubmitRequest(NetworkResponse response, emitter){
    response.maybeWhen(ok: (data) {
      onSubmitSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitAdvancedSalaryErrorState());
    });
  }
  
  void onSubmitSuccess(data, emitter){
    submitResponse = SalaryAdvanceSubmitResponse.fromJson(data);
    emitter(SubmitAdvancedSalarySuccessState());
  }
  
  bool isButtonEnable(){
    return (reasonController.text != '' || reasonController.text.isNotEmpty);
  }

}