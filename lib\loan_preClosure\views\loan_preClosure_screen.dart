import 'package:eeh/loan_preClosure/bloc/loan_preclousr_state.dart';
import 'package:eeh/loan_preClosure/model/submit_request_model.dart';
import 'package:eeh/loan_preClosure/views/widget/successScreenContentL.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../shared/app_constants.dart';
import '../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../shared/attachment_uda/widget/attachment_component.dart';
import '../../shared/favorite_component/bloc/favorite_bloc.dart';
import '../../shared/favorite_component/bloc/favorite_event.dart';
import '../../shared/favorite_component/view/favorite_component.dart';
import '../../shared/network/endpoints.dart';
import '../../shared/styles/colors.dart';
import '../../shared/utility/font_utility.dart';
import '../../shared/utility/navigation_utility.dart';
import '../../shared/widgets/bottomSheet_component/showBottomSheet.dart';
import '../../shared/widgets/cancel_button_component.dart';
import '../../shared/widgets/currency_field_component.dart';

import '../../shared/widgets/important_note.dart';
import '../../shared/widgets/new_textformfield_component.dart';
import '../../shared/widgets/sar_component.dart';
import '../../shared/widgets/submit_button_component.dart';
import '../../shared/widgets/toast.dart';
import '../../success_view/success_screen_view.dart';
import '../bloc/loan_preclousr_bloc.dart';
import '../bloc/loan_preclousr_event.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';

class LoanPreClosureScreen extends StatefulWidget {
  const LoanPreClosureScreen({super.key, this.isActiveLoan = false});
  final bool isActiveLoan;
  @override
  State<LoanPreClosureScreen> createState() => _LoanPreClosureScreenState();
}

class _LoanPreClosureScreenState extends State<LoanPreClosureScreen> {
  bool isTextEmpty = true;
  LoanPreClousrBloc? bloc;
  String totalLoan = "0.00";
  String paidAmount = "0.00";
  String remainingAmount = "0.00";
  String monthlyInstallment = "0.00";
  final TextEditingController amountController = TextEditingController(text: " ");
  final TextEditingController ibanController = TextEditingController(text: " ");
  final TextEditingController startDateController = TextEditingController(text: ' ');
  final TextEditingController endDateController = TextEditingController(text: ' ');
  final GlobalKey<FormState>  formKey = GlobalKey<FormState>();
  SubmitRequestResponse? submitResponseL;

  String formatDate(String rawDate) {
    try {
      final DateTime parsedDate = DateTime.parse(rawDate);
      return DateFormat('dd MMM, yyyy').format(parsedDate);
    } catch (e) {
      return rawDate;
    }
  }


  @override
  Widget build(BuildContext context) {


    return MultiBlocProvider(
      providers: [
        BlocProvider<LoanPreClousrBloc>(create: (BuildContext context) {
          return LoanPreClousrBloc()..add(LoanPreClousrInitialEvent());
          // return LoanPreClousrBloc()..add(LoanPreClousrInitialEvent());
        },),
        BlocProvider<FavoriteBloc>(create: (BuildContext context) {
          return FavoriteBloc()..add(FavoriteInitialEvent());
        },),
        BlocProvider<AttachBloc>(
          create: (BuildContext context) {
            return AttachBloc()..add(AttachInitialEvent());
          },
        )
      ],
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: getAppBarWidget(),
          body: BlocConsumer<LoanPreClousrBloc,LoanPreClousrState>(
            listenWhen: (previous, current) =>
            current is LoanPreClousrInitialState,
            listener: (context, state){
              bloc?.add(FetchEmployeeInfoEvent());
            },
            buildWhen:(previous, current) =>
                current is LoanPreClousrInitialState ||
                current is FetchEmployeeInfoErrorState ||
                current is FetchEmployeeInfoSuccessState,
            builder: (context, state){
              bloc = context.read<LoanPreClousrBloc>();
              bloc?.ctx = context;
              if(state is FetchEmployeeInfoSuccessState){
                print('DONE');
                print(bloc?.employeeInfo?.activeLoan);
                final ibanNumber  = bloc?.employeeInfo?.elmibanNumber ?? "0";
                ibanController.text = ibanNumber;
                final loans = bloc?.employeeInfo?.loans ?? [];
                if (loans.isNotEmpty) {
                  totalLoan = loans.first.totalAmount ?? '0';
                  paidAmount = loans.first.paidAmount ?? "0";
                  remainingAmount = loans.first.remainingAmount ?? "0";
                  monthlyInstallment = loans.first.monthlyInstallmentAmount ?? "0";
                  endDateController.text = formatDate(loans.first.endDate ?? "0");
                  startDateController.text = formatDate(loans.first.startDate ?? "0");
                  amountController.text = remainingAmount;
                }

              }

              if(state is LoanPreClousrInitialState ||
                  state is FetchEmployeeInfoLoadingState
              ){
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              else{
                return Form(
                  key: formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Padding(
                          padding:  EdgeInsets.symmetric(horizontal: 18.w),
                          child: Column(
                            children: [
                              SizedBox(height: 22.h,),
                              buildInfoGrid(
                                totalLoan,
                                paidAmount,
                                remainingAmount,
                                monthlyInstallment,
                              ),
                              SizedBox(
                                height: 12.h,
                              ),
                              CurrencyTextFieldComponent(
                                labelText: AppLocalizations.of(context).translate("Loan Pre-Closure Amount"),
                                isDemmed: true,
                                isSARComponentHasBrackets: true,
                                controller: amountController,
                                isReadOnly: true,
                                sARComponentSize: 20.sp,
                                sarColor: secondTextColor,
                              ),

                              NewCustomTextFieldComponent(
                                labelText: AppLocalizations.of(context).translate("ELM IBAN Number"),
                                isDemmed: true,
                                controller: ibanController,


                              ),
                              NewCustomTextFieldComponent(
                                labelText: AppLocalizations.of(context).translate("Start Date"),
                                isDemmed: true,
                                controller: startDateController,
                                type: TextFieldType.calendar,
                                onTap: (){},

                              ),
                              NewCustomTextFieldComponent(
                                labelText: AppLocalizations.of(context).translate("End Date"),
                                isDemmed: true,
                                controller: endDateController,
                                type: TextFieldType.calendar,
                                onTap: (){},

                              ),

                              Padding(
                                padding:  EdgeInsets.symmetric(vertical: 8.h),
                                child: AttachUI(
                                  isRequired: true,
                                  serviceType: 'PersonalLoanperColsure',
                                  color: primaryColor,
                                  endPoint: deleteLoanPreCAttach,
                                  subTitle: 'Click to upload',
                                  label: AppLocalizations.of(context).translate("Attachment"),
                                  onFilesUpdated: (){
                                    bloc?.add(FilesUpdatedEvent());
                                  },

                                ),
                              ),
                              ImportantNote(
                                notes: [
                                  AppLocalizations.of(context).translate("Loan Pre-Closure Note") ,
                                  AppLocalizations.of(context)
                                      .translate("If you settle the loan this month, you'll need to wait until next month to request a new one"),

                                ],
                              ),

                            ],
                          ),
                        ),
                        SizedBox(height: 22.h,),
                        getButton(isTextEmpty,formKey ),
                      ],
                    ),
                  ),
                );

              }
            } ,



          ),
          bottomSheet:BlocBuilder<LoanPreClousrBloc,LoanPreClousrState>(
            builder: (context, state) {
              if ((bloc?.employeeInfo?.activeLoan  ?? true) == false) {
                // hasShownBottomSheet = true;
                Future.delayed(Duration.zero,() {
                  showBottomSheets(
                    context: context,
                    exitText: AppLocalizations.of(context).translate("Exit"),
                    submitText: '',
                    headerText: AppLocalizations.of(context).translate("You do not have an active loan to close"),
                    showSubmitButton: false,
                    enableDrag: false,
                    isDismissible: false,
                    isScrollControlled: true,
                    onTap: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    },
                  );
                });
              }

              return SizedBox.shrink(); // If no condition, return an empty widget
            },
          ),

        ),

      ),

    );
  }


  Widget getButton(bool isTextEmpty, GlobalKey<FormState> formKey) {
    return BlocConsumer<LoanPreClousrBloc, LoanPreClousrState>(
      listener: (context, state)=>
      state is UploadAttachmentFilesSuccessState
          ?  Navigation.navigateToScreen(
        context,
        SuccessScreen(
          title: AppLocalizations.of(context).translate("Request Submitted Successfully"),
          contentWidget: SuccessScreenContentWidgetL(submitResponse: bloc?.submitRequestResponse),
        ),
      ): bloc?.add(UploadAttachmentFilesEvent()),


      listenWhen: (previous, current) =>
      current is UploadAttachmentFilesSuccessState || current is SubmitRequestSuccessState,
      buildWhen: (previous, current) =>
      current is FetchEmployeeInfoSuccessState ||
          current is FilesUpdatedState ||
          current is UploadAttachmentFilesSuccessState ||
          current is SubmitRequestLoadingState ||
          current is SubmitRequestSuccessState ||
          current is SubmitRequestErrorState,

      builder: (context, state) {
        final bool hasFiles = AttachBloc.instance(context).files.isNotEmpty;
        final Color buttonColor = hasFiles&&_hasNonEmptyField() ? primaryColor : secondTextColor.withOpacity(0.1);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            CancelButtonComponent(
              title: AppLocalizations.of(context).translate('Cancel'),

            ),
            SubmitButtonComponent(
              text: AppLocalizations.of(context).translate('Submit'),
              backGroung: buttonColor,
              isLoading: state is SubmitRequestLoadingState || state is  UploadAttachmentLoadingState  ? true : false,
              onPressed: () {
                      if (formKey.currentState!.validate() && hasFiles&&_hasNonEmptyField()) {
                  if(state is! UploadAttachmentLoadingState && state is! SubmitRequestLoadingState){
                    bloc?.add(SubmitRequestEvent());
                  }
                } else if (!hasFiles) {
                  showMessage(
                    AppLocalizations.of(context).translate("Must add at least one attachment"),
                    MessageType.warning,
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }
  bool _hasNonEmptyField()=> amountController.text.trim().isNotEmpty &&
      ibanController.text.trim().isNotEmpty &&
      startDateController.text.trim().isNotEmpty &&
      endDateController.text.trim().isNotEmpty &&
      num.tryParse(amountController.text.trim()) != 0;


  getAppBarWidget() {
    return PreferredSize(
        preferredSize: Size.fromHeight(56.h),
        child: FavoriteComponent(
          downArrowPressed: widget.isActiveLoan
              ? () {
            Navigation.popScreen(
              context,
            );
            Navigation.popScreen(
              context,
            );
            Navigation.popScreen(
              context,
            );
          }
              : null,

          title: AppLocalizations.of(context).translate("Loan Pre-Closure"),
          id: personalLoanPreClosureID,
        )

    );
  }


  Widget  buildInfoGrid(
      String totalLoan ,
      String paidAmount ,
      String remainingAmount ,
      String monthlyInstallment ,

      ) {
    final List<Map<String, String>> info = [
      {'label': "Total Loan", 'value': totalLoan},
      {'label': "Paid", 'value': paidAmount},
      {'label': 'Remaining', 'value': remainingAmount},
      {'label': "Monthly Installment", 'value': monthlyInstallment},
    ];

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      crossAxisSpacing: 15,
      mainAxisSpacing: 25,
      childAspectRatio: 2,
      physics: const NeverScrollableScrollPhysics(),
      children: info.map((item) {
        return Container(

          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: primaryDarkPurple.withOpacity(0.25),

          ),

          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(AppLocalizations.of(context).translate(item['label']!),
                  style: FontUtilities.getTextStyle(
                    TextType.medium,
                    size: 14.sp,
                    textColor: secondTextColor,
                    fontWeight: FontWeight.w400,
                  )),

              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(AppLocalizations.of(context).translate(item['value']!),
                      style: FontUtilities.getTextStyle(
                        TextType.medium,
                        size: 16.sp,
                        textColor: secondTextColor,
                        fontWeight: FontWeight.w500,
                      )),
                  Padding(
                    padding:  EdgeInsets.only(left: 4.w,right: 4.w),
                    child: SARComponent(
                      color: secondTextColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      }).toList(),
    );
  }


}
