import 'dart:convert';

import 'package:eeh/profile/bloc/profile_bloc.dart';
import 'package:eeh/profile/bloc/profile_event.dart';
import 'package:eeh/profile/bloc/profile_state.dart';
import 'package:eeh/shared/app_constants.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/app_bar.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../l10n/app_localizations.dart';
import '../../shared/utility/mangers/suprema_manger.dart';
import '../../shared/utility/methods.dart';
import '../../shared/widgets/screen_actions.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late ProfileBLoc bloc;
  SupremaManager supremaManager = SupremaManager();
  @override
  void dispose() {
    supremaManager.stopSupremaService();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(create: (BuildContext context) {
      return ProfileBLoc()..add(ProfileInitialEvent());
    }, child: BlocBuilder<ProfileBLoc, ProfileState>(builder: (context, state) {
      bloc = context.read<ProfileBLoc>();
      if (state is ProfileInitialState) {
        bloc.add(ProfileLoadingEvent());
      }
      return Scaffold(
        backgroundColor: lightGreyColor,
        appBar: CustomAppBar(
          title: AppLocalizations.of(context).translate('Profile'),
          appbar: AppbarType.titleWithBackArrow,
        ),
        body: SizedBox(
          height: MediaQuery.of(context).size.height,
          child: Column(
            children: [
              Expanded(
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.vertical(
                        bottom: Radius.circular(10.sp),
                      ),
                      child: Image.asset(
                        profileBackgroundImage,
                        fit: BoxFit.cover,
                        width: 375.w,
                        height: 100.h,
                      ),
                    ),
                    Positioned(
                      top: 48.h,
                      width: 375.w,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Column(
                          children: [
                            SizedBox(
                              width: 88.w,
                              height: 88.h,
                              child: ClipOval(
                                clipBehavior: Clip.hardEdge,
                                child: CircleAvatar(
                                    radius: 52.sp,
                                    child: bloc.hasNoImage()
                                        ? SvgPicture.asset(
                                            profileAvatar,
                                            fit: BoxFit.cover,
                                            width: 80.w,
                                            height: 80.h,
                                          )
                                        : bloc.pngBytes != null
                                            ? Image.memory(
                                                bloc.pngBytes!,
                                                fit: BoxFit.fill,
                                                height: 88.h,
                                                width: 88.w,
                                              )
                                            : SizedBox.shrink()),
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Text(
                              bloc.profileInfoResponse?.createdBy ?? '',
                              style: FontUtilities.getTextStyle(
                                TextType.medium,
                                size: 16.sp,
                              ),
                            ),
                            SizedBox(
                              height: 15.h,
                            ),
                            SizedBox(
                                width: 350.w,
                                height: 190.h,
                                child: Card(
                                  margin: EdgeInsets.zero,
                                  color: Colors.white,
                                  shadowColor: Colors.transparent,
                                  surfaceTintColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(4.sp))),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8.w, vertical: 16.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          AppLocalizations.of(context)
                                              .translate(
                                                  'Personal Information'),
                                          style:
                                              FontUtility.getTextStyleForText(
                                            TextType.medium,
                                            textColor: primaryColor,
                                            size: 16,
                                            isBold: true,
                                          ),
                                        ),
                                        SizedBox(
                                          height: 5.h,
                                        ),
                                        const Divider(
                                          color: Color(0xffEEECEC),
                                        ),
                                        SizedBox(
                                          height: 5.h,
                                        ),
                                        Expanded(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              getCardItem(
                                                  userProfileIconImage,
                                                  AppLocalizations.of(context)
                                                      .translate('Full Name'),
                                                  AppLocalizations.appLang ==
                                                          'en'
                                                      ? (bloc.profileInfoResponse
                                                              ?.nameen ??
                                                          '')
                                                      : (bloc.profileInfoResponse
                                                              ?.namear ??
                                                          '')),
                                              getCardItem(
                                                  userEmailAddressIconImage,
                                                  AppLocalizations.of(context)
                                                      .translate('Email'),
                                                  bloc.profileInfoResponse
                                                          ?.employeeemail ??
                                                      ''),
                                              getCardItem(
                                                  userPhoneIconImage,
                                                  AppLocalizations.of(context)
                                                      .translate('Phone'),
                                                  (bloc.profileInfoResponse
                                                                  ?.phone ??
                                                              '')
                                                          .replaceAll(
                                                              "null", '') ??
                                                      ''),
                                              getCardItem(
                                                  userManagerIconImage,
                                                  AppLocalizations.of(context)
                                                      .translate('Manager'),
                                                  bloc.profileInfoResponse
                                                                  ?.manager ==
                                                              null ||
                                                          bloc.profileInfoResponse
                                                                  ?.manager ==
                                                              "null"
                                                      ? ''
                                                      : (bloc.profileInfoResponse!
                                                                  .manager!
                                                                  .split(
                                                                      ' ')[0] +
                                                              ' ' +
                                                              bloc.profileInfoResponse!
                                                                      .manager!
                                                                      .split(
                                                                          ' ')[
                                                                  1]) ??
                                                          ''),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                )),
                            SizedBox(
                              height: 20.h,
                            ),
                            SizedBox(
                                width: 350.w,
                                height: 227.h,
                                child: Card(
                                  margin: EdgeInsets.zero,
                                  color: Colors.white,
                                  shadowColor: Colors.transparent,
                                  surfaceTintColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(4.sp))),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8.w, vertical: 16.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          AppLocalizations.of(context).translate('Access Card'),
                                          style:
                                              FontUtility.getTextStyleForText(
                                            TextType.medium,
                                            textColor: primaryColor,
                                            size: 16,
                                            isBold: true,
                                          ),
                                        ),
                                        SizedBox(
                                          height: 5.h,
                                        ),
                                        const Divider(
                                          color: Color(0xffEEECEC),
                                        ),
                                        SizedBox(
                                          height: 5.h,
                                        ),
                                        Expanded(
                                          child: supremaManager.isActive
                                              ? Center(
                                                  child: Image.asset(
                                                    "assets/images/Mobile Holder.gif",
                                                    width: 150.w,
                                                    height: 150.h,
                                                  ),
                                                )
                                              : Center(
                                                  child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            "please click open door when you are at the gate"),
                                                    style: FontUtilities
                                                        .getTextStyle(
                                                      TextType.regular,
                                                      size: 12.sp,
                                                    ),
                                                  ),
                                                  // child: supremaManager.isActive
                                                  //  ? Image.asset("assets/images/Mobile Holder.gif",width: 150.w,height: 150.h,)
                                                  //  : Image.asset('assets/images/frame.png',
                                                  //     fit: BoxFit.fill,
                                                  //     width: 129.h,
                                                  //     height: 129.h),
                                                ),
                                        )
                                      ],
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              ScreenActions(
                firstButtonCaption:
                    supremaManager.isActive ? AppLocalizations.of(context).translate("Close door") : AppLocalizations.of(context).translate("Open door"),
                isSingleButton: true,
                onFirstButtonTap: () async {
                  // Navigator.push(context,MaterialPageRoute(builder:
                  // (context) =>  AccessMachine(cardId: bloc.cardInfo?.cardid??'999991',),));
                  if (supremaManager.isActive) {
                    supremaManager.stopSupremaService();
                    setState(() {});
                  } else {
                    try {
                      await supremaManager
                          .startSupremaService(bloc.cardInfo?.cardid ?? '')
                          .then((value) {
                        setState(() {});
                      });
                    } catch (e) {
                      showMessage(e.toString(), MessageType.error);
                    }
                  }
                },
              )
            ],
          ),
        ),
      );
    }));
  }

  Widget getCardItem(String img, String key, String value) {
    return Row(
      children: [
        SvgPicture.asset(img),
        SizedBox(
          width: 13.w,
        ),
        Text(
          key,
          style: FontUtilities.getTextStyle(
            TextType.disable,
            size: 14,
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: FontUtilities.getTextStyle(TextType.regular, size: 14),
        )
      ],
    );
  }
}
