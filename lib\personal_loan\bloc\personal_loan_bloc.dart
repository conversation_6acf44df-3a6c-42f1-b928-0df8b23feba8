import 'package:bloc/bloc.dart';
import 'package:eeh/personal_loan/models/submit_personal_loan_request_body.dart';
import 'package:eeh/personal_loan/repo/personal_loan_repo.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../login/models/get_emp_info_response.dart';
import '../../shared/network/api_helper.dart';
import '../../shared/utility/methods.dart';
import '../../shared/widgets/toast.dart';
import '../models/employee_loan_info_response_model/employee_loan_info_response_model.dart';
import '../models/submit_presonal_loan_response_model/submit_presonal_loan_response_model.dart';

part 'personal_loan_event.dart';
part 'personal_loan_state.dart';

class PersonalLoanBloc extends Bloc<PersonalLoanEvent, PersonalLoanState> {
  GetEmployeeInfoResponse? userData;
  EmployeeLoanInfoResponseModel? employeeLoanInfoResponseModel;
  SubmitPersonalLoanResponseModel? submitPersonalLoanResponseModel;
  SubmitPersonalLoanRequestBody? submitPersonalLoanRequestBody;
  late IPersonalLoanRepo _repo;
  TextEditingController loanAmountController = TextEditingController();
  TextEditingController monthlyInstallmentsAmountController =
      TextEditingController();
  final formKey = GlobalKey<FormState>();
  bool isAcceptTermsAndConditions = false;
  bool hasActiveLoan = false;

  PersonalLoanBloc({required PersonalLoanRepo personalRepo})
      : super(PersonalLoanInitial()) {
    _repo = personalRepo;
    on<PersonalLoanInitialEvent>((event, emit) async {
      userData = await getEmpInfo();
      emit(PersonalLoanInitialFinishState());
    });
    on<GetEmployeeLoanInfoEvent>(_getEmployeeLoanInfo);
    on<SubmitPersonalLoanEvent>(_submitPersonalLoan);
  }
  Future _getEmployeeLoanInfo(
      GetEmployeeLoanInfoEvent event, Emitter<PersonalLoanState> emit) async {
    emit(GetPersonalLoanInitialDataLoadingState());
    await _repo
        .getEmpLoanInfo(empId: userData?.employeeid ?? '')
        .then((response) => onGetEmployeeLoanInfoResponse(response, emit));
  }

  void onGetEmployeeLoanInfoResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetEmployeeLoanInfoSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(GetPersonalLoanInitialDataErrorState());
    });
  }

  void onGetEmployeeLoanInfoSuccess(data, Emitter emitter) {
    EmployeeLoanInfoResponseModel response =
        EmployeeLoanInfoResponseModel.fromJson(data);
    hasActiveLoan = bool.tryParse(response.activeloan!.toLowerCase()) ?? false;

    emitter(GetPersonalLoanInitialDataSuccessState(
        employeeLoanInfoResponseModel: response));
  }

  Future _submitPersonalLoan(
      SubmitPersonalLoanEvent event, Emitter<PersonalLoanState> emit) async {
    emit(SubmitPersonalLoanLoadingState());
    await _repo
        .submitPersonalEmpLoanInfo(
            submitPersonalLoanRequestBody: SubmitPersonalLoanRequestBody(
          loanamount: loanAmountController.text,
          maximumloanlimi: "120000",
          numberofinstall: "12",
          policyapprove: isAcceptTermsAndConditions.toString(),
          monthinstallval: monthlyInstallmentsAmountController.text,
        ))
        .then(
            (response) => onSubmitPersonalEmpLoanInfoResponse(response, emit));
  }

  void onSubmitPersonalEmpLoanInfoResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitPersonalEmpLoanInfoSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitPersonalLoanErrorState());
    });
  }

  void onSubmitPersonalEmpLoanInfoSuccess(data, Emitter emitter) {
    SubmitPersonalLoanResponseModel response =
        SubmitPersonalLoanResponseModel.fromJson(data);
    emitter(SubmitPersonalLoanSuccessState(
        submitPersonalLoanResponseModel: response));
  }
}
