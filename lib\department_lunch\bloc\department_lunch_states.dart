import '../../shared/utility/methods.dart';

abstract class DepartmentLunchRequestState {}

class DepLunchInitialState extends DepartmentLunchRequestState {}

class DepLunchInitialFinishState extends DepartmentLunchRequestState {}

class ChangeFavoriteStatusState extends DepartmentLunchRequestState {}

class GetDepartmentEmployeesLoadingState extends DepartmentLunchRequestState {}

class GetDepartmentEmployeesSuccessState extends DepartmentLunchRequestState {}

class GetDepartmentEmployeesErrorState extends DepartmentLunchRequestState {}

class GetCorporateEmployeesLoadingState extends DepartmentLunchRequestState {}

class GetCorporateEmployeesSuccessState extends DepartmentLunchRequestState {}

class GetCorporateEmployeesErrorState extends DepartmentLunchRequestState {}

class UpdateSelectedDepartmentEmployeesState
    extends DepartmentLunchRequestState {}

class UpdateSelectedCorporateEmployeesState
    extends DepartmentLunchRequestState {}

class UploadAttachmentLoadingState extends DepartmentLunchRequestState {}

class UploadAttachmentFilesSuccessState extends DepartmentLunchRequestState {}

class UpdateCheckboxStatusState extends DepartmentLunchRequestState {}

class UpdateInvoiceAmountValueState extends DepartmentLunchRequestState {}

class UpdateNoteValueState extends DepartmentLunchRequestState {}

class SubmitRequestLoadingState extends DepartmentLunchRequestState {}

class SubmitRequestSuccessState extends DepartmentLunchRequestState {}

class SubmitRequestErrorState extends DepartmentLunchRequestState {
  SubmitRequestErrorState(){
    performFailureVibration();
  }
}

class FilesUpdatedState extends DepartmentLunchRequestState {}

class ChangeLunchMonthDateState extends DepartmentLunchRequestState {}

class SearchInDepartmentEmployeesState extends DepartmentLunchRequestState {}

class SearchInCorporateEmployeesState extends DepartmentLunchRequestState {}
