import 'dart:convert';

import 'package:eeh/lms_services/models/my_team_learning/my_team_lms_data.dart';

class MyTeamLearningResponseModel {
  final String lmsGetManagerTeamLearningV1;

  MyTeamLearningResponseModel({
    required this.lmsGetManagerTeamLearningV1,
  });

  factory MyTeamLearningResponseModel.fromJson(Map<String, dynamic> json) {
    return MyTeamLearningResponseModel(
      lmsGetManagerTeamLearningV1: json['lms_get_manager_team_learning_v1'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lms_get_manager_team_learning_v1': lmsGetManagerTeamLearningV1,
    };
  }

  MyTeamLmsData? getParsedData() {
    if (lmsGetManagerTeamLearningV1.isEmpty) return null;
    
    try {
      final decodedJson = jsonDecode(lmsGetManagerTeamLearningV1);
      return MyTeamLmsData.fromJson(decodedJson);
    } catch (e) {
      print('Error parsing LMS data: $e');
      return null;
    }
  }
}



