import 'package:eeh/lms_services/models/my_team_learning/employee_data.dart';
import 'package:eeh/lms_services/models/my_team_learning/my_team_dashboard_kpi.dart';


class MyTeamLmsData {
  final List<EmployeeData> itemDetails;
  final List<MyTeamDashboardKpi> dashboardKpis;

  MyTeamLmsData({
    required this.itemDetails,
    required this.dashboardKpis,
  });

  factory MyTeamLmsData.fromJson(List<dynamic> json) {
    List<EmployeeData> itemDetails = [];
    List<MyTeamDashboardKpi> dashboardKpis = [];

    for (var item in json) {
      if (item is Map<String, dynamic>) {
        // Parse item_details
        if (item.containsKey('item_details') && item['item_details'] is List) {
          itemDetails = (item['item_details'] as List)
              .map((e) => EmployeeData.fromJson(e as Map<String, dynamic>))
              .toList();
        }
        
        // Parse dashboard_kpi
        if (item.containsKey('dashboard_kpi') && item['dashboard_kpi'] is List) {
          dashboardKpis = (item['dashboard_kpi'] as List)
              .map((e) => MyTeamDashboardKpi.fromJson(e as Map<String, dynamic>))
              .toList();
        }
      }
    }

    return MyTeamLmsData(
      itemDetails: itemDetails,
      dashboardKpis: dashboardKpis,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'item_details': itemDetails.map((e) => e.toJson()).toList(),
      'dashboard_kpi': dashboardKpis.map((e) => e.toJson()).toList(),
    };
  }
}
