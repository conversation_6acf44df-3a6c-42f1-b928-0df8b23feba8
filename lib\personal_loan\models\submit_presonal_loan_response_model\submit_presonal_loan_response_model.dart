import 'meeting_parameters.dart';

class SubmitPersonalLoanResponseModel {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  // MeetingParameters? meetingParameters;
  String? requesteremail;
  String? loanamount;
  dynamic exception;
  String? decKey;
  dynamic reqStatusAr;
  dynamic approvedByAr;
  String? mytypeid;
  List<dynamic>? nextapprovallis;
  dynamic rejectionreason;
  dynamic requestDetails;
  dynamic reqCardData;
  dynamic notes;
  String? requeststatus;
  String? requestTypeAr;
  String? activeloan;
  String? numberofinstall;
  dynamic taskWebUrl;
  List<dynamic>? attachments;
  dynamic resultmessageen;
  dynamic withdrawreason;
  String? requestType;
  String? requesternameen;
  dynamic resultmessage;
  dynamic resultmessagear;
  String? pendingRequest;
  dynamic code;
  String? maximumloanlimi;
  dynamic rejectedbyar;
  String? policyapprove;
  dynamic withdrawflag;
  String? createddate;
  dynamic maxloanvalue;
  String? tableName;
  String? elmWebUrl;
  dynamic approvedByEn;
  String? requesternamear;
  dynamic rejectedbyen;
  String? employeeid;
  String? serviceKey;
  String? userid;
  String? display_recid;

  SubmitPersonalLoanResponseModel({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    // this.meetingParameters,
    this.requesteremail,
    this.loanamount,
    this.exception,
    this.decKey,
    this.reqStatusAr,
    this.approvedByAr,
    this.mytypeid,
    this.nextapprovallis,
    this.rejectionreason,
    this.requestDetails,
    this.reqCardData,
    this.notes,
    this.requeststatus,
    this.requestTypeAr,
    this.activeloan,
    this.numberofinstall,
    this.taskWebUrl,
    this.attachments,
    this.resultmessageen,
    this.withdrawreason,
    this.requestType,
    this.requesternameen,
    this.resultmessage,
    this.resultmessagear,
    this.pendingRequest,
    this.code,
    this.maximumloanlimi,
    this.rejectedbyar,
    this.policyapprove,
    this.withdrawflag,
    this.createddate,
    this.maxloanvalue,
    this.tableName,
    this.elmWebUrl,
    this.approvedByEn,
    this.requesternamear,
    this.rejectedbyen,
    this.employeeid,
    this.serviceKey,
    this.userid,
    this.display_recid,
  });

  factory SubmitPersonalLoanResponseModel.fromJson(Map<String, dynamic> json) {
    return SubmitPersonalLoanResponseModel(
      createdBy: json['createdBy'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      recId: json['recId'] as int?,
      // meetingParameters: json['meetingParameters'] == null
      //     ? null
      //     : MeetingParameters.fromJson(
      //         json['meetingParameters'] as Map<String, dynamic>),
      requesteremail: json['requesteremail'] as String?,
      loanamount: json['loanamount'] as String?,
      exception: json['exception'] as dynamic,
      decKey: json['dec_key'] as String?,
      reqStatusAr: json['req_status_ar'] as dynamic,
      approvedByAr: json['approved_by_ar'] as dynamic,
      mytypeid: json['mytypeid'] as String?,
      nextapprovallis: json['nextapprovallis'] as List<dynamic>?,
      rejectionreason: json['rejectionreason'] as dynamic,
      requestDetails: json['request_details'] as dynamic,
      reqCardData: json['req_card_data'] as dynamic,
      notes: json['notes'] as dynamic,
      requeststatus: json['requeststatus'] as String?,
      requestTypeAr: json['request_type_ar'] as String?,
      activeloan: json['activeloan'] as String?,
      numberofinstall: json['numberofinstall'] as String?,
      taskWebUrl: json['task_web_url'] as dynamic,
      attachments: json['attachments'] as List<dynamic>?,
      resultmessageen: json['resultmessageen'] as dynamic,
      withdrawreason: json['withdrawreason'] as dynamic,
      requestType: json['request_type'] as String?,
      requesternameen: json['requesternameen'] as String?,
      resultmessage: json['resultmessage'] as dynamic,
      resultmessagear: json['resultmessagear'] as dynamic,
      pendingRequest: json['pending_request'] as String?,
      code: json['code'] as dynamic,
      maximumloanlimi: json['maximumloanlimi'] as String?,
      rejectedbyar: json['rejectedbyar'] as dynamic,
      policyapprove: json['policyapprove'] as String?,
      withdrawflag: json['withdrawflag'] as dynamic,
      createddate: json['createddate'] as String?,
      maxloanvalue: json['maxloanvalue'] as dynamic,
      tableName: json['table_name'] as String?,
      elmWebUrl: json['elm_web_url'] as String?,
      approvedByEn: json['approved_by_en'] as dynamic,
      requesternamear: json['requesternamear'] as String?,
      rejectedbyen: json['rejectedbyen'] as dynamic,
      employeeid: json['employeeid'] as String?,
      serviceKey: json['service_key'] as String?,
      userid: json['userid'] as String?,
      display_recid: json['display_recid'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'createdBy': createdBy,
        'createdById': createdById,
        'createdDate': createdDate,
        'recId': recId,
        // 'meetingParameters': meetingParameters?.toJson(),
        'requesteremail': requesteremail,
        'loanamount': loanamount,
        'exception': exception,
        'dec_key': decKey,
        'req_status_ar': reqStatusAr,
        'approved_by_ar': approvedByAr,
        'mytypeid': mytypeid,
        'nextapprovallis': nextapprovallis,
        'rejectionreason': rejectionreason,
        'request_details': requestDetails,
        'req_card_data': reqCardData,
        'notes': notes,
        'requeststatus': requeststatus,
        'request_type_ar': requestTypeAr,
        'activeloan': activeloan,
        'numberofinstall': numberofinstall,
        'task_web_url': taskWebUrl,
        'attachments': attachments,
        'resultmessageen': resultmessageen,
        'withdrawreason': withdrawreason,
        'request_type': requestType,
        'requesternameen': requesternameen,
        'resultmessage': resultmessage,
        'resultmessagear': resultmessagear,
        'pending_request': pendingRequest,
        'code': code,
        'maximumloanlimi': maximumloanlimi,
        'rejectedbyar': rejectedbyar,
        'policyapprove': policyapprove,
        'withdrawflag': withdrawflag,
        'createddate': createddate,
        'maxloanvalue': maxloanvalue,
        'table_name': tableName,
        'elm_web_url': elmWebUrl,
        'approved_by_en': approvedByEn,
        'requesternamear': requesternamear,
        'rejectedbyen': rejectedbyen,
        'employeeid': employeeid,
        'service_key': serviceKey,
        'userid': userid,
        'display_recid': display_recid,
      };
}
