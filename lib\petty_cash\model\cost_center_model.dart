class EmployeeBankDetails {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final Map<String, dynamic>? meetingParameters;
  final String? bankNameAr;
  final String? employeeName;
  final String? iban;
  final List<CostCenter>? costCenter;
  final String? position;
  final String? currentBankKey;
  final String? employeeId;
  final String? personalArea;
  final String? employeeGroup;
  final List<PettyCashUsage>? pettyCashUsage;
  final String? orgUnit;
  final String? bankNameEn;

  EmployeeBankDetails({
     this.createdBy,
     this.createdById,
     this.createdDate,
     this.recId,
     this.meetingParameters,
     this.bankNameAr,
     this.employeeName,
     this.iban,
     this.costCenter,
     this.position,
     this.currentBankKey,
     this.employeeId,
     this.personalArea,
     this.employeeGroup,
     this.pettyCashUsage,
     this.orgUnit,
     this.bankNameEn,
  });

  factory EmployeeBankDetails.fromJson(Map<String, dynamic> json) {
    return EmployeeBankDetails(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      meetingParameters: json['meetingParameters'] ?? {},
      bankNameAr: json['banknamear'],
      employeeName: json['employeename'],
      iban: json['iban'],
      costCenter: (json['costcenter'] as List)
          .map((e) => CostCenter.fromJson(e))
          .toList(),
      position: json['position'],
      currentBankKey: json['currentbankkey'],
      employeeId: json['employeeid'],
      personalArea: json['personalarea'],
      employeeGroup: json['employegroup'],
      pettyCashUsage: (json['pettycashusage'] as List)
          .map((e) => PettyCashUsage.fromJson(e))
          .toList(),
      orgUnit: json['orgunit'],
      bankNameEn: json['banknameen'],
    );
  }
}
class CostCenter {
  final String? costCenterCode;
  final String? responsiblePersonNameAr;
  final String? shortText;
  final String? accountingObjectDescription;
  final String? responsiblePersonId;
  final String? companyDetail;
  final String? companyCode;
  final String? costCenterCat;
  final String? controllingArea;
  final String? validFrom;
  final String? responsiblePersonNameEn;
  final String? isDefault;
  final int? recId;
  final String? validTo;

  CostCenter({
     this.costCenterCode,
     this.responsiblePersonNameAr,
     this.shortText,
     this.accountingObjectDescription,
     this.responsiblePersonId,
     this.companyDetail,
     this.companyCode,
     this.costCenterCat,
     this.controllingArea,
     this.validFrom,
     this.responsiblePersonNameEn,
     this.isDefault,
     this.recId,
     this.validTo,
  });

  factory CostCenter.fromJson(Map<String, dynamic> json) {
    return CostCenter(
      costCenterCode: json['costcentercode'],
      responsiblePersonNameAr: json['responsiblepersonnamear'],
      shortText: json['shorttext'],
      accountingObjectDescription: json['accountingobjectdescription'],
      responsiblePersonId: json['responsiblepersonid'],
      companyDetail: json['companydetail'],
      companyCode: json['companycode'],
      costCenterCat: json['costcentercat'],
      controllingArea: json['controllingarea'],
      validFrom: json['validfrom'],
      responsiblePersonNameEn: json['responsiblepersonnameen'],
      isDefault: json['isdefault'],
      recId: json['recid'],
      validTo: json['validto'],
    );
  }
}
class PettyCashUsage {
  final int? cashUsageId;
  final String? cashUsageNameAr;
  final String? cashUsageNameEn;
  final int? recId;

  PettyCashUsage({
     this.cashUsageId,
     this.cashUsageNameAr,
     this.cashUsageNameEn,
     this.recId,
  });

  factory PettyCashUsage.fromJson(Map<String, dynamic> json) {
    return PettyCashUsage(
      cashUsageId: json['cashusageid'],
      cashUsageNameAr: json['cashusagenamear'],
      cashUsageNameEn: json['cashusagenameen'],
      recId: json['recid'],
    );
  }
}
