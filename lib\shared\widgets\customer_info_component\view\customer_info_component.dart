import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/widgets/customer_info_component/bloc/customer_info_bloc.dart';
import 'package:eeh/shared/widgets/customer_info_component/bloc/customer_info_event.dart';
import 'package:eeh/shared/widgets/customer_info_component/bloc/customer_info_state.dart';
import 'package:eeh/shared/widgets/customer_info_component/model/customer_Info_model.dart';
import 'package:eeh/shared/widgets/customer_info_component/view/customer_info_bottom_sheet.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../l10n/app_localizations.dart';
import '../../new_textformfield_component.dart';

class CustomerInfoComponent extends StatefulWidget {
  final TextEditingController controller;
  final String appKey;
  final Function(CustomerInfo) onSelect;
  const CustomerInfoComponent({
    super.key,
    required this.appKey,
    required this.controller,
    required this.onSelect,
  });

  @override
  State<CustomerInfoComponent> createState() => _CustomerInfoComponentState();
}

class _CustomerInfoComponentState extends State<CustomerInfoComponent> {
  CustomerInfoResponse? customerInfoResponse;
  List<CustomerInfo> customerList = [];
  CustomerInfoBloc? bloc;
  final TextEditingController searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return BlocProvider<CustomerInfoBloc>(
      create: (context) => CustomerInfoBloc()..add(CustomerListInitialEvent()),
      child: BlocConsumer<CustomerInfoBloc, CustomerInfoState>(
        listener: (context, state) {
          if (state is CustomerListInitialState) {
            bloc?.add(CustomerListEvent(widget.appKey, '', true));
          }
        },
        buildWhen: (previous, current) =>
        current is CustomerListInitialState ||
            current is CustomerListSuccessState ||
            current is CustomerListLoadingState ||
            current is SearchSuccessState ||
            current is SearchLoadingState ||
            current is SearchErrorState,
        builder: (context, state) {
          bloc = context.read<CustomerInfoBloc>();
          return NewCustomTextFieldComponent(
            labelText: AppLocalizations.of(context).translate("Customer Name"),
            controller: widget.controller,
            keyboardType: TextInputType.none,
            isReadOnly: true,
            isMandatory: true,
            type: TextFieldType.bottomSheet,
            bottomSheetHeight: 0.85,
            onSelect: (value) {
              widget.onSelect(value);
              bloc?.customerInfo = value;
              setState(() {});
            },
            isLoading: state is CustomerListLoadingState,
            validationText: AppLocalizations.of(context)
                .translate("must select Customer Name"),
            bottomSheetBody: _getBottomSheetBody(),
          );
        },
      ),
    );
  }

  Widget _getBottomSheetBody() {
    return BlocBuilder<CustomerInfoBloc, CustomerInfoState>(
        buildWhen: (previous, current) =>
        current is CustomerListSuccessState ||
            current is CustomerListLoadingState ||
            current is SearchLoadingState ||
            current is CustomerListErrorState ||
            current is SearchErrorState ||
            current is SearchSuccessState,
        bloc: bloc,
        builder: (context, state) {
          return CustomerInfoBottomSheet(
            isIconButtonLoading: state is CustomerListLoadingState,
            isApiLoading: state is SearchLoadingState,
            searchController: searchController,
            onSearchPressed: () {
              bloc?.add(SearchEvent(widget.appKey, searchController.text));
            },
            onDownArrowPressed: () => bloc?.add(
                CustomerListEvent(widget.appKey, searchController.text, false)),
            contentList: bloc?.customerInfoList ?? [],
            title: AppLocalizations.of(context).translate("Customer Name"),
            onSelection: (value) {
              widget.onSelect(value);
              bloc?.customerInfo = value;
              setState(() {});
              widget.controller.text = AppLocalizations.appLang == 'en'
                  ? value.nameen ?? value.namear
                  : value.namear ?? value.nameen;
              Navigation.popScreen(context);
            },
          );
        });
  }
}
