import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/lms_services/bloc/bloc/lms_bloc.dart';
import 'package:eeh/lms_services/widgets/stat_card.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

enum LearningItemType {
  course,
  quiz,
  certificate,
  unknown;

  static LearningItemType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'course':
        return LearningItemType.course;
      case 'quiz':
        return LearningItemType.quiz;
      case 'certificate':
        return LearningItemType.certificate;
      default:
        return LearningItemType.unknown;
    }
  }
}

enum LearningStatus {
  completed,
  planned,
  inProgress,
  overdue,
  unknown;

  static LearningStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return LearningStatus.completed;
      case 'planned':
        return LearningStatus.planned;
      case 'inprogress':
      case 'in progress':
        return LearningStatus.inProgress;
      case 'overdue':
        return LearningStatus.overdue;
      default:
        return LearningStatus.unknown;
    }
  }
}

class LmsUtils {
  // static const String baseUrl = 'https://api.365.systems/odata/v2';
  // static const String username = 'elme_full';
  // static const String password = 'f414bbe5-88c6-4ee1-939c-4813cbb7e0a3';
  // // static const String defaultUserLoginName = 'i:0#.f|membership|<EMAIL>';
  // static const String defaultUserLoginName =
  //     'i:0#.f|membership|<EMAIL>';
  // static const String testManagerId = '030d48c3-5ae2-4db3-61f5-08dd88911827';

  // Define course status constants for better readability
  static const String STATUS_COMPLETED = 'completed';
  static const String STATUS_IN_PROGRESS = 'inprogress';
  static const String STATUS_OVERDUE = 'overdue';
  static const String STATUS_CERTIFICATE = 'certificate';
  static const String STATUS_PLANNED = 'planned';

  // Tab indices
  static const int TAB_TOTAL = 0;
  static const int TAB_IN_COURSES = 1;
  static const int TAB_QUIZZES = 2;
  static const int TAB_IN_PROGRESS = 3;
  static const int TAB_COMPLETED = 4;
  static const int TAB_OVERDUE = 5;
  static const int TAB_CERTIFICATES = 6;
  static const int TAB_PLANNED = 7;

  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case STATUS_COMPLETED:
        return approvedColor;
      case STATUS_IN_PROGRESS:
        return primaryColor;
      case STATUS_PLANNED:
        return darkPurpleTextColor;
      case STATUS_OVERDUE:
        return alertError;
      default:
        return textSecondary;
    }
  }

  static String getStatusText(BuildContext context, String status) {
    switch (status.toLowerCase()) {
      case STATUS_PLANNED:
        return AppLocalizations.of(context).translate('Planned');
      case STATUS_COMPLETED:
        return AppLocalizations.of(context).translate('Completed');
      case STATUS_IN_PROGRESS:
        return AppLocalizations.of(context).translate('In Progress');
      case STATUS_CERTIFICATE:
        return AppLocalizations.of(context).translate('Certificate');
      case STATUS_OVERDUE:
        return AppLocalizations.of(context).translate('Overdue');
      default:
        return AppLocalizations.of(context).translate('Valid');
    }
  }

  static String getActionButtonText(BuildContext context, String status) {
    switch (status.toLowerCase()) {
      case STATUS_COMPLETED:
        return AppLocalizations.of(context).translate('View Certificate');
      case STATUS_IN_PROGRESS:
        return AppLocalizations.of(context).translate('Continue');
      case STATUS_PLANNED:
        return AppLocalizations.of(context).translate('Start');
      case STATUS_OVERDUE:
        return AppLocalizations.of(context).translate('Resume');
      default:
        return AppLocalizations.of(context).translate('View');
    }
  }

  static String formatDate(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      const List<String> months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];

      return "${date.day} ${months[date.month - 1]}, ${date.year}";
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error formatting date: $e');
      }
      return dateString;
    }
  }

  static List<StatisticsCard> getStatisticsCards(
      BuildContext context,
      LmsState state, {
      String selectedFilter = 'Total',
      Function(String)? onFilterChanged,
    }) {
    final lmsBloc = context.read<LmsBloc>();
    final dashboardKpi =
        lmsBloc.myLearningResponseModel?.learningData.dashboardKpi;
    return [
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Total'),
        count: dashboardKpi?.totalCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Total',
        onTap: () => onFilterChanged?.call('Total'),
        icon: getIconFromCss("fa-solid fa-book"),
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Courses'),
        count: dashboardKpi?.coursesCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Courses',
        onTap: () => onFilterChanged?.call('Courses'),
        icon: getIconFromCss("fa-solid fa-airplay"),
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Quizzes'),
        count: dashboardKpi?.quizzesCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Quizzes',
        onTap: () => onFilterChanged?.call('Quizzes'),
        icon: getIconFromCss("fa-regular fa-clipboard-list-check"),
        iconColor: darkPurpleTextColor,
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('In Progress'),
        count: dashboardKpi?.inProgressCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'In Progress',
        onTap: () => onFilterChanged?.call('In Progress'),
        icon: getIconFromCss("fa-solid fa-timer"),
        iconColor: primaryColor,
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Completed'),
        count: dashboardKpi?.completedCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Completed',
        onTap: () => onFilterChanged?.call('Completed'),
        icon: getIconFromCss("fa-regular fa-circle-check"),
        iconColor: approvedColor,
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Overdue'),
        count: dashboardKpi?.overdueCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Overdue',
        onTap: () => onFilterChanged?.call('Overdue'),
        icon: getIconFromCss("fa-solid fa-hourglass-clock"),
        iconColor: alertError,
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Certificates'),
        count: dashboardKpi?.certificatesCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Certificates',
        onTap: () => onFilterChanged?.call('Certificates'),
        icon: getIconFromCss("fa-regular fa-file-certificate"),
        iconColor: darkPrimary,
      ),
      StatisticsCard(
        title: AppLocalizations.of(context).translate('Planned'),
        count: dashboardKpi?.plannedCount ?? 0,
        percentage: 10,
        isSelected: selectedFilter == 'Planned',
        onTap: () => onFilterChanged?.call('Planned'),
        icon: getIconFromCss("fa-regular fa-calendar-range"),
        iconColor: darkPurpleTextColor,
      ),
    ];
  }

  static List<dynamic> getFilteredCourses(
    List<dynamic> allCourses,
    String selectedFilter
  ) {
    switch (selectedFilter) {
      case 'Total':
        return allCourses;
      case 'Courses':
        return allCourses.where((course) =>
          course.type.toLowerCase() == 'course').toList();
      case 'Quizzes':
        return allCourses.where((course) =>
          course.type.toLowerCase() == 'quiz').toList();
      case 'In Progress':
        return allCourses.where((course) =>
          course.status.toLowerCase() == 'inprogress' ||
          course.status.toLowerCase() == 'in progress').toList();
      case 'Completed':
        return allCourses.where((course) =>
          course.status.toLowerCase() == 'completed').toList();
      case 'Overdue':
        return allCourses.where((course) =>
          course.status.toLowerCase() == 'overdue').toList();
      case 'Certificates':
        return allCourses.where((course) =>
          course.type.toLowerCase() == 'certificate').toList();
      case 'Planned':
        return allCourses.where((course) =>
          course.status.toLowerCase() == 'planned').toList();
      default:
        return allCourses;
    }
  }

  static String getFilterDisplayName(BuildContext context, String filter) {
    switch (filter) {
      case 'Total':
        return AppLocalizations.of(context).translate('Total');
      case 'Courses':
        return AppLocalizations.of(context).translate('Courses');
      case 'Quizzes':
        return AppLocalizations.of(context).translate('Quizzes');
      case 'In Progress':
        return AppLocalizations.of(context).translate('In Progress');
      case 'Completed':
        return AppLocalizations.of(context).translate('Completed');
      case 'Overdue':
        return AppLocalizations.of(context).translate('Overdue');
      case 'Certificates':
        return AppLocalizations.of(context).translate('Certificates');
      case 'Planned':
        return AppLocalizations.of(context).translate('Planned');
      default:
        return filter;
    }
  }
}
