import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
 import '../../../l10n/app_localizations.dart';
import '../../styles/colors.dart';
import '../../utility/font_utility.dart';
import '../submit_button_component.dart';
import 'cancelButton_bottomSheet.dart';

class BottomsheetContant extends StatefulWidget {
  final String  exitText;
  final String  submitText;
  final String headerText;
  final String?  subText;
  final bool showSubmitButton;
  final Function() onTap;
  const BottomsheetContant({super.key, required this.exitText, required this.submitText, required this.headerText, this.subText,this.showSubmitButton = true, required this.onTap,});

  @override
  State<BottomsheetContant> createState() => _BottomsheetContantState();
}

class _BottomsheetContantState extends State<BottomsheetContant> {
  @override
  Widget build(BuildContext context) {
    return  Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding:  EdgeInsets.symmetric(vertical: 4.h,horizontal: 16.w),
          child: Divider(
            thickness: 3,
            endIndent: 150.w,
            indent: 150.w,
            color: secondTextColor,
          ),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal:16.w,vertical: 12.h),
          child: FaIcon(FontAwesomeIcons.circleInfo,
              color: darkbrone, size: 36.w),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal:16.w),
          child: Text(
            AppLocalizations.of(context).translate(widget.headerText),
            style: FontUtilities.getTextStyle(
                TextType.medium,
                size: 16.sp,
                textColor: textMain,
                fontWeight: FontWeight.w500
            ),
            textAlign: TextAlign.center,
          ),
        ),

        Padding(
          padding: EdgeInsets.symmetric(horizontal:16.w,vertical: 12.h),
          child: Text(
            AppLocalizations.of(context).translate(widget.subText ?? ' ' ),
            style: FontUtilities.getTextStyle(
                TextType.medium,
                size: 14.sp,
                textColor: secondTextColor,
                fontWeight: FontWeight.w400
            ),
            textAlign: TextAlign.center,
            softWrap: true,
            overflow: TextOverflow.visible,
          ),
        ),

        getButton(
            widget.exitText!,
            widget.submitText!
        ),

      ],
    );
  }



  Widget getButton(String exitText,String submitText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding:   EdgeInsets.only(bottom:16.h),
          child: CancelButton(
              onTap: widget.onTap,
              title: AppLocalizations.of(context).translate(exitText)

          ),
        ),
        if (widget.showSubmitButton)
          SubmitButtonComponent(
            text: AppLocalizations.of(context).translate(submitText),
            backGroung: primaryColor,
            isLoading: false,
            onPressed: () {

            },
          ),
      ],
    );
  }


}
