{"reply": "Reply", "report_comment": "Report comment", "edit_comment": "Edit comment", "delete_comment": "Delete Comment", "option": "Option", "add_option": "Add Option", "add_poll": "Add a poll", "report_reason": "why do you want to report this post?", "actions": "Actions", "Copy_Link": "copy link", "saved": "Saved", "Slot": "Slot", "Direct Reporting": "Direct Reporting", "My Team Dashboard": "My Team Dashboard", "My Team Learning": "My Team Learning", "Important Notes:": "Important Notes:", "Your maximum loan limit is : 4 * Basic Salary": "Your maximum loan limit is : 4 * Basic Salary", "Number of Installments is : 12 Month": "Number of Installments is : 12 Month", "bookmark": "bookmark", "report_post": "report post", "Duration : ": "Duration : ", "You have an active loan": "You have an active loan", "Exit": "Exit", "view loan": "View Loan", "Clock in": "Clock in", "Clock out": "Clock out", "Poof! Time Filled Like Magic!": "Poof! Time Filled Like Magic!", "You cannot apply for a new loan until your current loan is fully paid.": "You cannot apply for a new loan until your current loan is fully paid.", "We've got you covered! The Magic Wand feature will automatically fill your missing hours for this week with Work from Home (WFH) hours.": "We've got you covered! The Magic Wand feature will automatically fill your missing hours for this week with Work from Home (WFH) hours.", "Fill My Hours": "Fill My Hours", "Loan Amount should be less than maximum limit": "Loan Amount should be less than maximum limit", "Your maximum loan limit is :": "Your maximum loan limit is : ", "subscribe": "subscribe", "writeComment": "Write a Comment", "Has Missing Hours": "Has Missing Hours", "Has Missing Hours Description": "You have missing hours for the current week. Please make sure to log your hours to avoid any issues.", "View Details": "View Details", "reactions": "Reactions", "Comments": "Comments", "Cyber Security": "Cyber Security", "Cyber Security Services": "Cyber Security Services", "Cyber Security Services Description": "Explore our Cyber Security services to protect your organization from cyber threats and vulnerabilities.", "login": "<PERSON><PERSON>", "name": "Name", "email": "Email", "phoneNumber": "Phone Number", "companyName": "Company Name", "register": "Register", "“please attach the new passport”": "“please attach the new passport”", "registrationSuccessful": "Registration Successful", "registrationFailed": "Registration Failed: ", "User name must not be empty": "User name must not be empty", "Password must not be empty": "Password must not be empty", "Forget Password ?": "Forget Password ?", "Request Management": "Request Management", "Passport Update": "Passport Update", "You can apply now for Passport Update Request": "You can apply now for Passport Update Request", "You can apply now for Passport Update": "You can apply now for Passport Update", "Submit, Track, and stay updated on all your Requests - from sick leave to vacation time.": "Submit, Track, and stay updated on all your Requests - from sick leave to vacation time.", "Next": "Next", "update": "Update", "Open": "Open", "Send reminder": "Send reminder", "Send via teams": "Send via teams", "Please add a valid reason for your withdrawal , and please note if you proceed , this approval will be rejected": "Please add a valid reason for your withdrawal , and please note if you proceed , this approval will be rejected", "validation_failed": "Validation failed", "submit": "Submit", "comment": "Comment", "accept_resolution": "Accept Resolution", "feedback_improvement": "Your feedback helps us improve our service", "reject_resolution": "Reject Resolution", "validationFailed": "Validation failed", "Reject": "Reject", "acceptResolution": "Accept Resolution", "acceptResolutionDescription": "Your feedback helps us improve our service", "rejectResolution": "Reject Resolution", "rejectResolutionDescription": "Your feedback helps us improve our service", "resolveRequest": "Resolve Request", "resolveRequestDescription": "Please fill the data below to resolve the Request", "resolutionCategory": "Resolution Category", "Approval Control": "Approval Control", "Effortlessly review and decide on your team's requests, ensuring smooth operations.": "Effortlessly review and decide on your team's requests, ensuring smooth operations.", "Passcode doesn't match the previous one": "Passcode doesn't match the previous one", "Repeat the passcode you just entered": "Repeat the passcode you just entered", "Stay Logged In": "Stay Logged In", "Collaboration": "Collaboration", "Discuss your request with approvers": "Discuss your request with approvers", "By logging in now, you accept you'll stay logged in and won't need to enter credentials every time. You can log out in settings at any time.": "By logging in now , you accept you’ll stay logged in and won’t need to enter credentials every time. you can log out in settings at any time .", "Cancel": "Cancel", "Continue": "Continue", "Timesheet Tracking": "Timesheet Tracking", "Log tasks, projects, and breaks easily to manage your work hours effectively.": "Log tasks, projects, and breaks easily to manage your work hours effectively.", "Sign in to your Account": "Sign in to your Account", "User Name": "User Name", "Enter Name": "Enter Name", "Password": "Password", "Enter Password": "Enter Password", "Forgot Password": "Forgot Password", "Keep me logged in": "Keep me logged in", "By proceeding, you also agree to the terms of the Services and Privacy Policy.": "By proceeding, you also agree to the terms of the Services and Privacy Policy.", "LeaveRequest": "Leave Request", "Travel&Expenses": "Travel & Expenses", "Balance": "Balance :", "Available": "Available :", "Requested": "Requested :", "NewlyRequested": "Newly Requested :", "TypeOfLeave": "Type of Leave", "LeaveDate": "Leave Date", "Select": "Select", "Delegate": "Delegate", "SearchByName": "Search by Name", "Next Approval": "Next Approval", "ClickToUpload": "Click to Upload", "ToLargeFile": "To Large file, the most size should be 3MB", "FileName": "File Name", "Submit": "Submit", "RequestSubmittedSuccessfully": "Request Submitted Successfully", "NewRequest": "New Request", "Today": "Today", "AM": "AM", "YouDoNotHaveNotifications": "You don't have any notifications yet", "MyRequests": "My Requests", "MyTasks": "My Tasks", "Done": "Done", "More": "More", "Language": "Language", "privacyPolicy": "privacy policy", "LogOut": "Log out", "Profile": "Profile", "PersonalInfo": "Personal Information", "FullName": "Full Name", "Phone": "Phone", "Manager": "Manager", "Request Detail": "Request Details", "Owner name :": "Owner Name : ", "Owner ID :": "Owner ID : ", "WBS code :": "WBS code :", "Create new leave request": "Create New Leave Request", "Please restart to update your application": "Please restart to update your application", "restart": "restart", "My Requests": "My Requests", "PrivacyPolicyViewBodyContent": "Purpose\n\nElme - Services is an employee services platform that connects the employees in Elm with the service providers inside the organization. This policy sets the standards for acceptable use of Elme Services to ensure alignment while maintaining compliance with legal and organizational requirements.\n\nApplicability\n\nThis policy applies to all employees of Elm.\n\nData Collection\n\nWe collect and process employee data to facilitate your use of the Platform and to enhance your experience. The types of data collected include:\n\n· Personal Information: Name, employee ID, department, position, contact details, and profile information.\n\n· Usage Data: Login activity, preferences, and interactions with features on the Platform.\n\n· Technical Data: Device type, browser details, IP address, and session data for security and analytics purposes.\n\n· Other Data: Any additional information voluntarily submitted through forms or surveys.\n\nPurpose of Data Collection\n\nThe data we collect is used for the following purposes:\n\n· To enhance your experience on the Platform.\n\n· To deliver services, tools, and resources tailored to your role and needs.\n\n· To facilitate effective communication, including HR announcements and notifications.\n\n· To ensure compliance with internal policies and regulatory requirements.\n\n· To improve the functionality and security of the Platform through analytics and feedback.\n\nData Usage\n\nYour data is used exclusively for business purposes related to your employment. Examples include:\n\n· Providing access to employee services and resources.\n\n· Generating reports and insights for operational and performance improvements.\n\n· Responding to technical or HR-related inquiries and support requests.\n\nData Sharing\n\nYour personal information may be shared internally with authorized departments, such as:\n\n· Human Resources: For personnel management and benefits administration.\n\n· IT Services: For troubleshooting, access management, and security.\n\n· Management: For performance tracking and operational insights.\n\nData Protection\n\nWe implement robust security measures to safeguard your data, including:\n\n· Encryption of data during transmission and storage.\n\n· Access control mechanisms to restrict unauthorized access.\n\n· Regular audits to ensure compliance with security standards.\n\nWe are committed to maintaining the confidentiality, integrity, and availability of your information.\n\nData Retention\n\nYour data will be retained only for as long as necessary to fulfill the purposes outlined in this policy. Retention periods are determined by legal, regulatory, and operational requirements.\n\nChanges to the Policy\n\nThis Privacy and Policy page is subject to periodic updates to reflect changes in practices or legal requirements. Any significant changes will be communicated via email or platform notification. The updated policy will be effective upon posting.\n\nAcknowledgment of Compliance\n\nBy using the Platform, you agree to abide by this Privacy and Policy. You also agree to adhere to all company policies and guidelines related to the use of internal systems.", "Leave Request > Create Leave Request": "Leave Request > Create Leave Request", "New": "New", "Last Day": "Last Day", "please click open door when you are at the gate": "please click open door when you are at the gate", "Find Employee": "Find Employee", "Check Balance": "Check Balance", "Enter Employee name": "Enter Employee Name", "Enter Employee ID": "Enter Employee ID", "Search by Type": "Search by Type", "Resend": "Resend", "SICK W/O": "SICK W/O", "Didn't recieve?": "Didn't recieve?", "Emergency": "Emergency", "Emergency Leave": "Emergency Leave", "School Allowance Request": "School Allowance Request", "Create New Request": "Create New Request", "You can add your school allowance request": "You can add your school allowance request", "Pending Requests": "Pending Requests", "Services": "Services", "Service": "Service", "Request": "Request", "Received": "Received", "From": "From", "No pending requests found": "No pending requests found", "You have no pending requests at the moment": "You have no pending requests at the moment", "Time Off": "Time Off", "My Request History": "My Request History", "Leave Balance": "Leave Balance", "Emergency Leave Request": "Emergency Leave Request", "Sick Leave Request": "Sick Leave Request", "You don't have any task assigned yet": "You don't have any task assigned yet", "Review Request": "Review Request", "Login to your account": "Login to your account", "Travel Expenses": "Travel Expenses", "Time & Leave": "Time & Leave", "Annual Leave": "Annual Leave", "Favorite Services": "Favorite Services", "IT Assets": "IT Assets", "Explore": "Explore", "Explore Help Section for answers to FAQs, guides, and tips to enhance your experience": "Explore Help Section for answers to FAQs, guides, and tips to enhance your experience", "No pending requests at the moment": "No pending requests at the moment", "View Timeline": "View Timeline", "Travel Request": "Travel Request", "Expense Claim Request": "Expense Claim Request", "Leave Request": "Leave Request", "Request History": "Request History", "Sign in": "Sign in", "Submit Your Request": "Submit Your Request", "Expense Claims": "Expense <PERSON>", "Reports": "Reports", "Stay logged in": "Stay logged in", "Enter your email": "Enter your email", "Enter your passcode": "Enter your passcode", "Forgot Password?": "Forgot Password?", "Request a new code": "Request a new code", "Your request has been submitted successfully.": "Your request has been submitted successfully.", "Home page": "Home page", "Timesheet page": "Timesheet page", "Leave Request page": "Leave Request page", "Travel & Expenses page": "Travel & Expenses page", "Timesheet Management": "Timesheet Management", "Log hours spent on different tasks and projects for accurate tracking and reporting.": "Log hours spent on different tasks and projects for accurate tracking and reporting.", "Dashboard": "Dashboard", "Overview of your tasks, projects, and performance in one central location.": "Overview of your tasks, projects, and performance in one central location.", "My Profile": "My Profile", "Manage your personal information, preferences, and security settings.": "Manage your personal information, preferences, and security settings.", "Notifications": "Notifications", "Stay updated on important events, requests, and changes within the system.": "Stay updated on important events, requests, and changes within the system.", "Help & Support": "Help & Support", "Access resources and get assistance whenever you need it.": "Access resources and get assistance whenever you need it.", "Settings": "Settings", "Customize your experience and manage your account settings.": "Customize your experience and manage your account settings.", "Search": "Search by service", "Search_by_request": "By Requester mail, Request: ID, etc", "Enter keywords to find what you're looking for": "Enter keywords to find what you're looking for", "Results": "Results", "No results found": "No results were found", "Procurement services": "Procurement services", "Legal Services": "Legal Services", "Marketing services": "Marketing services", "Create RFI and RFQ, Issue POs, and Approve GRs": "Create RFI and RFQ, Issue POs, and Approve GRs", "Go back": "Go back", "No internet connection": "No internet connection", "Please check your connection and try again": "Please check your connection and try again", "An unexpected error occurred": "An unexpected error occurred", "Please try again later": "Please try again later", "Are you sure you want to delete this item?": "Are you sure you want to delete this item?", "This action cannot be undone": "This action cannot be undone", "Yes, delete it": "Yes, delete it", "No, cancel": "No, cancel", "Assign / Re-assign / Update Request": "Assign / Re-assign / Update Request", "Please assign the request to a member or group": "Please assign the request to a member or group", "note": "Note", "Assign": "Assign", "SLA : 11 hrs": "SLA : 11 hrs", "Classification": "Classification", "Classification is mandatory": "Classification is mandatory", "Terms of Service": "Terms of Service", "Actions": "Actions", "Privacy Policy": "Privacy Policy", "Cookie Policy": "<PERSON><PERSON>", "Leaves": "Leaves", "School Allowance": "School Allowance", "Mazaya": "<PERSON><PERSON><PERSON>", "General Expense": "General Expense", "Travel": "Travel", "Leave": "Leave", "School Allowance for your children": "School Allowance for your children", "Health, Car Insurance, and more...": "Health, Car Insurance, and more...", "Office Supplies, Business Lunch, and more...": "Office Supplies, Business Lunch, and more...", "Business  and Training trip:": "Business  and Training trip:", "Annual, Emergency, Sick, and more...": "Annual, Emergency, Sick, and more...", "IT Services": "IT Services", "Procurement Services": "Procurement Services", "Time and Leave Services": "Time and Leave Services", "Travel and Expense Services": "Travel and Expense Services", "Payroll Services": "Payroll Services", "Contact Us": "Contact Us", "© 2024 All rights reserved.": "© 2024 All rights reserved.", "Mazaya Request": "Mazaya Request", "Add Mazaya Request": "You can add your Ma<PERSON>ya request", "Time and leave": "Time and leave", "You can add your leave request": "You can add your leave request", "Favorites": "Favorites", "Popular Services": "Popular Services", "You don't have any favorite services yet": "You don't have any favorite services yet", "You can add your expense claim request": "You can add your Expense claim request", "You can add your Mazaya request": "You can add your Ma<PERSON>ya request", "Payment & Benefits": "Payment & Benefits", "You can add your school allowance": "You can add your school allowance", "You add your travel request": "You add your travel request", "Expenses Claim Request": "Expenses Claim Request", "Add Expenses claim request": "You can add your expense claim request", "Enter the code sent to your Email": "Enter the code sent to your Email", "Create Passcode": "Create Passcode", "change language": "تغيير اللغة", "Create a new 4-digit passcode": "Create a new 4-digit passcode", "The passcode must be:": "The passcode must be:", "* Not contain 2 or more repeated numbers consecutively": "* Not contain 2 or more repeated numbers consecutively", "* Not contain 2 or more sequential numbers": "* Not contain 2 or more sequential numbers", "Skip": "<PERSON><PERSON>", "By logging in now, you accept you'll stay logged in Continue in and won't need to enter credentials every time.": "By logging in now , you accept you’ll stay logged in and won’t need to enter credentials every time. you can log out in settings at any time .", "My Leave Balance": "Leave Balance", "Send Request To": "Send Request To", "Search by Manager name": "Search by Manager name", "Manager_Name": "Manager_Name", "Annual leave, Sick leave, Exam leave, ect": "Annual leave, Sick leave, Exam leave, ect", "Explore our Help Section for answers to FAQs, quides, and tips to": "Explore our Help Section for answers to FAQs, quides, and tips to", "enhance your experience": "enhance your experience", "Mazaya, Schooling, Loan, ADV, Salary, ect": "<PERSON><PERSON><PERSON>, Schooling, Loan, ADV, Salary, ect", "Time and leave services": "Time and leave services", "Get help-800": "Get help-800", "Receipt Date": "Receipt Date", "Receipt Amount ( SAR )": "Receipt Amount ( SAR )", "Request Laptop, Email, AD Services and report issues related to it": "Request Laptop, Email, AD Services and report issues related to it", "Services > Time and leave services": "Services > Time and leave services", "Services > Payments & Benefits": "Services > Payments & Benefits", "Travel & Expanses > Expense Claim Request": "Travel & Expanses > Expense Claim Request", "Cost Allocation ": "Cost Allocation ", "Department": "Department", "Department Type": "Department Type", "Project": "Project", "Tech - Enterprise Resource Planning": "Tech - Enterprise Resource Planning", "Project WBS": "Project WBS", "Type": "Type", "resolve": "Resolve request submitted", "Available / Total": "Available / Total", "available": "Available", "Resolve": "Resolve", "Expense Type*": "Expense Type*", "Receipt Amount (SAR)*": "Receipt Amount (SAR)*", "Receipt Date*": "Receipt Date*", "Click to upload": "Click to upload", "Not Valid OTP": "Invalid verification code", "Expense Type": "Expense Type", "Search by expense type": "Search by expense type", "Dependent visa expense": "Dependent visa expense", "Search by project name": "Search by project name", "Project ID, Name, Owner": "Project ID, Name, Owner", "Request Submitted Successfully": "Request Submitted Successfully", "You can view your request details below": "You can view your request details below", "Cost Allocation": "Cost Allocation", "Please add a valid reason for your approval": "Please add a valid reason for your approval", "Mohamed still has 20 days off for this year": "<PERSON> still has 20 days off for this year", "Approve": "Approve", "Reason of rejection": "Reason of rejection", "Please add a valid reason for your rejection,.and please not if you proceed, this approval will be rejected": "Please add a valid reason for your rejection,.and please not if you proceed, this approval will be rejected", "Mohamed already exceeded his vacation": "<PERSON> already exceeded his vacation", "limit for this month": "limit for this month", "Attachments": "Attachments", "resolution_attachments": "Resolution Attachment", "File name": "File name", "I need a vacation for 3 days because I'm sick": "I need a vacation for 3 days because I'm sick", "Travel & Expanses": "Travel & Expanses", "Total": "Total", "SAR": "SAR", "Consumed": "Consumed", "Remaining": "Remaining", "Benefit Type 1": "Benefit Type 1", "Maximum Limit": "Maximum Limit", "must select leave date": "must select leave date", "must select delegate": "must select delegate", "must select leave type": "must select leave type", "Consumed Amount": "Consumed Amount", "Remaining Amount": "Remaining Amount", "ELM covered amount": "ELM covered amount", "Invoice Amount ( SAR )": "Invoice Amount ( SAR )", "Search by": "Search by", "Benefit Type": "Benefit Type", "Search by benefit name": "Search by benefit name", "Gym/Healthy food subscription": "Gym/Healthy food subscription", "Subscription for family": "Subscription for family", "Technology Learning": "Technology Learning", "Car Insurance": "Car Insurance", "None": "None", "Submit Changes": "Submit Changes", "Search by category name": "Search by category name", "Fitness subscription by Elm": "Fitness subscription by Elm", "Buy fitness equipment": "Buy fitness equipment", "Healthy food subscription": "Healthy food subscription", "Fitness Center": "Fitness Center", "Fitness time": "Fitness time", "Fitness time pro ladies": "Fitness time pro ladies", "Gym Subscription Fees": "Gym Subscription Fees", "Beneficiary ID / Iqama": "Beneficiary ID / Iqama", "Beneficiary Mobile": "Beneficiary Mobile", "Request Type": "Request Type", "Change Request": "Change Request", "Personal Loan": "Personal Loan", "Enter the reason here": "Enter the reason here", "Please enter reason": "Please enter reason", "Loan Amount": "<PERSON><PERSON> ", "reason": "Reason", "monthly_installments_amount": "Monthly Installments Amount ", "Withdraw": "Withdraw", "I read and accept": "I read and accept ", "terms and conditions.": "terms and conditions.", "Open via teams": "Open via teams", "Reason of Approve": "Reason of Approve", "*Reason of Approve": "Reason of Approve", "Reason of approval": "Reason of Approval", "Please add a valid reason for your approve": "Please add a valid reason for your approve", "Reject Request": "Reject Request", "Reson of rejection": "Reason of rejection", "Please provide a valid reason for your rejection": "Please provide a valid reason for your rejection", "Please add a valid reason for your rejection , \nand please not if you proceed , this approval will be rejected": "Please add a valid reason for your rejection , \nand please not if you proceed , this approval will be rejected", "Are you sure you want to logout?": "Are you sure you want to logout?", "Benefit": "Benefit", "Details notes": "Notes", "Reason of withdrawal": "Reason for withdrawal", "Please add a valid reason for your withdrawaland please not if you proceed, this request will be deleted": "Please add a valid reason for your withdrawal, and please note that if you proceed, this request will be deleted", "I'm no longer needs sick leave, i feel better today.": "I no longer need sick leave, I feel better today.", "Withdraw Request": "Withdraw Request", "Travel & Expenses": "Travel & Expenses", "Services > Travel & Expenses": "Services > Travel and Expenses", "Travel & Expanses > School Allowance": "Travel and Expenses > School Allowance", "School Allowance is exclusively available for children between the ages of 2 and 18": "School Allowance is exclusively available for children aged between 2 and 18", "School Allowance (Year)": "School Allowance (Year)", "Year": "Year", "one": "one", "two": "two", "three": "three", "four": "four", "five": "five", "Requester Name": "Requester Name", "nextApprovalPersonName": "Next approval", "2023-2024": "2023-2024", "School benefits remaining balance": "School benefits remaining balance", "Child 1": "Child 1", "General Expenses": "General Expenses", "By logging in now, you accept you will stay logged in and won't need to enter credentials every time. You can log out in settings at any time.": "By logging in now, you accept you will stay logged in and won't need to enter credentials every time. You can log out in settings at any time.", "Child 2": "Child 2", "Child 3": "Child 3", "Child": "Child", "Total/Remaining": "Total/Remaining", "0 SAR/3350 SAR": "0 SAR/3350 SAR", "Child One": "Child One", "please select period": "please select period", "Abdullah Mohamed": "<PERSON>", "Payment Mode": "Payment Mode", "Semester/Yearly": "Semester/Yearly", "Yearly": "Yearly", "Semester": "<PERSON><PERSON><PERSON>", "I confirm fees are paid": "I confirm fees are paid", "Extend Date": "Extend Date", "Semester 1 (SAR)": "Semester 1 (SAR)", "Semester 2 (SAR)": "Semester 2 (SAR)", "Semester 3 (SAR)": "Semester 3 (SAR)", "Cancel Exit Re-Entry Visa": "Cancel Exit Re-Entry Visa", "Extend Exit Re-Entry Visa": "Extend Exit Re-Entry Visa", "Click to upload Invoice": "Click to upload Invoice", "Must add at least 1 invoice": "Must add at least 1 invoice", "remaining balance is insufficient": "remaining balance is insufficient", "The request contains already claimed child": "The request contains already claimed child", "can't submit while you have pending request": "can't submit while you have pending request", "must add at least one attachment for each child": "must add at least one attachment for each child", "Children": "Children", "To large file The most size should be 8MB": "To large file The most size should be 8MB", "Total Claim Amount": "Total Claim Amount", "137367 SAR": "137367 SAR", "I Requested school allowance for my 3 children..": "I requested school allowance for my 3 children.", "Newly Requested": "Newly Requested", "Type of Leave": "Type of Leave", "Annual": "Annual", "Sick": "Sick", "Paternity": "Paternity", "Pilgrimage": "Pilgrimage", "Condolence of parents": "Condolence of parents", "Condolence of dependents": "Condolence of dependents", "Condolence of relative": "Condolence of relative", "Elm reward-10 YRS in Elm": "Elm reward-10 YRS in Elm", "Examination": "Examination", "Maternity": "Maternity", "Sick unpaid": "Sick unpaid", "Bereavement": "Bereavement", "Internal accompany leave": "Internal accompany leave", "External accompany leave": "External accompany leave", "Marriage leave": "Marriage leave", "Leave Date": "Leave Date", "Non-Working Day": "Non-Working Day", "Pending Request": "Pending Request", "Rejected Request": "Rejected Request", "Approved Request": "Approved Request", "Search by Name": "Search by Name", "Click to Upload": "Click to Upload", "To Large file, the most size should be 3MB": "File too large. Maximum size should be 3MB.", "File Name": "File Name", "My Approvals": "My Approvals", "No results were found": "No results were found", "You don't have any pending approvals yet": "You don't have any pending approvals yet", "You don't have any history yet": "You don't have any history yet", "New Update": "New Update", "lang": "العربية", "Please take an action to proceed": "Please take an action to proceed", "request_management_description": "Submit, track, and stay updated on all your requests – from sick leave to vacation time.", "approval_control_description": "Effortlessly review and decide on your team’s requests, ensuring smooth operations.", "Please Install our new version to be able to proceed": "Please install our new version to proceed", "Install": "Install", "accept-resolution": "Acceptance has been submitted", "reject-resolution": "Rejection has been submitted", "approve": "Approved has been submitted", "timesheet_approve": "Approved has been submitted", "reject": "Rejection has been submitted", "Leave Type": "Leave Type", "from To": "From To", "New Request": "New Request", "You don't have any notifications yet": "You don't have any notifications yet", "privacy policy": "Privacy Policy", "By proceeding you also agree\n\r to the terms of Services and Privacy Policy": "By proceeding you also agree\n\r to the terms of Services and Privacy Policy", "select a child": "select a child", "please enter valid amount": "please enter valid amount", "please enter valid semester amount": "please enter valid semester amount", "Log out": "Log out", "Logout": "Logout", "Personal Information": "Personal Information", "Full Name": "Full Name", "Email": "Email", "Error-Title": "Error Title", "please choose payment mode": "please choose payment mode", "Something Went Wrong": "Something Went Wrong", "Current Working Hours": "Current Working Hours", "Remaining Hours for this week": "Remaining Hours for this week", "Current Hours": "Current Hours", "Total Hours": "Total Hours", "Hours Breakdown": "Hours Breakdown", "Hours Used": "Hours Used", "Hours Available": "Hours Available", "YouDoNotHaveFavorites": "You don't have any favorite services yet", "PopularServices": "Popular Services", "My IT assets": "My IT assets", "Delegate to": "Delegate to", "to": "To", "Status": "Status", "Approved": "Approved", "Declined": "Declined", "Request ID": "Request ID", "Forward Task Successfully": "Forward Task Successfully", "Forward Task Failed": "Forward Task Failed", "addComment": "Add Comment", "sendingMessage": "Sending...", "Invalid username or password": "Invalid username or password", "expanse type": "Expanse Type", "cost allocation": "Cost Allocation", "department": "Department", "mazayaAttachNote": "Please attach payment Receipt of transfer to Elm account Saudi National Bank IBAN: ************************", "You don't have any task here": "You don't have any Task here", "You don't have any Request here": "You don't have any Request here", "You don't have any Notification here": "You don't have any Notification here", "Search by Project name, WPS code, owner name or owner ID": "Search by Project name, WPS code, owner name or owner ID", "Project name :": "Project name :", "Elm Covered Amount": "Elm Covered Amount", "maximum_limit": "Maximum Amount Already Reached", "choose_gallery": "Choose from gallery", "choose_files": "Choose from files", "There is a conflict please choose again": "There is a conflict please choose again", "your balance is insufficient": "your balance is insufficient", "Send Reminder": "Send Reminder", "Send Reminder Successfully": "Send Reminder Successfully", "Send Reminder Failed": "Send Reminder Failed", "Soft Reminder for": "Soft Reminder for my", "child already claimed": "This child is already claimed", "Details": "Details", "Balance :": "Balance :", "Newly Requested :": "Newly Requested :", "Leave Time (From-To) :": "Leave Time (From-To) :", "Delegate To :": "Delegate To :", "Cost Allocation :": "Cost Allocation :", "Department Type :": "Department Type :", "Expense Type :": "Expense Type :", "Receipt Amount :": "Receipt Amount :", "Receipt Date :": "Receipt Date :", "Benefit Type :": "Benefit Type :", "Category :": "Category :", "Category": "Category", "Elm Covered Amount :": "Elm Covered Amount :", "School Allowance Year :": "School Allowance Year ", "Payment Mode :": "Payment Mode :", "Child one remaing amount :": "Child one remaing amount :", "Child one total amount :": "Child one total amount :", "Requested Amount :": "Requested Amount :", "Project WBS :": "Project WBS :", "Claimed children :": "Claimed children :", "name : ": "name : ", "type : ": "Payment type : ", "amount": "amount : ", "year": "yearly", "semester": "semester", "Purpose of travel": "Purpose of travel", "Business travel": "Business travel", "In house training": "In house training", "Public training/Conference": "Public training/Conference", "Travel type": "Travel type", "Outside KSA": "Outside KSA", "Inside KSA": "Inside KSA", "Reason of trip": "Reason of trip", "Embassy Name": "Embassy Name", "Passport Number  : ": "Passport Number  : ", "Expire Date  : ": "Expire Date  : ", "Origin region/city (KSA)": "Origin region/city (KSA)", "Destination country": "Destination country", "Destination region/city": "Destination region/city", "Zone": "Zone", "Assignment": "Assignment", "Cost center / WPS Projects": "Cost center / WPS Projects", "Cost center": "Cost center", "WPS Projects": "WPS Projects", "Last/First/Middle name as per passport": "Last/First/Middle name as per passport", "Title": "Title", "Nationality": "Nationality", "DOB (MM/DD/YY)": "DOB (YYYY-MM-DD)", "Passport type": "Passport type", "Passport Number": "Passport Number", "Add guest +": "Add guest +", "please enter": "please enter", "short text": "short text : ", "cost center code": "cost center code : ", "cost center :": "Cost Center : ", "responsible person name": "responsible person name : ", "responsible person id": "responsible person id : ", "search here": "search here", "By Elm": "By Elm", "By Cash": "By Cash", "Air Ticket": "Air Ticket", "Travel agent": "Travel agent", "Accommodation": "Accommodation", "Fly dates": "Fly dates", "Actual dates of the trip": "Actual dates of the trip", "Total business days": "Total business days", "Full per diem to be paid : ": "Full per diem to be paid : ", "Transportation limit to be provided : ": "Transportation limit to be provided : ", "Exit re-entry : ": "Exit re-entry : ", "Accommodation limit to be provided : ": "Accommodation limit to be provided : ", "Course details": "Course details", "Course provider": "Course provider", "Course fee": "Course fee", "Additional destination": "Additional destination", "Event in itinerary": "Event in itinerary", "Date": "Date", "Country": "Country", "Region/ City": "Region/ City", "Add +": "Add +", "Start of trip": "Start of trip", "Reason": "Reason", "Additional Destination": "Additional Destination", "Destination": "Destination", "Saudi Arabia": "Saudi Arabia", "Guests list": "Guests list", "Guest": "Guest", "Fly Date": "Fly Date", "Purpose": "Purpose", "Passport Number/Type :": "Passport Number/Type :", "Passport Number :": "Passport Number :", "Passport Type :": "Passport Type :", "Expiry Date : ": "Expiry Date : ", "Passport Name : ": "Passport Name : ", "Origin : ": "Origin : ", "Destination : ": "Destination : ", "Zone : ": "Zone : ", "Exit re-entry visa :": "Exit re-entry visa :", "Include Invitation Letter :": "Include Invitation Letter :", "Embassy Name :": "Embassy Name :", "Assignment :": "Assignment :", "Type :": "Type :", "Air Ticket :": "Air Ticket :", "Accommodation :": "Accommodation :", "Travel Agent :": "Travel Agent :", "Fly Dates :": "Fly Dates :", "Actual dates of the trip  :": "Actual dates of the trip  :", "Total Business days :": "Total Business days  :", "Course Details : ": "Course Details : ", "Course Provider : ": "Course Provider : ", "Course Fees : ": "Course Fees : ", "Additional Destination : ": "Additional Destination : ", "Additional Destinations": "Additional Destinations", "Event in itinerary :": "Event in itinerary :", "Date : ": "Date : ", "Country :": "Country :", "Region :": "Region :", "Reason :": "Reason :", "Full per diem to be paid :": "Full per diem to be paid :", "Transportation limit to be provided :": "Transportation limit to be provided :", "Exit re-entry :": "Exit re-entry:", "Distance allowance for travel by road :": "Distance allowance for travel by road :", "Total cost :": "Total cost :", "Guest List": "Guest List", "Passport Update Request": "Passport Update Request", "Issue Date": "Issue Date", "Issue Date : ": "Issue Date : ", "Expiry Date": "Expiry Date", "please enter notes": "please enter notes", "Country Of Issue": "Country Of Issue", "Place Of Issue": "Place Of Issue", "“Please attach old and new passport”": "“Please attach old and new passport”", "Update on Absher , available only for expat users": "Update on Absher, available only for expat users", "Reason For Trip": "Reason For Trip", "Notes": "Notes", "Yes": "Yes", "No": "No", "Name :": "Name :", "Title : ": "Title : ", "Nationality : ": "Nationality : ", "DOB : ": "DOB : ", "Invoices": "Invoices", "soon": "Soon", "Receipt Amount Benefit": "Receipt Amount Benefit :", "Type Reason *": "Type Reason *", "Purpose Of Travel | Type": "Purpose Of Travel | Type", "From : ": "From : ", "To : ": "To : ", "Total Cost": "Total Cost", "Total Cost : ": "Total Cost : ", "Air Ticket Value filled by HR : ": "Air Ticket Value filled by HR : ", "Distance Allowance for travel by road : ": "Distance Allowance for travel by road : ", "Please attach a clearance": "“ Please attach a clearance letter from current bank”", "Please Select bank name": "Please Select bank name", "New Bank Name": "New Bank Name", "Please enter new IBAN number": "Please enter new IBAN number", "New IBAN Number": "New IBAN Number", "Current IBAN No": "Current IBAN No :", "Current Bank Name": "Current Bank Name :", "Advance Salary": "Advance Salary", "IBAN Update Request": "IBAN Update Request", "Start Date": "Start Date", "Please select date": "Please select date", "Profile Services": "Profile Services", "change personal info, IBAN..": "change personal info, IBAN..", "Update IBAN": "Update IBAN", "You can apply now for IBAN updates": "You can apply now for IBAN updates", "Please enter valid IBAN number": "Please enter valid IBAN number", "Business Card Update": "Business Card Update", "You can apply now for Business Card updates": "You can apply now for Business Card updates", "Business Card Request": "Business Card Request", "Employee Name": "Employee Name :", "Position": "Position :", "Department:": "Department :", "Devision": "Devision :", "Business Email": "Business Email :", "Mobile Number": "Mobile Number", "Please enter your mobile number": "Please enter your mobile number", "Office Location": "Office Location", "Please Select office location": "Please Select office location", "Department request": "Department Lunch Request", "Department lunch": "Department Lunch", "Department lunch desc": "Add request with department lunch", "Budget amount": "Budgeted Amount (SAR)", "Lunch Date": "Lunch/Invoice Date", "Department Employees": "Department Employees", "Corporate Employees": "Corporate Employees", "Lunch receipt": "“ Please attach the lunch receipt'”", "Search employees": "Search by name or id", "Add": "Add", "You don’t have any favourite services yet ": "You don’t have any favourite services yet", "your max allowed is": "your max allowed of days is ", "Please enter valid mobile number": "Please enter valid mobile number", "Futuer dates are invalid. Please choose a valid date": "Futuer dates are invalid. Please choose a valid date", "title withdrawal": "Please add a valid reason for your withdrawal, \nand please note if you proceed, this approval will be rejected", "Covored Amount": "Covered amount (SAR)", "invitees": "Invitees", "invoice amount number": "Invoice amount must be equal to or less than budget amount", "Invoice Amount": "Invoice Amount", "Budget amount success": "Budgeted Amount", "Covored Amount success": "Covered Amount", "Department Invitees": "Department Invitees", "Corporate Invitees": "Corporate Invitees", "Iqama Number": "Iqama Number", "Iqama Number : ": "Iqama Number : ", "Expiry date should be greater than issue date": "Expiry date should be greater than issue date", "please enter valid passport number": "please enter valid passport number", "please enter valid Iqama number": "please enter valid Iqama number", "warning": "Warning", "change lunch month": "Changing the lunch date to a different month will remove the selected invitees", "confirm": "Confirm", "add all users": "Do you want to add all of your department employees", "Education Update Request": "Education Update Request", "Education level": "Education level ", "Education level *": "must select education level", "Country Key": "Country", "must select country": "must select country", "Field of study": "Field of study", "must select field of study": "must select field of study", "Certificate": "Certificate ", "Certificate *": "must select certificate", "Institute/Location ": "Institute Location", "Institute/Location *": "Institute location must not be empty", "Final Grade ": "Final Grade ", "Final Grade *": "final grade must not be empty", "Duration of Course ": "Duration of Course ", "Duration of Course*": "duration of course must not be empty", "Duration Unit": "Duration Unit ", "must select duration unit": "must select duration unit", "Must add at least 1 attachment": "“Must add at least 1 attachment”", "Update Education": "Update Education", "update education details": "Update Education Details", "You can apply now for Education updates": "'You can apply now for Education updates", "months": "Months", "years": "Years", "Egypt": "Egypt", "Select Country": "Select Country", "department add employee message": "You have to select at least one department employee or corporate employee", "employee to pay": "Employee to pay for his Family", "Iqama Renewal Request": "Iqama Renewal Request", "Government Relations": "Government Relations", "Iqama Renewal": "<PERSON><PERSON><PERSON>", "Submit Iqama Renewal": "Submit <PERSON><PERSON><PERSON>", "iqama reason": "Select Reason", "iqama other reason": "Other Reason", "iqama printing request": "Iqama Printing Request", "search by reason": "Search by reason", "iqama printing": "Iqama Printing", "submit iqama printing": "Submit Iqama Printing", "government services desc": "Renew, Print your Iqama, etc...", "iqama issue": "Iqama Issue Request", "iqama issue title": "Iqama Issue For Employee", "iqama issue subtitle": "Submit Iqama Issue For Employee", "iqama issue for employee": "Iqama Issue For Employee Request ", "employee id": "Employee ID", "Select Employee": "Select Employee", "passport expiry date": "Passport Expiry Date", "border number": "Border Number", "name (en)": "Employee Name (EN)", "name (ar)": "Employee Name (AR)", "cost center": "Cost Center", "iqama issue attachment msg": "“ Please attach employee picture and passport”", "border number msg": "Please enter 10 digits", "Employee Name : ": "Employee Name : ", "Iqama Number: ": "Iqama Number: ", "Issue Date: ": "Issue Date: ", "Expiry Date: ": "Expiry Date: ", "Iqama Cancelation Request": "Iqama Cancelation Request", "Iqama Cancelation for employee Request": "Iqama Cancelation for employee Request", "Iqama Cancelation for employee": "Iqama Cancelation for employee", "Employee ID": "Employee ID", "Submit Iqama Cancelation for employee": "Submit Iqama Cancelation for employee", "employee name": "Employee Name", "iqama issue attach msg": "“ Please upload employee picture and passport”", "Do you want to paste": "Do you want to paste ", "Paste OTP": "Paste OTP", "Paste": "Paste", "employee number": "Employee Number", "Family Member": "Family Member", "Iqama Cancelation for employee’s family Request": "Iqama Cancelation for employee’s family Request", "Family Members": "Family Members", "Search by name or id": "Search by name or id", "Delete": "Delete", "Relation : ": "Relation : ", "Name : ": "Name : ", "search by cost center": "Search by Cost Center Name, Cost Center Number", "number:": "Number : ", "description:": "Description : ", "Search by employee ID or Name": "Search by employee ID or Name", "You don't have family members": "You don't have any family members", "change emp id iqama issue": "Changing the employee ID to a different one will remove the selected data you entered", "Issue Iqama for Employee": "Issue Iqama for Employee", "Cancel Iqama for Employee": "Cancel Iqama for Employee", "Cancel Iqama -  Family": "<PERSON><PERSON> -  Family", "Visa Type": "Visa Type", "Duration in Days": "Duration in Days", "I confirm fees are paid *": "I confirm fees are paid *", "Issue Exit Re-Entry Visa": "Issue Exit Re-Entry Visa", "Facility and Admin Services": "Facility and Admin", "Service Details": "Service Details", "Service Selection": "Service Selection", "Service Provider": "Service Provider", "Service Name": "Service Name", "Access Card": "Access Card", "Open door": "Open door", "Close door": "Close door", "Service Type": "Service Type", "Service is mandatory": "Service is mandatory", "Group": "Group", "Group is mandatory": "Group is mandatory", "Member": "Member", "Member is mandatory": "Member is mandatory", "Select Member": "Select Member", "Priority": "Priority", "Priority is mandatory": "Priority is mandatory", "Select Priority": "Select Priority", "Service Type is mandatory": "Service Type is mandatory", "Service Provider is mandatory": "Service Provider is mandatory", "You must select Service Provider": "You must select Service Provider", "You must select Service Type ": "You must select Service Type ", "Overdue": "Overdue", "UnDefiend": "UnDefiend", "Please enter a note": "Please enter a note", "You must select Service": "You must select Service", "Please attach supporting documents": "“Please attach supporting documents”", "You can add your support request": "You can add your support request", "must not be empty": "must not be empty", "check-in": "Check In Request Successfully", "check-out": "Check Out Request Successfully", "All": "All", "you must upload file": "you must upload file", "Dewan": "<PERSON><PERSON>", "privacy_policy_purpose_title": "Purpose", "privacy_policy_purpose_description": "Elme - Services is an employee services platform that connects the employees in Elm with the service providers inside the organization. This policy sets the standards for acceptable use of Elme Services to ensure alignment while maintaining compliance with legal and organizational requirements.", "privacy_policy_applicability_title": "Applicability", "privacy_policy_applicability_description": "This policy applies to all employees of Elm.", "privacy_policy_data_collection_title": "Data Collection", "privacy_policy_data_collection_description": "We collect and process employee data to facilitate your use of the Platform and to enhance your experience. The types of data collected include:", "privacy_policy_data_collection_point1": "Personal Information: Name, employee ID, department, position, contact details, and profile information.", "privacy_policy_data_collection_point2": "Usage Data: Login activity, preferences, and interactions with features on the Platform.", "privacy_policy_data_collection_point3": "Technical Data: Device type, browser details, IP address, and session data for security and analytics purposes.", "privacy_policy_data_collection_point4": "Other Data: Any additional information voluntarily submitted through forms or surveys.", "privacy_policy_purpose_of_data_collection_title": "Purpose of Data Collection", "privacy_policy_purpose_of_data_collection_description": "The data we collect is used for the following purposes:", "privacy_policy_data_usage_title": "Data Usage", "privacy_policy_data_usage_description": "Your data is used exclusively for business purposes related to your employment. Examples include:", "privacy_policy_data_sharing_title": "Data Sharing", "privacy_policy_data_sharing_description": "Your personal information may be shared internally with authorized departments, such as:", "privacy_policy_data_protection_title": "Data Protection", "privacy_policy_data_protection_description": "We implement robust security measures to safeguard your data, including:", "privacy_policy_data_protection_additional": "We are committed to maintaining the confidentiality, integrity, and availability of your information.", "privacy_policy_data_retention_title": "Data Retention", "privacy_policy_data_retention_description": "Your data will be retained only for as long as necessary to fulfill the purposes outlined in this policy. Retention periods are determined by legal, regulatory, and operational requirements.", "privacy_policy_changes_to_policy_title": "Changes to the Policy", "privacy_policy_changes_to_policy_description": "This Privacy and Policy page is subject to periodic updates to reflect changes in practices or legal requirements. Any significant changes will be communicated via email or platform notification. The updated policy will be effective upon posting.", "privacy_policy_acknowledgment_of_compliance_title": "Acknowledgment of Compliance", "privacy_policy_acknowledgment_of_compliance_description": "By using the Platform, you agree to abide by this Privacy and Policy. You also agree to adhere to all company policies and guidelines related to the use of internal systems.", "privacy_policy_purpose_of_data_collection_point1": "To enhance your experience on the Platform.", "privacy_policy_purpose_of_data_collection_point2": "To deliver services, tools, and resources tailored to your role and needs.", "privacy_policy_purpose_of_data_collection_point3": "To facilitate effective communication, including HR announcements and notifications.", "privacy_policy_purpose_of_data_collection_point4": "To ensure compliance with internal policies and regulatory requirements.", "privacy_policy_purpose_of_data_collection_point5": "To improve the functionality and security of the Platform through analytics and feedback.", "privacy_policy_data_usage_point1": "Providing access to employee services and resources.", "privacy_policy_data_usage_point2": "Generating reports and insights for operational and performance improvements.", "privacy_policy_data_usage_point3": "Responding to technical or HR-related inquiries and support requests.", "privacy_policy_data_sharing_point1": "Human Resources: For personnel management and benefits administration.", "privacy_policy_data_sharing_point2": "IT Services: For troubleshooting, access management, and security.", "privacy_policy_data_sharing_point3": "Management: For performance tracking and operational insights.", "privacy_policy_data_protection_point1": "Encryption of data during transmission and storage.", "privacy_policy_data_protection_point2": "Access control mechanisms to restrict unauthorized access.", "privacy_policy_data_protection_point3": "Regular audits to ensure compliance with security standards.", "File": "File", "Elme Letters and Certifications Center": "Elme Letters and Certifications Center", "HR Admin": "HR Admin", "No team members with missing hours": "No team members with missing hours", "download_failed": "File download failed", "download_success": "The file has been downloaded successfully", "internal_discussion": "Internal Discussion", "closee": "Close", "reopenn": "Re-Open", "Please fill the data below to close the request": "Please fill the data below to close the request", "Please fill the data below to re-open the request": "Please fill the data below to re-open the request", "reopen": "Re-Open Task Successfully", "close": "Close Task Successfully", "Please select your rate": "Please select your rate", "800 Support Services": "800 Support Services", "Marketing": "Marketing", "Terms and Conditions": "Terms and Conditions", "Approval Template": "Approval Template", "must select template": "must select template", "Group Name": "Group Name", "SLA": "SLA", "get_approvals": "Approval Template submitted successfully", "By Clicking , you agree to our": "By Clicking , you agree to our", "must accept our terms and condition": "must accept our terms and condition", "Get approval": "Get approval", "Please get require approval": "Please get require approval", "Unstamped Attachments": "Unstamped Attachments", "Upload HR Letter": "Upload HR Letter", "Time Check! Total Missing hours is": "Time Check! Total Missing hours is", "Looks like you’ve got some missing hours! Don’t worry—just update your timesheet to keep things on track.": "Looks like you’ve got some missing hours! Don’t worry—just update your timesheet to keep things on track.", "create_time_entry": "Create Time Entry", "submit_out_of_office": "Please submit your Out-of-Office Work time and Reason", "add_slot": "Add another slot +", "save": "Save", "start_time": "Start time", "end_time": "End time", "total_time": "Total Time", "meeting_outside_elm": "Meeting outside Elm", "shift_hours": "Shift hours", "training_in_riyadh": "Training in Riyadh", "visit_external_client": "Visiting External Clients", "work_from_home": "Working from home", "write_note": "Write a note", "My Timesheet": "My Timesheet", "Out of Office": "Out of Office", "Leave and Others": "Leave and Others", "Out of": "Out of", "Consumed Days": "Consumed Days", "Available Days": "Available Days", "resolve_hr": "HR Letter Resolved Successfully", "ratingAsInt(1)": "Very Sad", "ratingAsInt(2)": "Sad", "ratingAsInt(3)": "Normal", "ratingAsInt(4)": "Good", "ratingAsInt(5)": "Very Good", "time_sheet_complaint": "Timesheet Complaint", "select_date": "Select a Date", "select_time": "Select Time", "Magic AutoFill Successfully": "Magic AutoFill Successfully", "time_sheet_tasks_msg1": "Please make sure to take action on", "time_sheet_tasks_msg2": "timesheets before Tuesday. If no action is taken, they will be automatically approved.", "approved_time_sheet_title": "Time Check! All set - No missing Hours", "approved_time_sheet_desc": "everything is perfectly tracked. Keep up the great work!", "time_sheet_complaint_desc": "Submit a complaint regarding any discrepancies in your workday or time records", "attendance_complaint_title": "Attendance Complaints", "time_sheet_title": "Time sheet", "off": "off", "Project hours can only be submitted if your Clock-in time (office) or Out-of-office hours have been approved. ": "Project hours can only be submitted if your Clock-in time (office) or Out-of-office hours have been approved.", "Ensure all time entries are complete before submitting project hours.": "Ensure all time entries are complete before submitting project hours.", "Your Total Project Hours : ": "Your total project hours : ", "Total Worked Hours": "Total worked hours", "Add Hoc Project": "Ad-Hoc Project", "Save as Draft": "Save as a draft ", "My Project Hours": "My Projects Hours", "Project WPS": "Projects WBS ", "Search by project name,  project owner, WPS code": "Search by project name, Project owner, or project WBS code ", "Project Name": "Project Name", "Project Hours": "Project Hours", "From - To": "From - To", "Duration": "Duration", "Detailed time breakdown for your team": "Detailed time breakdown for your team", "Task Assignment": "Task Assignment", "This field for Task": "This field for Task", "Eligible Hours": "Eligible Hours", "Review Employee Project Hours –SWA-Developing digital transformation st Overview": "Review Employee Project Hours –SWA-Developing digital transformation st Overview", "Review and take action on the submitted project hours for the week. Approve or reject entries as needed before finalizing the weekly submission.": "Review and take action on the submitted project hours for the week. Approve or reject entries as needed before finalizing the weekly submission.", "Task Name": "Task Name", "Rejected": "Rejected", "Submitted": "Submitted", "validate_time_out_of_office": "End time must be after start time", "overlap_out_of_office": "Overlapping time entries are not allowed", "my_team_time_sheet_title": "My Team Timesheet", "time_sheet_total_details_title": "Total Time Details", "personal_loan_title": "Personal Loan", "personal_loan_details_title": "Personal Loan Details", "company_loan_agreement": "Company Loan Agreement", "acknowledgement": "I, {name},", "employment_status": "an employee of ELM company herein submit my request for an internal loan of an amount as of {amount} (SAR), and this loan will be closed within a period of (12) months.", "monthly_deduction_agreement": "Moreover, I agree to give my full approval to the company to deduct the loan from my monthly salary in installments even if my monthly liability to company exceeds 33% of my monthly salary.", "end_of_service_clause": "Also, I authorize the company to deduct the loan balance from my end of service bonus in case of my employment contract has been terminated for whatever reason, and agree to give ELM the right to hold the Clearance Certificate until loan been settled.", "installment_note_1": "* The maximum installments period is 12 months", "installment_note_2": "* 24 months if package not exceed 10,000 SAR.", "requester_signature": "Requester and Signed By:", "time_sheet_total_details_desc": "Track your clock-ins, clock-outs, and total hours effortlessly.", "duration": "Duration", "terms_and_conditions": "Terms and Conditions", "time_sheet_empty_data": "You don’t have any recorded times yet.", "search_name_title": "Search by employee name or title", "time_sheet_submit_msg": "Your timesheet will be automatically submitted on", "time_sheet_submit_time": "at 15:00 PM.", "time_sheet_wfo": "Days working from office", "time_sheet_wfh": "Days working from home", "time_sheet_auto_fill_1": "Your daily total will be adjusted to 8 hours per day.", "time_sheet_auto_fill_2": "This applies only to the remaining missing hours for the current week.", "time_sheet_auto_fill_3": "You can review and edit your timesheet afterward if needed.", "time_sheet_proceed": "Would you like to proceed ?", "time_sheet_leave_desc": "Track your leave hours at a glance.", "Important_Note": "Important Notes:", "You_have_surpassed_your_limit_for_Advance_Salary_requests_for_this_year": "You have surpassed your limit for Advance Salary requests for this year.", "You_can_request_only_one_Advance_Salary_per_month": "You can request only one Advance Salary per month.", "You requested": "You requested", "Advanced Salary": "Advanced Salary", "Maximum_Advance_Salary_per_year_is_3_times": "Maximum Advance Salary per year is 3 times.", "Advance Salary this year": "Advance Salary this year", "Empowering you with early access to your earned salary": "Empowering you with early access to your earned salary.", "Personal Loan Pre-Closure": "Personal Loan Pre-Closure", "Settle the loan before the end of the term": "Settle the loan before the end of the term.", "available_annual_leave": "Available Annual Leave", "missing_hours": "Missing Hours", "project_hours": "Project Hours", "wfh": "WFH", "Loan Pre-Closure Amount": "Loan Pre-Closure Amount", "ELM IBAN Number": "ELM IBAN Number", "End Date": "End Date", "Attachment": "Attachment", "Loan Pre-Closure": "Loan Pre-Closure", "Total Loan": "Total Loan", "Paid": "Paid", "Monthly Installment": "Monthly Installment", "Loan Pre-Closure Note": "For pre-closure, please make the payment using the above Elm IBAN and upload the payment receipt.", "Important Notes": "Important Notes:", "You do not have an active loan to close": "You do not have an active loan to close.", "Manage & Track Your Extra Hours.": "Manage & Track Your Extra Hours.", "Duration must be equal to or less than eligible hours": "Duration must be equal to or less than eligible hours", "Recorded Hours": "Recorded Hours", "Add Project": "Add Project", "Submit for Approval": "Submit for Approval", "attendance_category": "Attendance Category", "overlap_response_msg_timesheet": "Time entry overlaps detected on the following days:", "review_overlap_timesheet": "Please review and adjust accordingly", "If you settle the loan this month, you'll need to wait until next month to request a new one": "If you settle the loan this month, you'll need to wait until next month to request a new one .", "Tender Request": "Tender Request", "Budget Type": "Budget Type", "Cost Center": "Cost Center", "Tender Name": "Tender Name", "Tender Number": "Tender Number", "Customer Name": "Customer Name", "Payment Type": "Payment Type", "Tender Value": "Tender Value", "SADAD Number": "SADAD Number", "Bank Transfer": "Bank Transfer", "Bank Cheque": "Bank Cheque", "Beneficiary Name": "Beneficiary Name", "Bank": "Bank", "IBAN": "IBAN", "5 Days (Starting after complete all approval steps)": "5 Days (Starting after complete all approval steps)", "Please enter Comments": "Please enter Comments", "Write a Comments": "Write a Comments", "must select Budget Type": "must select Budget Type", "must select Cost Center": "must select Cost Center", "must select Project": "must select Project", "must select Tender Name": "must select Tender Name", "must select Tender Number": "must select Tender Number", "must select Tender Value": "must select Tender Value", "must select Customer Name": "must select Customer Name", "must select Payment Type": "must select Payment Type", "must enter SADAD Number": "must enter SADAD Number", "must enter Beneficiary Name": "must enter Beneficiary Name", "must select Bank": "must select Bank", "must enter IBAN": "must enter IBAN", "Edit Rejected Project Hours Entry": "Edit Rejected Project Hours Entry", "This entry was rejected by your project manager. Please review and update your time details, then resubmit for approval.": "This entry was rejected by your project manager. Please review and update your time details, then resubmit for approval.", "Meeting Service": "Meeting Service", "Eligible hours represent the standard estimated time for each task and may be exceeded if additional work is performed. However, you can’t log hours beyond your assigned daily working hours or exceed the total weekly eligible hours allocated for the project": "Eligible hours represent the standard estimated time for each task and may be exceeded if additional work is performed. However, you can’t log hours beyond your assigned daily working hours or exceed the total weekly eligible hours allocated for the project", "permanent_petty_cash": "Permanent Petty Cash", "petty_cash_usage": "Petty Cash Usage", "cash_amount": "Cash Amount", "expected_closing_date": "Expected Closing Date", "iban_number": "IBAN Number", "bank_name": "Bank Name", "wrtie_purpose": "Write a Purpose", "petty_cash_policy": "Petty Cash Policy", "petty_cash_note": "5 Days (Starting after complete all approval steps)", "budget_type": "Budget Type", "Period (days)": "Period (days)", "Guarantee Type": "Guarantee Type", "Guarantee Percentage (%) ": "Guarantee Percentage (%) ", "Guarantee Value": "Guarantee Value", "Please attach the tender document": "Please attach the tender document.", "Letter of Guarantee": "Letter of Guarantee", "Bid Bond": "<PERSON><PERSON>", "must select guarantee type": "must select guarantee type", "must select date": "must select date", "must select guarantee percentage": "must select guarantee percentage", "Request a Tender Request": "Request a Tender Request", "Enterprise Architecture": "Enterprise Architecture", "Request a Letter of Guarantee": "Request a Letter of Guarantee", "related_to_board_of_directors": "Related to Board of Directors", "petty_cash_terms_title": "Terms and Conditions for the Use of Permanent Petty Cash", "petty_cash_terms_desc_1": "Below are the key points outlining the policy for using the permanent petty cash:", "petty_cash_terms_1": "1. The permanent petty cash is to be replenished on a monthly or weekly basis. At the end of the fiscal year, the petty cash balance will be reconciled or closed for the purposes of preparing the final accounts, in accordance with the company's financial and accounting policies.", "petty_cash_terms_2": "2. All petty cash expenses must be submitted with official supporting documents (invoices only).", "petty_cash_terms_3": "3. The petty cash must not be used for purchasing fixed assets such as equipment or similar items.", "petty_cash_terms_4": "4. Invoices must be tax-compliant and meet the requirements of the Zakat, Tax, and Customs Authority in order to be correctly recorded — with the exception of foreign suppliers.", "petty_cash_terms_5": "5. The invoice submission form must be completed and sent on a weekly basis to ensure timely review and proper recording within the corresponding financial period.", "petty_cash_terms_6": "6. Funds must be transferred only to the executing party (e.g., vendor or service provider) — transferring money to individuals, employees, or intermediaries is strictly prohibited.", "petty_cash_terms_7": "7. When sending the form and invoices, the email subject must be standardized as follows: Permanent Petty Cash mm-yyyy", "petty_cash_terms_desc_2": "Requirements for Tax Invoices (for vendors within Saudi Arabia): The document must be titled 'Tax Invoice'.", "petty_cash_terms_8": "1. It must include the name “Elm Company”.", "petty_cash_terms_9": "2. The address of Elm Company should be listed as:", "petty_cash_terms_10": "3. Riyadh, Al-Nakheel District, Al-Thaghr Street, Digital City.", "petty_cash_terms_11": "4. Elm Company’s VAT number: 300075658510003", "petty_cash_terms_12": "5. The invoice must include the supplier’s name, address, and VAT number.", "petty_cash_terms_13": "6. The currency must be Saudi Riyals (SAR).", "petty_cash_terms_14": "7. The 15% VAT rate must be explicitly stated.", "petty_cash_terms_15": "8. The invoice must be in Arabic.", "petty_cash_terms_end_desc": "Please ensure full compliance with the above points. If you have any questions or require further clarification, do not hesitate to contact us.", "missing_mandatoryField": "Some required fields are missing", "File:": "File", "Comments:": "Comments", "Important Note:": "Important Note:", "CEO / CEO Groups": "CEO / CEO Groups", "Meeting category": "Meeting category", "Required from the meeting": "Required from the meeting", "Purpose of the meeting": "Purpose of the meeting", "Meeting type": "Meeting type", "Meeting Subject": "Meeting Subject", "Meeting Requester": "Meeting Requester", "Meeting Attendees": "Meeting Attendees", "Note Taker": "Note Taker", "Meeting Date": "Meeting Date", "Time": "Time", "Agenda": "Agenda", "expect_after_the_meeting": "What do we expect after the meeting?", "Value Should be more than or equal 1": "Tender Value Should be more than or equal 1", "Customer ID": "Customer ID", "other": "Other", "Future date is not allowed": "Future date is not allowed", "Old date is not allowed": "Old date is not allowed", "Future time is not allowed": "Future time is not allowed", "Old time is not allowed": "Old time is not allowed", "must be greater than": "must be greater than", "must be less than": "must be less than", "must be greater than or equal to": "must be greater than or equal to", "must be less than or equal to": "must be less than or equal to", "must be equal to": "must be equal to", "must be not equal to": "must be not equal to", "back": "Back", "Step 1": "Step 1", "Step 2": "Step 2", "Write Agenda here": "Write Agenda here", "What do we expect after the meeting?": "What do we expect after the meeting?", "Please summarize the expected outcomes here.": "Please summarize the expected outcomes here.", "Search by Empolyee Name or ID": "Search by Employee Name or ID", "Select date": "Select date", "Corporate Planning": "Corporate Planning", "New Communication Allowance": "New Communication Allowance", "New Gas Allowance": "New Gas Allowance", "Extend Gas Allowance": "Extend Gas Allowance", "Extend Communication Allowance": "Extend Communication Allowance", "Cancel Gas Allowance": "Cancel Gas Allowance", "Cancel Communication Allowance": "Cancel Communication Allowance", "Beneficial Employee": "Beneficial Employee", "must select Beneficial Employee": "must select Beneficial Employee", "Eligible Amount": "Eligible Amount", "must select Eligable Amount": "must select Eligible Amount", "Number Of Months": "Number Of Months", "must select Extended Months": "must select Extended Months", "must select Number Of Months": "must select Number Of Months", "Start Date will default to the request creation date.": "Start Date will default to the request creation date.", "If the request is submitted on or before the 15th, the eligible amount will be applied starting this month otherwise will be next month.": "If the request is submitted on or before the 15th, the eligible amount will be applied starting this month otherwise will be next month.", "Employee Allowances": "Employee Allowances", "Number of Employees :": "Number of Employees :", "Add Employee": "Add Employee", "Edit Employee": "Edit Employee", "Please fill in the data below": "Please fill in the data below", "Extended Months": "Extended Months", "Select Extended Months": "Select Extended Months", "Select All": "Select All", "to cancel their allowances.": "to cancel their allowances.", "employees selected": "employees selected", "Past dates are invalid. Please choose a valid date": "Past dates are invalid. Please choose a valid date", "Action": "Action", "This employee is already added": "This employee is already added", "Request a new allowance": "Request a new allowance", "Request an extend allowance": "Request an extend allowance", "Request a cancel allowance": "Request a cancel allowance", "out_of_office_note": "Please be informed that you cannot edit or update your Out of Office entries once it is approved", "p_propose": "Success Proposition Submitted", "extend request": "extend request", "Search By Employee Name": "Search By Employee Name", "You don’t have any employees yet": "You don’t have any employees yet", "No employees were found !": "No employees were found !"}