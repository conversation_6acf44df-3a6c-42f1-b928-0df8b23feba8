import 'dart:convert';

import 'package:eeh/shared/utility/methods.dart';
import 'package:eeh/tender_request/model/submit_body_model.dart';

import '../../login/models/get_emp_info_response.dart';
import '../../shared/network/api_helper.dart';
import '../../shared/network/api_utilities.dart';
import '../../shared/network/endpoints.dart';
import '../../shared/utility/secure_storage.dart';

abstract class ITenderRequestRepo{
  Future<NetworkResponse> getPaymentTypeList({
    required String optimizedUdasValueMap,
  });

  Future<NetworkResponse> getProjectWpsList({
    required String employeeid,
  });

  Future<NetworkResponse> getBankList({
    required String employeeid,
  });

  Future<NetworkResponse> getCustomerList({
    required String searchTerm,
    required String skip,
    required String top,
  });

  Future<NetworkResponse> getSubmit({
    required TenderSubmitBody submitBodyModel ,
  });


}


class  TenderRequestRepo implements ITenderRequestRepo{
  @override
  Future<NetworkResponse> getPaymentTypeList({required String optimizedUdasValueMap}) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'optimizedUdasValueMap': optimizedUdasValueMap},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Get Dropdown list data Form",
          "moduleName": "Get Dropdown list data",
          "appKey": "TER",
          "Content-Type": "application/json",
        });

    return response;

  }
  @override
  Future<NetworkResponse> getProjectWpsList({required String employeeid}) async{

    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'employeeid': (await getEmpId())??''},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Project WPS Form",
          "moduleName": "Project WPS",
          "appKey": "NOT",
          "Content-Type": "application/json",
        });

    return response;

  }
  @override
  Future<NetworkResponse> getBankList({required String employeeid}) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'employeeid':  (await getEmpId())??''},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Bank Name Form",
          "moduleName": "Bank Name",
          "appKey": "NOT",
          "Content-Type": "application/json",
        });

    return response;

  }
  @override
  Future<NetworkResponse> getCustomerList({required String searchTerm,
    required String skip,
    required String top,}) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'search_term': searchTerm,
          'skip': skip,
          'top': top},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "CustomerInformation Form",
          "moduleName": "CustomerInformation",
          "appKey": "NOT",
          "Content-Type": "application/json",
        });

    return response;

  }
  @override
  Future<NetworkResponse> getSubmit({required TenderSubmitBody submitBodyModel }) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: submitBodyModel.toJson(),
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Service Data Form",
          "moduleName": "Service Data",
          "appKey": "TER",
          "Content-Type": "application/json",
        });

    return response;

  }
}