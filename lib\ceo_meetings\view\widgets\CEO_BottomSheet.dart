import 'package:eeh/ceo_meetings/bloc/ceo_meetings_events.dart';
import 'package:flutter/material.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../shared/utility/font_utility.dart';
import '../../../shared/widgets/cancel_button_component.dart';
import '../../../shared/widgets/submit_button_component.dart';
import '../../bloc/ceo_meetings_bloc.dart';
import '../../bloc/ceo_meetings_states.dart';
import '../../model/CEOMembers.dart';

class CEOBottomSheetWidget extends StatefulWidget {
  final CEOMeetingsBloc bloc;
  final Function(List<ElmGroupMemeb> members) onItemSelect;

  const CEOBottomSheetWidget({
    super.key,
    required this.bloc,
    required this.onItemSelect,
  });

  @override
  State<CEOBottomSheetWidget> createState() => _CEOBottomSheetWidgetState();
}

class _CEOBottomSheetWidgetState extends State<CEOBottomSheetWidget> {
  List<ElmGroupMemeb> members = [];
  @override
  void initState() {
    super.initState();
    members = getMemebrList();
  }

  @override
  Widget build(BuildContext context) {
    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    return Padding(
      padding: EdgeInsets.only(bottom: keyboardHeight),
      child: DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.6,
        expand: false,
        builder: (context, scrollController) => Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
              bloc: widget.bloc,
              buildWhen: (previous, current) =>
                  current is UpdateCEOGroupValueState,
              builder: (context, state) => getBottomSheetTitle(),
            ),
            SizedBox(
              height: 20.h,
            ),
            BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
              bloc: widget.bloc,
              buildWhen: (previous, current) =>
                  current is UpdateCEOGroupValueState,
              builder: (context, state) => getCEOListViewWidget(),
            ),
            SizedBox(
              height: 10.h,
            ),
            CancelButtonComponent(
              title: AppLocalizations.of(context).translate("Cancel"),
            ),
            SubmitButtonComponent(
              backGroung: primaryColor,
              text: AppLocalizations.of(context).translate("Add"),
              isLoading: false,
              onPressed: () {
                List<ElmGroupMemeb> selectedMembers = [];
                for (var element in members) {
                  if (element.isSelected ?? false) {
                    selectedMembers.add(element);
                  }
                }

                // Update the text field with selected members
                if (selectedMembers.isNotEmpty) {
                  String selectedText = selectedMembers
                      .map((member) => AppLocalizations.appLang == 'en'
                          ? member.nameen ?? ''
                          : member.namear ?? '')
                      .join(', ');

                  widget.bloc.ceoGroupsController.text = selectedText;
                  widget.bloc.selectedCEOMembers = selectedMembers;
                } else {
                  widget.bloc.ceoGroupsController.text = '';
                  widget.bloc.selectedCEOMembers = [];
                }

                widget.bloc.ceoMembers?.elmGroupMemeb = members;
                widget.bloc.add(ChangeFieldValueEvent());
                Navigator.pop(context);
              },
            )
          ],
        ),
      ),
    );
  }

  Widget getBottomSheetTitle() {
    return Center(
      child: Text.rich(TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context).translate("CEO / CEO Groups"),
            style: FontUtilities.getTextStyle(TextType.regular,
                fontWeight: FontWeight.w700, size: 16.sp),
          ),
        ],
      )),
    );
  }

  Widget getCEOListViewWidget() {
    return Expanded(
      child: members.isNotEmpty
          ? ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 25.0.w),
              itemBuilder: (context, index) {
                if (index == 0) {
                  return CheckboxListTile(
                    checkColor: primaryColor,
                    fillColor: WidgetStateProperty.resolveWith(
                      (states) => Colors.transparent,
                    ),
                    side: WidgetStateBorderSide.resolveWith(
                      (states) => states.contains(WidgetState.selected)
                          ? BorderSide(
                              width: 1.8,
                              color: primaryColor,
                              strokeAlign: 0.1,
                            )
                          : BorderSide(width: 2.0, color: Colors.black),
                    ),
                    checkboxShape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(3.0.sp)),
                    contentPadding: EdgeInsets.zero,
                    value: _areAllMembersSelected(),
                    onChanged: (bool? value) {
                      setState(() {
                        // Select or deselect all members
                        for (var member in members) {
                          member.isSelected = value ?? false;
                        }
                      });
                    },
                    title: Text(
                        "${AppLocalizations.of(context).translate("All")} (${members.length})"),
                  );
                }

                final memberIndex = index - 1;
                return CheckboxListTile(
                  checkColor: primaryColor,
                  fillColor: WidgetStateProperty.resolveWith(
                    (states) => Colors.transparent,
                  ),
                  side: WidgetStateBorderSide.resolveWith(
                    (states) => states.contains(WidgetState.selected)
                        ? BorderSide(
                            width: 1.8,
                            color: primaryColor,
                            strokeAlign: 0.1,
                          )
                        : BorderSide(width: 2.0, color: Colors.black),
                  ),
                  checkboxShape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(3.0.sp)),
                  contentPadding: EdgeInsets.zero,
                  value: members[memberIndex].isSelected,
                  onChanged: (bool? value) {
                    setState(() {
                      members[memberIndex].isSelected = value ?? false;
                    });
                  },
                  title: AppLocalizations.appLang == 'en'
                      ? Text(members[memberIndex].nameen ?? '')
                      : Text(members[memberIndex].namear ?? ''),
                  subtitle: Text(
                    AppLocalizations.of(context)
                        .translate("Corporate Planning"),
                  ),
                );
              },
              itemCount: members.isEmpty ? 0 : members.length + 1,
            )
          : Center(
              child: Text(
                  AppLocalizations.of(context)
                      .translate("No CEO / CEO office members"),
                  style: FontUtilities.getTextStyle(TextType.disable,
                      fontWeight: FontWeight.w400, size: 14.sp)),
            ),
    );
  }

  List<ElmGroupMemeb> getMemebrList() {
    List<ElmGroupMemeb> originalList =
        widget.bloc.ceoMembers?.elmGroupMemeb ?? [];
    return originalList.map((e) => e.copyWith()).toList();
  }

  bool _areAllMembersSelected() {
    if (members.isEmpty) return false;
    return members.every((member) => member.isSelected ?? false);
  }
}
