class ProjectName {
  String createdBy;
  String createdById;
  String createdDate;
  int recId;
  String employeeid;
  List<Projectwp> projectwps;
  String authkey;

  ProjectName({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeid,
    required this.projectwps,
    required this.authkey,
  });

  factory ProjectName.fromJson(Map<String, dynamic> json) => ProjectName(
    createdBy: json["createdBy"],
    createdById: json["createdById"],
    createdDate: json["createdDate"],
    recId: json["recId"],
    employeeid: json["employeeid"],
    projectwps: List<Projectwp>.from(json["projectwps"].map((x) => Projectwp.fromJson(x))),
    authkey: json["authkey"],
  );

  Map<String, dynamic> toJson() => {
    "createdBy": createdBy,
    "createdById": createdById,
    "createdDate": createdDate,
    "recId": recId,
    "employeeid": employeeid,
    "projectwps": List<dynamic>.from(projectwps.map((x) => x.toJson())),
    "authkey": authkey,
  };
}

class Projectwp {
  String wbscode;
  String ownernameen;
  String projectnameen;
  int ownerid;
  int recid;
  String ownernamear;

  Projectwp({
    required this.wbscode,
    required this.ownernameen,
    required this.projectnameen,
    required this.ownerid,
    required this.recid,
    required this.ownernamear,
  });

  factory Projectwp.fromJson(Map<String, dynamic> json) => Projectwp(
    wbscode: json["wbscode"],
    ownernameen: json["ownernameen"],
    projectnameen: json["projectnameen"],
    ownerid: json["ownerid"],
    recid: json["recid"],
    ownernamear: json["ownernamear"],
  );

  Map<String, dynamic> toJson() => {
    "wbscode": wbscode,
    "ownernameen": ownernameen,
    "projectnameen": projectnameen,
    "ownerid": ownerid,
    "recid": recid,
    "ownernamear": ownernamear,
  };
}
