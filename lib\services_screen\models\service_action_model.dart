import 'package:eeh/admin_service/view/layout/admin_service_layout.dart';
import 'package:eeh/allowance_services/cancel_allowance/view/cancel_allowance_view.dart';
import 'package:eeh/allowance_services/extend_allowance/view/extend_allowance_view.dart';
import 'package:eeh/allowance_services/new_allowance/view/new_allowance_view_.dart';
import 'package:eeh/attendance_complaint/view/attendance_complaint_screen.dart';
import 'package:eeh/business_card/view/business_card_view.dart';
import 'package:eeh/ceo_meetings/view/ceo_meetings_screen.dart';
import 'package:eeh/department_lunch/view/department_lunch_screen.dart';
import 'package:eeh/general_expenses_request_ui/view/general_expenses_ui.dart';
import 'package:eeh/iqama_services/view/iqama_cancellation/iqama_cancellation_employee_family_screen.dart';
import 'package:eeh/iqama_services/view/iqama_cancellation/iqama_cancellation_employee_screen.dart';
import 'package:eeh/iqama_services/view/iqama_printing/iqama_printing_screen.dart';
import 'package:eeh/issu_%D9%80Exit_Re_Entry_Visa/view/issueExitReEntryVisa_screen.dart';
import 'package:eeh/leave_request/view/leave_request_screen.dart';
import 'package:eeh/lms_services/views/my_learning_view.dart';
import 'package:eeh/lms_services/views/my_team_learning_view.dart';
import 'package:eeh/mazaya_request/view/mazaya_request.dart';
import 'package:eeh/iban_number/view/ibanRequestScreen.dart';
import 'package:eeh/my_project_hours_service/view/screens/project_hours_view.dart';
import 'package:eeh/petty_cash/view/petty_cash_screen.dart';
import 'package:eeh/school_allowance/school_allowance_screen.dart';
import 'package:eeh/services_screen/models/service_model.dart';
import 'package:eeh/services_screen/screens/services_screen.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/timesheet_service/view/screens/my_time_sheet_screen.dart';
import 'package:eeh/travel_requset/presentation/travel_request_layout.dart';
import 'package:eeh/visa_services/presentation/views/cancel_re_entry_visa/cancel_re_entry_visa.dart';
import 'package:eeh/visa_services/presentation/views/extend_exit_re_entry_visa/extend_exit_re_entry_visa.dart';
import 'package:flutter/material.dart';

import '../../iqama_services/view/iqama_renewal/iqama_renewal_screen.dart';
import '../../iqama_services/view/iqama_issue/iqama_issue_for_employee_screen.dart';

import '../../letter_of_guarantee/view/letter_of_guarantee_screen.dart';
import '../../loan_preClosure/views/loan_preClosure_screen.dart';
import '../../my_team_timesheet_service/presentation/my_team_timesheet_screen.dart';
import '../../passport/presentation/screens/passport_update_request_view.dart';

import '../../education/update_education_screen.dart';
import '../../salary_advance/views/salary_advance_screen.dart';
import '../../personal_loan/views/personal_loan_view.dart';
import '../../tender_request/views/tender_request_screen.dart';

abstract class ServiceItemTapped {
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName);
}

class ServiceScreenTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreen(
        context,
        ServicesScreen(
          screenType:
              serviceModel.serviceScreenType ?? ServiceScreenType.typeScreen,
          currentServiceName: serviceModel.title ?? '',
          previousServiceName: previousServiceScreenName,
          servicesList: serviceModel.subServiceModel ?? [],
        ));
  }
}

class LeaveRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const LeavesRequestScreen());
  }
}

class UpdateEducationTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const UpdateEducationScreen());
  }
}

class GeneralExpensesRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context,
        ExpensesScreen(
          serviceId: serviceModel.id ?? "-1",
        ));
  }
}

class SchoolAllowanceRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, SchoolAllowanceScreen());
  }
}

class MazayaRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context,
        MazayaRequest(
          requestID: serviceModel.id ?? "-1",
          isEditMode: false,
        ));
  }
}

class TravelRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const TravelRequestLayout());
  }
}

class LoanPreClosureRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const LoanPreClosureScreen());
  }
}

class TenderRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const TenderRequestView());
  }
}

class LetterofGuaranteeTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const LetterOfGuaranteeScreen());
  }
}

class IqamaPrintingTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const IqamaPrintingScreen());
  }
}

class IqamaRenewalTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const IqamaRenewalScreen());
  }
}

class IqamaCancellationTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const IqamaCancellationEmployeeScreen());
  }
}

class IqamaCancellationFamilyTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const IqamaCancellationEmployeeFamilyScreen());
  }
}

class BusinessCardRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const BusinessCardView());
  }
}

class SalaryAdvanceRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, const SalaryAdvanceScreen());
  }
}

class PassportUpdateRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, PassportUpdateRequestView());
  }
}

class IbanUpdateRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, const IBANUpdate());
  }
}

class DepartmentLunchRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, DepartmentLunchScreen());
  }
}

class IqamaIssueHrRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, IqamaIssueForEmployeeScreen());
  }
}

class IssueExitReEntryVisaRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, IssueExitReEntryVisaScreen());
  }
}

class ExtendReEntryVisaRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, ExtendExitReEntryVisaScreen());
  }
}

class CancelReEntryVisaRequestTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, CancelReEntryVisaScreen());
  }
}

class AdminAndFacilityTapped implements ServiceItemTapped {
  String? tabelName;
  AdminAndFacilityTapped({this.tabelName});
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context,
        AdminServiceLayout(
          tableName: tabelName,
        ));
  }
}

class AttendaceComplaintTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(
        context, AttendanceComplaintScreen());
  }
}

class TimesheetTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, MyTimeSheetScreen());
  }
}

class MyTeamTimesheetTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, MyTeamTimeSheetScreen());
  }
}

class MyProjectHoursTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, ProjectHoursView());
  }
}

class PersonalLoanTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, PersonalLoanScreen());
  }
}

class PettyCashTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, PettyCashScreen());
  }
}

class CEOMeetingTapped implements ServiceItemTapped {
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, CEOMeetingsScreen());
  }
}

class NewAllowanceTapped implements ServiceItemTapped {
  bool isGas;
  NewAllowanceTapped(this.isGas);
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, NewAllowanceView(isGas: isGas,));
  }
}

class ExtendAllowanceTapped implements ServiceItemTapped {
  bool isGas;
  ExtendAllowanceTapped(this.isGas);
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, ExtendAllowanceView(isGas: isGas,));
  }
}

class CancelAllowanceTapped implements ServiceItemTapped {
  bool isGas;
  CancelAllowanceTapped(this.isGas);
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, CancelAllowanceView(isGas: isGas,));
  }
}
class MyLearningTapped implements ServiceItemTapped {

  MyLearningTapped();
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, MyLearningView());}
}
class MyTeamLearningTapped implements ServiceItemTapped {

  MyTeamLearningTapped();
  @override
  void onItemTapped(BuildContext context, ServiceModel serviceModel,
      String previousServiceScreenName) {
    Navigation.navigateToScreenWithTransition(context, MyTeamLearningView());}
}
