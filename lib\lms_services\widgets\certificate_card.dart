import 'package:cached_network_image/cached_network_image.dart';
import 'package:eeh/education/models/certificate_response_model.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/lms_services/utils/lms_utils.dart';
import 'package:eeh/lms_services/widgets/certificate_View.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/lms_services/widgets/detail_row.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';
import 'package:path/path.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';


class CertificateCard extends StatelessWidget {  final double progress;
  final DateTime? dueDate;
  final VoidCallback? onCourseLaunch;

  const CertificateCard({
    Key? key,
    required this.progress,
    this.dueDate,
    this.onCourseLaunch,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
   
    final status = "certificate";

    double progressPercentage = progress / 100;

    return Container(
      padding: EdgeInsets.all(16).h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: borderColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildImageSection("imageUrl"),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 12.h),
              _buildDetailRowSection(context),
              SizedBox(height: 8.h),
              _buildTitle("title"),
              SizedBox(height: 8.h),
              _buildDescription("description"),
              SizedBox(height: 16.h),
              _buildStatusCard(context, status),
              SizedBox(height: 16.h),
              _buildProgressBar(progressPercentage, status),
              SizedBox(height: 16.h),
              _buildActionButton(context, status),
              SizedBox(height: 16.h),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(String? imageUrl) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: CachedNetworkImage(
            imageUrl: imageUrl ?? "",
            height: 150.h,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 150.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: CircularProgressIndicator(
                  color: primaryColor,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 150.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                getIconFromCss('fa-solid fa-book'),
                color: Colors.grey,
                size: 50.sp,
              ),
            ),
          ),
        ),
        Positioned(
          right: AppLocalizations.appLang == 'en' ? 0 : null,
          left: AppLocalizations.appLang == 'en' ? null : 0,
          child: Padding(
            padding: EdgeInsets.all(16),
            child: DetailRow(
                label: "Course",
                icon: getIconFromCss("fa-solid fa-airplay"),
                onPressed: () {}),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRowSection(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          DetailRow(
            label: "Certificate",
            icon: getIconFromCss("fa-regular fa-file-certificate"),
            onPressed: () {},
            borderColor: borderColor,
            textColor: textMain,
            iconColor: textMain,
          ),
          SizedBox(width: 8.w),
          dueDate != null
              ? DetailRow(
                  label: "Due Date ${LmsUtils.formatDate(dueDate.toString())}",
                  icon: getIconFromCss("fa-regular fa-file-certificate"),
                  onPressed: () {},
                  borderColor: borderColor,
                  textColor: textMain,
                  iconColor: textMain,
                )
              : SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: FontUtilities.getTextStyle(
        TextType.medium,
        size: 16.sp,
        fontWeight: FontWeight.bold,
        textColor: textMain,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildDescription(String description) {
    return Text(
      description,
      style: FontUtilities.getTextStyle(
        TextType.medium,
        size: 14.sp,
        textColor: textSecondary,
      ),
      maxLines: 5,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildStatusCard(BuildContext context, String displayStatus) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: LmsUtils.getStatusColor(displayStatus).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(
          color: LmsUtils.getStatusColor(displayStatus),
          width: 1,
        ),
      ),
      child: Text(
        LmsUtils.getStatusText(context, displayStatus),
        style: FontUtilities.getTextStyle(
          TextType.medium,
          size: 12.sp,
          fontWeight: FontWeight.w500,
          textColor: LmsUtils.getStatusColor(displayStatus),
        ),
      ),
    );
  }

  Widget _buildProgressBar(double progressPercentage, String displayStatus) {
    return Row(
      children: [
        Expanded(
          child: LinearPercentIndicator(
            lineHeight: 8.h,
            percent: progressPercentage,
            backgroundColor: Colors.grey[200],
            progressColor: LmsUtils.getStatusColor(displayStatus),
            barRadius: Radius.circular(4.r),
            padding: EdgeInsets.zero,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          'Completed ',
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 12.sp,
            textColor: textSecondary,
          ),
        ),
        Text(
          '${(progressPercentage * 100).round()}%',
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 12.sp,
            textColor: LmsUtils.getStatusColor(displayStatus),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context, String displayStatus) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        onPressed: () {
          // _launchCourse(context);
        },
        child: Text(
          LmsUtils.getActionButtonText(context, displayStatus),
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 14.sp,
            fontWeight: FontWeight.w500,
            textColor: Colors.white,
          ),
        ),
      ),
    );
  }

}
