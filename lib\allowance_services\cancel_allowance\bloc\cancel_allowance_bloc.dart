import 'package:eeh/allowance_services/cancel_allowance/bloc/cancel_allowance_event.dart';
import 'package:eeh/allowance_services/cancel_allowance/bloc/cancel_allowance_state.dart';
import 'package:eeh/allowance_services/model/allawance_list_response_model.dart';
import 'package:eeh/allowance_services/model/submit_allowance_request_body.dart';
import 'package:eeh/allowance_services/model/submit_allowance_response_model.dart';
import 'package:eeh/allowance_services/repo/allowance_repo.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CancelAllowanceBloc
    extends Bloc<CancelAllowanceEvent, CancelAllowanceState> {
  TextEditingController commentController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  List<Employee> checkedList = [];
  List<Employee> filterList = [];
  bool isSelectAll = false;
  late AllowanceRepo repo;
  AllowanceListResponse? allowanceListResponse;
  bool isGas = false;
  SubmitAllowanceResponse? submitResponse;
  CancelAllowanceBloc() : super(CancelAllowanceInitialState()) {
    on<CancelAllowanceInitialEvent>((event, emit) {
      repo = AllowanceRepo(isGas: event.isGas);
      isGas = event.isGas;
      emit(CancelAllowanceInitialState());
    });

    on<SelectAllItemEvent>((event, emit) {
      onSelectAllItem(event.isSelected);
      emit(SelectAllItemState());
    });

    on<CheckItemEvent>((event, emit) {
      onSelectItem(event.index, event.value);
      emit(CheckItemState());
    });

    on<EmployeeAllowanceEvent>((event, emit) async {
      emit(EmployeeAllowanceLoadingState());
      await fetchEmployeeAllowance(emit);
    });

    on<SubmitAllowanceEvent>((event, emit) async {
      emit(SubmitAllowanceLoadingState());
      await submitAllowance(emit);
    });

    on<SearchEmployeeEvent>((event, emit) {
      searchEmployee(event.value);
      emit(SearchEmployeeState());
    });
  }

  onSelectAllItem(value) {
    isSelectAll = value;
    for (var element in filterList) {
      element.isChecked = isSelectAll;
      if (value == true && !checkedList.contains(element)) {
        checkedList.add(element);
      } else if (value == false && checkedList.contains(element)) {
        checkedList.remove(element);
      }
    }
  }

  onSelectItem(int index, bool? value) {
    final item = filterList[index];
    item.isChecked = value;
    if (value == true) {
      checkedList.add(item);
    } else {
      checkedList.remove(item);
    }
    if (checkedList.length == filterList.length || filterList.every((item) => checkedList.contains(item))) {
      isSelectAll = true;
    } else {
      isSelectAll = false;
    }
  }

  fetchEmployeeAllowance(emitter) async {
    await repo
        .fetchAllownceList(requestType: 'all')
        .then((response) => onFetchEmployeeAllowance(response, emitter));
  }

  onFetchEmployeeAllowance(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchEmployeeAllowanceSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(EmployeeAllowanceErrorState());
    });
  }

  onFetchEmployeeAllowanceSuccess(data, emitter) {
    allowanceListResponse = AllowanceListResponse.fromJson(data);
    filterList = allowanceListResponse?.employees ?? [];
    emitter(EmployeeAllowanceSuccessState());
  }

  submitAllowance(emitter) async {
    await repo
        .supmitAllowance(
            employeesAllowa: getRequestBody(), appKey: isGas ? 'CGA' : 'CCA')
        .then((response) => onSubmitAllowance(response, emitter));
  }

  onSubmitAllowance(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitAllowanceSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitAllowanceErrorState());
    });
  }

  onSubmitAllowanceSuccess(data, emitter) {
    submitResponse = SubmitAllowanceResponse.fromJson(data);
    emitter(SubmitAllowanceSuccessState());
  }

  getRequestBody() {
    List<Employees> body = checkedList.map((emp) {
      return Employees(
        employeeId: getNumofMon(emp.employeeid ?? ''),
        employeeNameAr: emp.employeenamear,
        employeeNameEn: emp.employeenameen,
        startDate: emp.startdate,
        endDate: emp.enddate,
        eligibleAmount: emp.eligibleamount,
        numberOfMonths: getNumofMon(emp.numberofmonths ?? ''),
      );
    }).toList();
    return SupmitAllowanceBody(
        employeesallowa: body,
        numOfEmps: checkedList.length.toString(),
        cancelComment: commentController.text);
  }

  getNumofMon(String num) {
    final match = RegExp(r'\d+').firstMatch(num);
    int? numofMon = match != null ? int.tryParse(match.group(0)!) : null;
    return numofMon;
  }

  searchEmployee(String v) {
    bool? isChecked;
    if (v.isEmpty) {
      filterList = allowanceListResponse?.employees ?? [];
        isChecked = filterList.every((item) => checkedList.contains(item));
    } else {
      filterList = [];
      for (var item in allowanceListResponse?.employees ?? []) {
        if ((AppLocalizations.appLang == 'en'
                ? item.employeenameen
                : item.employeenamear)
            .toString()
            .toLowerCase()
            .contains(v.toLowerCase())) {    
          filterList.add(item);
          isChecked = filterList.every((item) => checkedList.contains(item));
        }
      }
    }
    if (isChecked== true) {
      isSelectAll = true;
    } else {
      isSelectAll = false;
    }
  }
}
