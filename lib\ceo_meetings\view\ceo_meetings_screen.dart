import 'package:eeh/ceo_meetings/bloc/ceo_meetings_events.dart';
import 'package:eeh/ceo_meetings/bloc/ceo_meetings_states.dart';
import 'package:eeh/ceo_meetings/view/widgets/ceo_meeting_stepper.dart';
import 'package:eeh/ceo_meetings/view/widgets/ceo_step_one.dart';
import 'package:eeh/ceo_meetings/view/widgets/ceo_step_two.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_bloc.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_event.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../bloc/ceo_meetings_bloc.dart';
import '../repo/ceo_meetings_repo.dart';

class CEOMeetingsScreen extends StatefulWidget {
  const CEOMeetingsScreen({super.key});

  @override
  State<CEO<PERSON>eetingsScreen> createState() => _CEOMeetingsScreenState();
}

class _CEOMeetingsScreenState extends State<CEOMeetingsScreen> {
  int _currentStep = 0;
  late CEOMeetingsBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider<CEOMeetingsBloc>(
            create: (context) =>
                CEOMeetingsBloc(cEOMeetingsRepo: CEOMeetingsRepo.instance)
                  ..add(CEOMeetingsInitialEvent()),
          ),
          BlocProvider<FavoriteBloc>(
            create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
          ),
          BlocProvider<AttachBloc>(
            create: (BuildContext context) {
              return AttachBloc()..add(AttachInitialEvent());
            },
          )
        ],
        child: Builder(builder: (context) {
          _bloc = context.read<CEOMeetingsBloc>();
          return Scaffold(
            resizeToAvoidBottomInset: true,
            appBar: _getAppbarWidget(),
            body: getCustomStepper(context),
          );
        }));
  }

  List<Step> getScreens(BuildContext context) {
    return [
      Step(
        title: const Text('1'),
        content: Builder(builder: (context) {
          return CEOStepOneView();
        }),
        isActive: _currentStep == 0,
        state: _currentStep > 0
            ? StepState.complete
            : _currentStep == 0
                ? StepState.editing
                : StepState.disabled,
      ),
      Step(
        title: const Text('2'),
        content: Builder(builder: (context) {
          return CEOStepTwoView();
        }),
        isActive: _currentStep == 1,
        state: _currentStep > 1
            ? StepState.complete
            : _currentStep == 1
                ? StepState.editing
                : StepState.disabled,
      )
    ];
  }

  moveToNextStep() {
    _currentStep++;
    _bloc.add(MoveToScreenEvent());
  }

  Widget getCustomStepper(BuildContext context) {
    return BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
        buildWhen: (previous, current) => current is MoveToScreenState,
        builder: (context, state) => CEOMeetingStepper(
              withCancel: false,
              currentStep: _currentStep,
              onStepContinue: () {
                if (_currentStep < getScreens(context).length) {
                  if (_currentStep == 0) {
                    ///validate
                    moveToNextStep();
                    // }
                  } else if (_currentStep == 1) {
                    ///validate
                    moveToNextStep();
                    // }
                  } else {
                    /// here submit
                  }
                }
              },
              onStepCancel: () {
                if (_currentStep > 0) {
                  _currentStep--;
                  context.read<CEOMeetingsBloc>().add(MoveToScreenEvent());
                }
              },
              steps: getScreens(context),
            ));
  }

  PreferredSize _getAppbarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: FavoriteComponent(
        id: "37",
        title: AppLocalizations.of(context).translate('Meeting Service'),
      ),
    );
  }
}

OutlineInputBorder getNoteWidgetOutlineInputBorder() {
  return OutlineInputBorder(
    borderSide: const BorderSide(
      color: Color.fromRGBO(185, 200, 252, 0.5),
    ),
    borderRadius: BorderRadius.circular(10),
  );
}
