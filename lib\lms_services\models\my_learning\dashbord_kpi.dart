class DashboardKpi {
  final String total;
  final String courses;
  final String quizzes;
  final String certificates;
  final String? inProgress;
  final String completed;
  final String? overdue;
  final String planned;

  DashboardKpi({
    required this.total,
    required this.courses,
    required this.quizzes,
    required this.certificates,
    this.inProgress,
    required this.completed,
    this.overdue,
    required this.planned,
  });

  factory DashboardKpi.fromJson(Map<String, dynamic> json) {
    return DashboardKpi(
      total: json['total'] ?? '0',
      courses: json['courses'] ?? '0',
      quizzes: json['quizzes'] ?? '0',
      certificates: json['certificates'] ?? '0',
      inProgress: json['inprogress'],
      completed: json['completed'] ?? '0',
      overdue: json['overdue'],
      planned: json['planned'] ?? '0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'courses': courses,
      'quizzes': quizzes,
      'certificates': certificates,
      'inprogress': inProgress,
      'completed': completed,
      'overdue': overdue,
      'planned': planned,
    };
  }
  // Convenience getters for integer values
  int get totalCount => int.tryParse(total) ?? 0;
  int get coursesCount => int.tryParse(courses) ?? 0;
  int get quizzesCount => int.tryParse(quizzes) ?? 0;
  int get certificatesCount => int.tryParse(certificates) ?? 0;
  int get completedCount => int.tryParse(completed) ?? 0;
  int get plannedCount => int.tryParse(planned) ?? 0;
  int get inProgressCount => int.tryParse(inProgress ?? '0') ?? 0;
  int get overdueCount => int.tryParse(overdue ?? '0') ?? 0;
}
