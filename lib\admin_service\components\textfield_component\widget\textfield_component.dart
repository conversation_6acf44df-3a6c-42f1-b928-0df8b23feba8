import 'package:eeh/admin_service/components/TextField_component/bloc/TextField_event.dart';
import 'package:eeh/admin_service/components/textfield_component/bloc/textfield_bloc.dart';
import 'package:eeh/shared/style.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextfieldComponent extends StatefulWidget {
 final Function(String)? onChanged;
 final FormFieldValidator? validator;
 final String labelText;
//  final TextEditingController controller = TextEditingController();
 final bool isMandatory;

 const TextfieldComponent({
    super.key,
    required this.onChanged,
    required this.validator,
    required this.labelText,
    this.isMandatory = false,
  });

  @override
  State<TextfieldComponent> createState() => _TextfieldComponentState();
}

class _TextfieldComponentState extends State<TextfieldComponent> with AutomaticKeepAliveClientMixin {
    
  @override
  bool get wantKeepAlive => true;
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create: (context) => TextFieldBloc()..add(TextFieldInitialEvent()),
      child: buildWidget(),);
  }

  buildWidget(){
    return  TextFormField(
      onChanged: widget.onChanged,
      // controller: controller,
      validator: widget.validator,
      keyboardType:TextInputType.text,
      style:FontUtilities.getTextStyle(TextType.regular,
            size:12.sp,fontWeight:FontWeight.w400),
      decoration:InputDecoration(
              enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Color(0xff949494))),
              label: Text.rich(TextSpan(children: <TextSpan>[
                TextSpan(
                  style: FontUtilities.getTextStyle(TextType.medium,
                      size:12.sp,fontWeight:FontWeight.w500),
                  text: widget.labelText,
                ),
                TextSpan(
                    text: widget.isMandatory?' *' : '',
                    style: mandatoryStyle),
              ])),
            ),
    );
  }
}