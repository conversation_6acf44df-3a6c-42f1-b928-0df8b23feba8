class SubmitRequestBody {


  final dynamic totalLoan;
  final dynamic remainingAmount;
  final dynamic paid;
  final dynamic loanPreClosureA;
  final String? settlementDate;
  final dynamic loanId;
  SubmitRequestBody({

    this.totalLoan,
    this.remainingAmount,
    this.paid,
    this.loanPreClosureA,
    this.settlementDate,
    this.loanId,
  });
  factory SubmitRequestBody.fromJson(Map<String, dynamic> json) {
    return SubmitRequestBody(

      totalLoan: json['totalloan'],
      remainingAmount: json['remainingamount'],
      paid: json['paid'],
      loanPreClosureA: json['loanpreclosurea'],
      loanId: json['loanid'],
      settlementDate: json['settlementdate'],
    );
  }
}
