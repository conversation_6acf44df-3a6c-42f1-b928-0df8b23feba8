import 'dart:convert';
import 'package:eeh/loan_preClosure/bloc/loan_preclousr_state.dart';
import 'package:eeh/loan_preClosure/model/submit_request_model.dart';
import 'package:eeh/loan_preClosure/repo/loan_precloser_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../login/models/get_emp_info_response.dart';
import '../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../shared/attachment_uda/bloc/attach_event.dart';
import '../../shared/attachment_uda/model/attachment_request_model.dart';
import '../../shared/network/api_helper.dart';
import '../../shared/utility/secure_storage.dart';
import '../../shared/widgets/toast.dart';
import '../model/employee_loaninfo_model.dart';
import '../model/submit_request_body.dart';
import 'loan_preclousr_event.dart';

class LoanPreClousrBloc extends Bloc<LoanPreClousrEvent,LoanPreClousrState>{
  GetEmployeeInfoResponse? empInfo;
  LoanStatusModel? employeeInfo;
  LoanPreClosurRepo repo = LoanPreClosurRepo();
  AttachmentRequestBody attachmentRequestBody =
  AttachmentRequestBody(attachmentUdaId: '', objectId: '');
  SubmitRequestResponse submitRequestResponse=SubmitRequestResponse();
  bool isEditMode = false;
  late BuildContext ctx;
  bool isLoading = false;

  LoanPreClousrBloc() : super(LoanPreClousrInitialState()){

    on<LoanPreClousrInitialEvent>((event, emit) async {
      await getEmpInfo();
      emit(LoanPreClousrInitialState());

    });

    on<FetchEmployeeInfoEvent>((event, emit) async {
      emit(FetchEmployeeInfoLoadingState());
      await fetchEmployeeInfo(emit);
    });

    on<UploadAttachmentFilesEvent>((event, emit) async {
      emit(UploadAttachmentLoadingState());
      await uploadAttachmentFiles(emit);
    });


    on<FilesUpdatedEvent>((event, emit) async {
      emit(FilesUpdatedState());
    });

    on<SubmitRequestEvent>((event, emit) async {
      await submitRequest(emit);
    });




  }


  Future getEmpInfo() async {
    String? jsonString =
    await SecureStorageService().readSecureData(key: "emp_info_response");
    if (jsonString != null) {
      Map<String, dynamic> jsonMap = jsonDecode(jsonString);
      empInfo = GetEmployeeInfoResponse.fromJson(jsonMap);
    }
  }

  fetchEmployeeInfo(emitter) async {
    await repo
        .fetchEmployeeInfo(employeeid: empInfo?.employeeid ?? '')
        .then((response) => onFetchEmployeeInfo(response, emitter));
  }

  void onFetchEmployeeInfo(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchEmployeeInfoSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(FetchEmployeeInfoErrorState());
    });
  }

  void onFetchEmployeeInfoSuccess(data, emitter) {
    employeeInfo = LoanStatusModel.fromJson(data);
    emitter(FetchEmployeeInfoSuccessState(employeeInfo!));
  }



  bool isValidAttach() => AttachBloc.instance(ctx).files.isNotEmpty &&
      AttachBloc.instance(ctx).files.any((file) => file.isNetworkFile == false);



  Future uploadAttachmentFiles(emitter) async {
    if (AttachBloc.instance(ctx).files.isNotEmpty) {
      {
        AttachBloc.instance(ctx).add(UploadFilesEvent(
            false,
            submitRequestResponse.recId,
            submitRequestResponse.recId,
            AttachBloc.instance(ctx).metaDataResponse?.attachmentId ?? ''));
      }
    }

    emitter(UploadAttachmentFilesSuccessState());

  }
/*  Future<void> submitRequest(Emitter<LoanPreClousrState> emit) async {
    emit(SubmitRequestLoadingState());

    try {
      final newFiles = AttachBloc.instance(ctx).files
          .where((file) => file.isNetworkFile == false)
          .toList();

      final serializedAttachments = newFiles.map((file) => file.toJson()).toList();

      final response = await repo.submitRequest(serializedAttachments);

      response.maybeWhen(
        ok: (data) async {
          submitRequestResponse = SubmitRequestResponse.fromJson(data);

          if (submitRequestResponse.recId != null) {
            await uploadAttachmentFiles(emit);
          }

          emit(SubmitRequestSuccessState());
        },
        onError: (error) {
          showMessage(error.toString(), MessageType.error);
          emit(SubmitRequestErrorState());
        },
      );
    } catch (e) {
      showMessage("Request submission failed: $e", MessageType.error);
      emit(SubmitRequestErrorState());
    }
  }

*/

  Future<void> submitRequest(Emitter emit) async {
    emit(SubmitRequestLoadingState());

    await repo.submitRequest((employeeInfo?.loans.first.loanId)??'',
        (employeeInfo?.loans.first.remainingAmount)??'',
        (employeeInfo?.loans.first.totalAmount)??'',
        (employeeInfo?.loans.first.paidAmount)??'',
        (employeeInfo?.loans.first.remainingAmount)??'',
        DateFormat('yyyy-MM-dd').format(DateTime.now())
    ).then((response) async{
      onsubmitRequestResponse(response, emit);
      // await uploadAttachmentFiles(emit);
    }).catchError((e) {
      showMessage("Request submission failed: $e", MessageType.error);
      emit(SubmitRequestErrorState());
    });
  }



  void onsubmitRequestResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onsubmitRequestSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitRequestErrorState());
    });
  }


  void onsubmitRequestSuccess(data, Emitter emitter) {
    submitRequestResponse = SubmitRequestResponse.fromJson(data);
    attachmentRequestBody.objectId = submitRequestResponse.recId.toString();

    emitter(SubmitRequestSuccessState());
  }


}