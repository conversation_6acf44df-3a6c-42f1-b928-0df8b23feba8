import 'package:eeh/lms_services/models/my_learning/learning_data_model.dart';

class MyLearningResponseModel {
  final String lmsGetMyLearning;
  final LearningData learningData;

  MyLearningResponseModel({
    required this.lmsGetMyLearning,
    required this.learningData,
  });

  // Handle both List and single object responses
  factory MyLearningResponseModel.fromJson(dynamic json) {
    Map<String, dynamic> jsonMap;
    
    // If json is a List, take the first element
    if (json is List && json.isNotEmpty) {
      jsonMap = json[0] as Map<String, dynamic>;
    } else if (json is Map<String, dynamic>) {
      jsonMap = json;
    } else {
      throw Exception('Invalid JSON format for MyLearningResponseModel');
    }

    final lmsDataString = jsonMap['lms_get_my_learning'] as String;
    final learningData = LearningData.fromJsonString(lmsDataString);
    
    return MyLearningResponseModel(
      lmsGetMyLearning: lmsDataString,
      learningData: learningData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lms_get_my_learning': lmsGetMyLearning,
    };
  }
}
