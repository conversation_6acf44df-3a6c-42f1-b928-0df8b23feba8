
import '../../shared/widgets/general_text_form_field.dart';

class BudgetTypeModel extends GeneralSheetContent {
  String? nameAr;
  String? nameEn;
  String? id;
  List<String> requiredFields;

  BudgetTypeModel({
    this.nameAr,
    this.nameEn,
    this.id,
    this.requiredFields = const [],
  }) {
    nameen = nameEn;
    namear = nameAr;
  }

  factory BudgetTypeModel.fromJson(Map<String, dynamic> json) {
    return BudgetTypeModel(
      nameAr: json['name_ar'],
      nameEn: json['name_en'],
      id: json['id'],
      requiredFields: List<String>.from(json['required_fields'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name_ar': nameAr,
      'name_en': nameEn,
      'id': id,
      'required_fields': requiredFields,
    };
  }
}

class PaymentTypeModel extends GeneralSheetContent {
  String? nameAr;
  String? nameEn;
  List<String> requiredFields;

  PaymentTypeModel({
    this.nameAr,
    this.nameEn,
    this.requiredFields = const [],
  }) {
    nameen = nameEn;
    namear = nameAr;
  }

  factory PaymentTypeModel.fromJson(Map<String, dynamic> json) {
    return PaymentTypeModel(
      nameAr: json['name_ar'],
      nameEn: json['name_en'],
      requiredFields: List<String>.from(json['required_fields'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name_ar': nameAr,
      'name_en': nameEn,
      'required_fields': requiredFields,
    };
  }
}



class CustomerNameModel extends GeneralSheetContent {
  String? nameAr;
  String? nameEn;


  CustomerNameModel({
    this.nameAr,
    this.nameEn,

  }) {
    nameen = nameEn;
    namear = nameAr;
  }

  factory CustomerNameModel.fromJson(Map<String, dynamic> json) {
    return CustomerNameModel(
      nameAr: json['name_ar'],
      nameEn: json['name_en'],

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name_ar': nameAr,
      'name_en': nameEn,

    };
  }
}


class ProjectModel extends GeneralSheetContent {
  String? nameAr;
  String? nameEn;

  ProjectModel({
    this.nameAr,
    this.nameEn,

  }) {
    nameen = nameEn;
    namear = nameAr;
  }

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    return ProjectModel(
      nameAr: json['name_ar'],
      nameEn: json['name_en'],

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name_ar': nameAr,
      'name_en': nameEn,

    };
  }
}