import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/lms_services/bloc/bloc/lms_bloc.dart';
import 'package:eeh/lms_services/utils/lms_utils.dart';
import 'package:eeh/lms_services/widgets/course_card.dart';
import 'package:eeh/lms_services/widgets/info_card.dart';
import 'package:eeh/lms_services/widgets/stat_card.dart';
import 'package:eeh/lms_services/widgets/year_filter_field.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../../shared/favorite_component/bloc/favorite_event.dart';

class MyLearningView extends StatefulWidget {
  const MyLearningView({super.key});
  @override
  State<MyLearningView> createState() => _MyLearningViewState();
}

class _MyLearningViewState extends State<MyLearningView> {
  String selectedFilter = 'Total'; // Track selected filter

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
        BlocProvider(
          create: (context) => LmsBloc()..add(LmsGetSheetIdEvent()),
        ),
      ],
      child: BlocConsumer<LmsBloc, LmsState>(
        listener: (context, state) {
          if (state is GetLmsSheetIdSuccessState) {
            print('GetLmsSheetIdSuccessState Sheet ID: ${state.model.sheetId}');
            context.read<LmsBloc>().add(
                LmsGetMyLearningEvent(sheetId: state.model.sheetId ?? "0"));
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: getAppbarWidget(),
            body: BlocConsumer<LmsBloc, LmsState>(
              listener: (context, state) {},
              buildWhen: (previous, current) =>
                  current is GetLmsSheetIdSuccessState ||
                  current is GetLmsMyLearningSuccessState ||
                  current is GetLmsMyLearningLoadingState ||
                  current is GetLmsMyLearningErrorState,
              builder: (context, state) {
               if (state is GetLmsMyLearningSuccessState) {
                  return SafeArea(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: CustomScrollView(
                          slivers: [
                            getMyDashboardCardInfo(context),
                            getYearFilterField(context),
                            getSpace(),
                            getStatisticsCards(context),
                            getSpace(),
                            getEmployeeCardInfo(context),
                            getSpace(),
                            getCoursesList(),
                          ],
                        ),
                      ),
                    ),
                  );
                }else{
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }

  getCoursesList() {
    return BlocBuilder<LmsBloc, LmsState>(
      buildWhen: (previous, current) => current is GetLmsMyLearningSuccessState,
      builder: (context, state) {
        final lmsBloc = context.read<LmsBloc>();
        if (lmsBloc.myLearningResponseModel == null ||
            lmsBloc.myLearningResponseModel!.learningData.itemDetails.isEmpty) {
          return SliverToBoxAdapter(
            child: Center(
              child: Text(
                AppLocalizations.of(context).translate("No courses found"),
                style: TextStyle(fontSize: 16.sp),
              ),
            ),
          );
        }
        return SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => CourseCard(
              itemDetail: lmsBloc
                  .myLearningResponseModel?.learningData.itemDetails[index],
            ),
            childCount: lmsBloc
                .myLearningResponseModel?.learningData.itemDetails.length,
          ),
        );
      },
    );
  }

  SliverToBoxAdapter getEmployeeCardInfo(BuildContext context) {
    return SliverToBoxAdapter(
      child: InfoCard(
        title: AppLocalizations.of(context).translate("My Courses & Certificates"),
        icon: getIconFromCss("fa-solid fa-graduation-cap"),
      ),
    );
  }

  SliverToBoxAdapter getSpace() {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 16.h,
      ),
    );
  }

  Widget getStatisticsCards(BuildContext context) {
    return BlocBuilder<LmsBloc, LmsState>(
      builder: (context, state) {
        final statisticsCards = LmsUtils.getStatisticsCards(context, state);
        return SliverGrid(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              return statisticsCards[index];
            },
            childCount: statisticsCards.length,
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childAspectRatio: 1.9,
          ),
        );
      },
    );
  }

 
  SliverToBoxAdapter getYearFilterField(BuildContext context) {
    return SliverToBoxAdapter(
      child: YearFilterField(
        label: AppLocalizations.of(context).translate("Filter by year"),
        initialYear: 2005,
        onYearSelected: (year) {
          print(year);
        },
      ),
    );
  }

  SliverToBoxAdapter getMyDashboardCardInfo(
    BuildContext context,
  ) {
    return SliverToBoxAdapter(
      child: InfoCard(
        title: AppLocalizations.of(context).translate("My Dashboard "),
        icon: getIconFromCss("fa-regular fa-chart-pie-simple"),
      ),
    );
  }

  PreferredSize getAppbarWidget() {
    return PreferredSize(
        preferredSize: Size.fromHeight(56.h),
        child: PreferredSize(
          preferredSize: Size.fromHeight(56.h),
          child: FavoriteComponent(
            id: "30",
            title: AppLocalizations.of(context).translate("My Learning"),
            hasSearchIcon: true,
          ),
        ));
  }
}
