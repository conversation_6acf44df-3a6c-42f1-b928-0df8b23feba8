class TenderSubmitBody {
  final String? sadadNumber;
  final String? ibanNumber;
  final String? tenderNumber;
  final String? paymentTypeId;
  final String? budgetTypeEn;
  final String? bankNameEn;
  final String? comments;
  final String? customerId;
  final String? bankNameId;
  final String? projectNameId;
  final String? beneficiaryName;
  final String? tenderName;
  final String? tenderValue;
  final String? costCenterId;

  TenderSubmitBody({
    this.sadadNumber,
    this.ibanNumber,
    this.tenderNumber,
    this.paymentTypeId,
    this.budgetTypeEn,
    this.bankNameEn,
    this.comments,
    this.customerId,
    this.bankNameId,
    this.projectNameId,
    this.beneficiaryName,
    this.tenderName,
    this.costCenterId,
    this.tenderValue,
  });

  factory TenderSubmitBody.fromJson(Map<String, dynamic> json) {
    return TenderSubmitBody(

      sadadNumber: json['sadadnumber'],
      ibanNumber: json['ibannumber'],
      tenderNumber: json['tendernumber'],
      paymentTypeId: json['paymenttype_id'],
      budgetTypeEn: json['budget_type_en'],
      bankNameEn: json['bank_name_en'],
      comments: json['comments'],
      customerId: json['customer_id'],
      bankNameId: json['bank_name_id'],
      projectNameId: json['project_name_id'],
      beneficiaryName: json['beneficiaryname'],
      tenderName: json['tendername'],
      costCenterId: json['cost_center_id'],
      tenderValue: json['tendervalue'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'tendername': tenderName,
      'tendernumber': tenderNumber,
      'tendervalue': tenderValue,
      'customer_id': customerId,
      'sadadnumber': sadadNumber,
      'comments': comments,
      'beneficiaryname': beneficiaryName,
      'bankname': bankNameEn,
      'ibannumber': ibanNumber,
      'paymenttype_id': paymentTypeId,
      'budget_type_en': budgetTypeEn,
      'bank_name_id':bankNameId,
      'cost_center_id': costCenterId,
      'project_name_id': projectNameId
    };
  }
}
