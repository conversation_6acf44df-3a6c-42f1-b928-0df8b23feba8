class CostCenterResponse {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  List<CostCenter>? costCenter;
  String? employeeName;
  String? authCode;
  String? top;
  String? skip;
  String? employeeId;

  CostCenterResponse(
      {this.createdBy,
      this.createdById,
      this.createdDate,
      this.recId,
      this.costCenter,
      this.employeeName,
      this.authCode,
      this.top,
      this.skip,
      this.employeeId});

  CostCenterResponse.fromJson(Map<String, dynamic> json) {
    createdBy = json['createdBy'];
    createdById = json['createdById'];
    createdDate = json['createdDate'];
    recId = json['recId'];
    if (json['cost_center'] != null) {
      costCenter = <CostCenter>[];
      json['cost_center'].forEach((v) {
        costCenter!.add(CostCenter.fromJson(v));
      });
    }
    employeeName = json['employee_name'];
    authCode = json['auth_code'];
    top = json['top'];
    skip = json['skip'];
    employeeId = json['employee_id'];
  }
}

class CostCenter {
  String? costcentercode;
  String? shorttext;
  String? accountingobjectdescription;
  String? responsiblepersonid;
  String? companydetail;
  String? companycode;
  String? costcentercat;
  String? controllingarea;
  String? validfrom;
  String? isdefault;
  int? recid;
  String? validto;
  String? responsiblepersonnamear;
  String? responsiblepersonnameen;

  CostCenter(
      {this.costcentercode,
      this.shorttext,
      this.accountingobjectdescription,
      this.responsiblepersonid,
      this.companydetail,
      this.companycode,
      this.costcentercat,
      this.controllingarea,
      this.validfrom,
      this.isdefault,
      this.recid,
      this.validto,
      this.responsiblepersonnamear,
      this.responsiblepersonnameen});

  CostCenter.fromJson(Map<String, dynamic> json) {
    costcentercode = json['costcentercode'];
    shorttext = json['shorttext'];
    accountingobjectdescription = json['accountingobjectdescription'];
    responsiblepersonid = json['responsiblepersonid'];
    companydetail = json['companydetail'];
    companycode = json['companycode'];
    costcentercat = json['costcentercat'];
    controllingarea = json['controllingarea'];
    validfrom = json['validfrom'];
    isdefault = json['isdefault'];
    recid = json['recid'];
    validto = json['validto'];
    responsiblepersonnamear = json['responsiblepersonnamear'];
    responsiblepersonnameen = json['responsiblepersonnameen'];
  }
}