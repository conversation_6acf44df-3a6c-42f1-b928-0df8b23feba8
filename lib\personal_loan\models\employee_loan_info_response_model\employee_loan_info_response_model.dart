import 'loan.dart';
import 'meeting_parameters.dart';

class EmployeeLoanInfoResponseModel {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  // MeetingParameters? meetingParameters;
  String? employeeid;
  String? elmibannumber;
  String? maxloanvalue;
  String? activeloan;
  String? maximumLoanLimit;
  String? decKey;
  List<Loan>? loans;

  EmployeeLoanInfoResponseModel({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    // this.meetingParameters,
    this.employeeid,
    this.elmibannumber,
    this.maxloanvalue,
    this.activeloan,
    this.maximumLoanLimit,
    this.decKey,
    this.loans,
  });

  factory EmployeeLoanInfoResponseModel.fromJson(Map<String, dynamic> json) {
    return EmployeeLoanInfoResponseModel(
      createdBy: json['createdBy'] as String?,
      createdById: json['createdById'] as String?,
      createdDate: json['createdDate'] as String?,
      recId: json['recId'] as int?,
      // meetingParameters: json['meetingParameters'] == null
      //     ? null
      //     : MeetingParameters.fromJson(
      //         json['meetingParameters'] as Map<String, dynamic>),
      employeeid: json['employeeid'] as String?,
      elmibannumber: json['elmibannumber'] as String?,
      maxloanvalue: json['maxloanvalue'] as String?,
      activeloan: json['activeloan'] as String?,
      maximumLoanLimit: json['maximumloanlimi'] as String?,
      decKey: json['dec_key'] as String?,
      loans: (json['loans'] as List<dynamic>?)
          ?.map((e) => Loan.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'createdBy': createdBy,
        'createdById': createdById,
        'createdDate': createdDate,
        'recId': recId,
        // 'meetingParameters': meetingParameters?.toJson(),
        'employeeid': employeeid,
        'elmibannumber': elmibannumber,
        'maxloanvalue': maxloanvalue,
        'activeloan': activeloan,
        'maximumloanlimi': maximumLoanLimit,
        'dec_key': decKey,
        'loans': loans?.map((e) => e.toJson()).toList(),
      };
}
