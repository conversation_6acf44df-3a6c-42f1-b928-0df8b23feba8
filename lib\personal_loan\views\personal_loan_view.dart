import 'package:eeh/DiscussionBoard/Modules/CreatePost/widgets/submit_cancel_buttons_comp.dart';
import 'package:eeh/home/<USER>';
import 'package:eeh/passport/presentation/widgets/submit_button.dart';
import 'package:eeh/personal_loan/bloc/personal_loan_bloc.dart';
import 'package:eeh/personal_loan/repo/personal_loan_repo.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/cancel_button_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../../l10n/app_localizations.dart';
import '../../loan_preClosure/views/loan_preClosure_screen.dart';
import '../../shared/favorite_component/bloc/favorite_bloc.dart';
import '../../shared/favorite_component/bloc/favorite_event.dart';
import '../../shared/favorite_component/view/favorite_component.dart';
import '../../shared/styles/colors.dart';
import '../../shared/utility/navigation_utility.dart';
import '../../shared/widgets/app_bar.dart';
import '../../shared/widgets/currency_field_component.dart';
import '../../shared/widgets/general_bottom_sheet.dart';
import '../../shared/widgets/header_notes.dart';
import '../../shared/widgets/important_note.dart';
import '../../shared/widgets/sar_component.dart';
import '../../shared/widgets/toast.dart';
import '../../shared/widgets/validator_component.dart';
import '../../shared/widgets/new_textformfield_component.dart';
import '../../shared/widgets/submit_button_component.dart';
import '../../success_view/success_screen_view.dart';
import '../../travel_requset/presentation/widgets/custom_switcher.dart';
import 'widgets/success_screen_content_widget.dart';

class PersonalLoanScreen extends StatefulWidget {
  const PersonalLoanScreen({super.key});

  @override
  State<PersonalLoanScreen> createState() => _PersonalLoanScreenState();
}

class _PersonalLoanScreenState extends State<PersonalLoanScreen> {
  num maxAmount=0;
  num loanAmount = 0;
  bool hasActiveLoan = false;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) =>
                PersonalLoanBloc(personalRepo: PersonalLoanRepo.instance)
                  ..add(const PersonalLoanInitialEvent())),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
      ],
      child: Scaffold(
        appBar: getAppbarWidget(),
        body: BlocConsumer<PersonalLoanBloc, PersonalLoanState>(
          listenWhen: (previous, current) =>
              current is PersonalLoanInitialFinishState ||
              current is GetPersonalLoanInitialDataSuccessState ||
              current is GetPersonalLoanInitialDataLoadingState ||
              current is GetPersonalLoanInitialDataErrorState ||
              current is SubmitPersonalLoanLoadingState ||
              current is SubmitPersonalLoanSuccessState ||
              current is SubmitPersonalLoanErrorState,
          listener: (context, state) {
            if (state is PersonalLoanInitialFinishState) {
              context.read<PersonalLoanBloc>().add(GetEmployeeLoanInfoEvent());
            }
            if (state is GetPersonalLoanInitialDataSuccessState) {
              hasActiveLoan = context.read<PersonalLoanBloc>().hasActiveLoan;
              if (hasActiveLoan) {
                getActiveLoanBottomSheet(context, state);
              }
            }
          },
          buildWhen: (previous, current) =>
              current is PersonalLoanInitialFinishState ||
              current is GetPersonalLoanInitialDataSuccessState ||
              current is GetPersonalLoanInitialDataLoadingState ||
              current is GetPersonalLoanInitialDataErrorState ||
              current is SubmitPersonalLoanLoadingState ||
              current is SubmitPersonalLoanSuccessState ||
              current is SubmitPersonalLoanErrorState,
          builder: (context, state) {
            if (state is GetPersonalLoanInitialDataLoadingState ||
                state is PersonalLoanInitialFinishState) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return Center(
                child: Form(
                  key: context.read<PersonalLoanBloc>().formKey,
                  child: Flex(
                    direction: Axis.vertical,
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0).w,
                            child: Column(
                              children: [
                                getHeader(),
                                SizedBox(height: 12.h),
                                getLoanAmount(context),
                                getMonthlyInstallmentsAmount(context),
                                SizedBox(height: 32.h),
                                getValidator(context),
                                SizedBox(height: 32.h),
                                getImportantNotes(context),
                              ],
                            ),
                          ),
                        ),
                      ),
                      getButtons(context),
                    ],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Future<dynamic> getActiveLoanBottomSheet(
      BuildContext context, PersonalLoanState state) {
    return showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => PopScope(
        canPop: false,
        child: DraggableScrollableSheet(
            initialChildSize: 0.76,
            maxChildSize: 1,
            minChildSize: 0.4,
            expand: false,
            builder: (context, scrollController) => SizedBox(
                  width: double.infinity,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0).w,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(height: 8.h),
                            Container(
                              width: 45.w,
                              decoration: ShapeDecoration(
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                    width: 3.w,
                                    strokeAlign: BorderSide.strokeAlignCenter,
                                    color: secondTextColor,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 16.h),
                            Icon(
                              getIconFromCss("fa-regular fa-circle-info"),
                              size: 36.sp,
                              color: rejectColor,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              AppLocalizations.of(context)
                                  .translate('You have an active loan'),
                              textAlign: TextAlign.center,
                              style: FontUtilities.getTextStyle(TextType.medium,
                                  size: 16.sp,
                                  fontWeight: FontWeight.w500,
                                  textColor: textMain),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              AppLocalizations.of(context).translate(
                                  'You cannot apply for a new loan until your current loan is fully paid.'),
                              textAlign: TextAlign.center,
                              style: FontUtilities.getTextStyle(
                                TextType.medium,
                                size: 14.sp,
                                fontWeight: FontWeight.w400,
                                textColor: secondTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      CancelButtonComponent(
                        title: AppLocalizations.of(context).translate("Exit"),
                        onPressed: () {
                          Navigation.popScreen(
                            context,
                          );
                          Navigation.popScreen(
                            context,
                          );
                        },
                      ),
                      SubmitButtonComponent(
                        backGroung: primaryColor,
                        text:
                            AppLocalizations.of(context).translate("view loan"),
                        isLoading: state is SubmitPersonalLoanLoadingState,
                        dividerColor: grey,
                        onPressed: () {
                          Navigation.navigateToScreen(
                              context,
                              LoanPreClosureScreen(
                                isActiveLoan: hasActiveLoan,
                              ));
                        },
                      ),
                    ],
                  ),
                )),
      ),
    );
  }

  Widget getImportantNotes(BuildContext context) {
    return ImportantNote(
      title: AppLocalizations.of(context).translate("Important Notes:"),
      notes: [
        AppLocalizations.of(context)
            .translate("Your maximum loan limit is : 4 * Basic Salary"),
        AppLocalizations.of(context)
            .translate("Number of Installments is : 12 Month"),
        AppLocalizations.of(context)
            .translate("If you settle the loan this month, you'll need to wait until next month to request a new one"),
      ],
    );
  }

  PreferredSize getAppbarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: FavoriteComponent(
        id: "32",
        title: AppLocalizations.of(context).translate("Personal Loan"),
      ),
    );
  }

  Widget getButtons(BuildContext context) {
    return Column(
      children: [
        CancelButtonComponent(),
        BlocConsumer<PersonalLoanBloc, PersonalLoanState>(
          listenWhen: (previous, current) =>
              current is SubmitPersonalLoanLoadingState ||
              current is SubmitPersonalLoanSuccessState ||
              current is SubmitPersonalLoanErrorState,
          buildWhen: (previous, current) =>
              current is SubmitPersonalLoanLoadingState ||
              current is SubmitPersonalLoanSuccessState ||
              current is SubmitPersonalLoanErrorState,
          listener: (context, state) {
            if (state is SubmitPersonalLoanSuccessState) {
              Navigation.navigateToScreen(
                  context,
                  SuccessScreen(
                    contentWidget: SuccessScreenContentWidget(
                      submitResponse: state.submitPersonalLoanResponseModel,
                      monthlyAmount: context
                          .read<PersonalLoanBloc>()
                          .monthlyInstallmentsAmountController
                          .text,
                    ),
                    title: AppLocalizations.of(context)
                        .translate("Request Submitted Successfully"),
                  ));
            }
          },
          builder: (context, state) {
            return SubmitButtonComponent(
              backGroung: context
                          .read<PersonalLoanBloc>()
                          .loanAmountController
                          .text
                          .isNotEmpty &&
                      context
                          .read<PersonalLoanBloc>()
                          .isAcceptTermsAndConditions && (loanAmount <= maxAmount)
                  ? primaryColor
                  : grey,
              text: AppLocalizations.of(context).translate("Submit"),
              isLoading: state is SubmitPersonalLoanLoadingState,
              dividerColor: grey,
              onPressed: () {
                if (context
                    .read<PersonalLoanBloc>()
                    .formKey
                    .currentState!
                    .validate()) {
                  if (loanAmount > maxAmount) {
                    showMessage(
                        AppLocalizations.of(context).translate(
                            "Loan Amount should be less than maximum limit"),
                        MessageType.error);
                    return;
                  }
                  context
                      .read<PersonalLoanBloc>()
                      .add(SubmitPersonalLoanEvent());
                }
              },
            );
          },
        ),
      ],
    );
  }

  Widget getValidator(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: context.read<PersonalLoanBloc>().loanAmountController,
      builder: (context, value, child) {
        final loanAmountIsEmpty = value.text.isEmpty;

        return ValidatorComponent(
          isMandatory: true,
          onChange: (p0) {
            setState(() {
              context.read<PersonalLoanBloc>().isAcceptTermsAndConditions = p0;
              debugPrint(
                  "isAcceptTermsAndConditions: ${context.read<PersonalLoanBloc>().isAcceptTermsAndConditions}");
            });
          },
          value: context.read<PersonalLoanBloc>().isAcceptTermsAndConditions,
          preText: AppLocalizations.of(context).translate("I read and accept"),
          postText:
              AppLocalizations.of(context).translate("terms and conditions."),
          postTextCallback: /*loanAmountIsEmpty
              ? null
              : */() {
                  getTermsAndConditionsBottomSheet(context);
                },
        );
      },
    );
  }

  getTermsAndConditionsBottomSheet(BuildContext context) {
    final personalLoanBloc = context.read<PersonalLoanBloc>();
    final userData = personalLoanBloc.userData;
    final loanAmount = personalLoanBloc.loanAmountController.text;
    final appLang = AppLocalizations.appLang;
    return showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
      builder: (bottomSheetContext) => DraggableScrollableSheet(
          initialChildSize: 0.85,
          maxChildSize: 1,
          minChildSize: 0.5,
          expand: false,
          builder: (context, scrollController) => SizedBox(
                width: double.infinity,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0).w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        // mainAxisSize: MainAxisSize.min,
                        children: [
                          // SizedBox(height: 8.h),
                          Container(
                            width: 45.w,
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 3,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: const Color(0xFF919191),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 16.h),
                          Icon(
                            getIconFromCss("fa-solid fa-shield-halved"),
                            color: primaryColor,
                            size: 36.sp,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            AppLocalizations.of(bottomSheetContext)
                                .translate('terms_and_conditions'),
                            textAlign: TextAlign.center,
                            style: FontUtilities.getTextStyle(TextType.medium,
                                size: 16.sp,
                                fontWeight: FontWeight.w500,
                                textColor: textMain),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            "${AppLocalizations.of(bottomSheetContext).translate('acknowledgement', args: {
                                  "name": appLang == "en"
                                      ? userData?.nameen ?? ""
                                      : userData?.namear ?? ""
                                })} ${AppLocalizations.of(bottomSheetContext).translate(
                              'employment_status',
                              args: {
                                'amount': loanAmount.isEmpty
                                    ? ""
                                    : loanAmount.toString()
                              },
                            )}",
                            textAlign: TextAlign.start,
                            style: FontUtilities.getTextStyle(
                              TextType.medium,
                              size: 14.sp,
                              fontWeight: FontWeight.w400,
                              textColor: secondTextColor,
                            ),
                          ),
                          Text(
                            AppLocalizations.of(bottomSheetContext).translate(
                              'monthly_deduction_agreement',
                            ),
                            textAlign: TextAlign.start,
                            style: FontUtilities.getTextStyle(
                              TextType.medium,
                              size: 14.sp,
                              fontWeight: FontWeight.w400,
                              textColor: secondTextColor,
                            ),
                          ),
                          Text(
                            AppLocalizations.of(bottomSheetContext).translate(
                              'end_of_service_clause',
                            ),
                            textAlign: TextAlign.start,
                            style: FontUtilities.getTextStyle(
                              TextType.medium,
                              size: 14.sp,
                              fontWeight: FontWeight.w400,
                              textColor: secondTextColor,
                            ),
                          ),
                          Text(
                            AppLocalizations.of(bottomSheetContext).translate(
                              'installment_note_1',
                            ),
                            textAlign: TextAlign.start,
                            style: FontUtilities.getTextStyle(
                              TextType.medium,
                              size: 14.sp,
                              fontWeight: FontWeight.w400,
                              textColor: secondTextColor,
                            ),
                          ),
                          Text(
                            AppLocalizations.of(bottomSheetContext).translate(
                              'requester_signature',
                            ),
                            textAlign: TextAlign.start,
                            style: FontUtilities.getTextStyle(
                              TextType.medium,
                              size: 14.sp,
                              fontWeight: FontWeight.w400,
                              textColor: secondTextColor,
                            ),
                          ),
                          Text(
                            appLang == "en"
                                ? userData?.nameen ?? ""
                                : userData?.namear ?? "",
                            textAlign: TextAlign.start,
                            style: FontUtilities.getTextStyle(
                              TextType.medium,
                              size: 14.sp,
                              fontWeight: FontWeight.w400,
                              textColor: secondTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    SubmitButtonComponent(
                      backGroung: primaryColor,
                      text: AppLocalizations.of(bottomSheetContext)
                          .translate("Continue"),
                      isLoading: false,
                      dividerColor: grey,
                      onPressed: () {
                        Navigation.popScreen(
                          context,
                        );
                      },
                    ),
                  ],
                ),
              )),
    );
  }

  Widget getMonthlyInstallmentsAmount(BuildContext context) {
    return CurrencyTextFieldComponent(
      labelText:
          AppLocalizations.of(context).translate("monthly_installments_amount"),
      controller:
          context.read<PersonalLoanBloc>().monthlyInstallmentsAmountController,
      isSARComponentHasBrackets: true,
      isDemmed: true,
      isReadOnly: true,
    );
  }

  Widget getLoanAmount(BuildContext context) {
    return CurrencyTextFieldComponent(
      controller: context.read<PersonalLoanBloc>().loanAmountController,
      labelText: AppLocalizations.of(context).translate("Loan Amount"),
      isSARComponentHasBrackets: true,
      onChange: (value) {
        context.read<PersonalLoanBloc>().loanAmountController.text = value;

        if (value.isEmpty) {
          context
              .read<PersonalLoanBloc>()
              .monthlyInstallmentsAmountController
              .text = "0";
          loanAmount = 0;
        } else {
          try {
            String numericValue = value.replaceAll(RegExp(r'[^0-9.]'), '');
            loanAmount = double.parse(numericValue);
            double monthlyAmount = loanAmount / 12;

            context
                .read<PersonalLoanBloc>()
                .monthlyInstallmentsAmountController
                .text = monthlyAmount.toStringAsFixed(2);
          } catch (e) {
            print("Error parsing loan amount: $e");
            context
                .read<PersonalLoanBloc>()
                .monthlyInstallmentsAmountController
                .text = "0";
            loanAmount = 0;
          }
        }
        setState(() {});
      },
      isMandatory: true,
      validationText:
          AppLocalizations.of(context).translate("Loan Amount is required"),
    );
  }

  Widget getHeader() {
    return BlocConsumer<PersonalLoanBloc, PersonalLoanState>(
      listener: (context, state) {},
      buildWhen: (previous, current) =>
          current is GetPersonalLoanInitialDataSuccessState ||
          current is GetPersonalLoanInitialDataLoadingState ||
          current is GetPersonalLoanInitialDataErrorState,
      builder: (context, state) {
         if (state is GetPersonalLoanInitialDataSuccessState) {
          String maxValueStr =
              state.employeeLoanInfoResponseModel.maxloanvalue ?? '0';
          try {
            maxAmount = double.parse(maxValueStr).toInt();
          } catch (e) {
            maxAmount = 0;
          }
     }
        return (state is GetPersonalLoanInitialDataLoadingState)
            ? Center(
                child: CircularProgressIndicator(),
              )
            : HeaderNotes(
                content: Row(
                  children: [
                    Text(
                        AppLocalizations.of(context)
                                .translate("Your maximum loan limit is :") +
                            maxAmount.toString(),
                        style: FontUtilities.getTextStyle(
                          TextType.medium,
                          size: 14.sp,
                          textColor: textMain,
                        )),
                    SARComponent(
                  opacity: 1,
                  hasBrackets: true,
                )
              ],
            ),
          );
        }
      ,
    );
  }
}
