import '../../shared/widgets/general_text_form_field.dart';

const String department="Department";
const String project="Project";
final List<BudgetTypeModel> departmentList = [
  BudgetTypeModel(nameEn: department, nameAr: 'القسم',id: department),
  BudgetTypeModel(nameEn: "Project WBSًً", nameAr: "مشروع WBS",id: project),
];
//
// final List<GuaranteePercentageModel> guaranteePercentageList1 = [
//   GuaranteePercentageModel(nameEn: '1%', nameAr: '1%'),
//   GuaranteePercentageModel(nameEn: '2%', nameAr: '2%'),
// ];
//
// final List<GuaranteePercentageModel> guaranteePercentageList = [
//   GuaranteePercentageModel(nameEn: '5%', nameAr: '5%'),
//   GuaranteePercentageModel(nameEn: '10%', nameAr: '10%'),
//   GuaranteePercentageModel(nameEn: '20%', nameAr: '20%'),
//   GuaranteePercentageModel(nameEn: '25%', nameAr: '25%'),
// ];
//
// final List<GuaranteeTypeModel> guaranteeTypeList = [
//   GuaranteeTypeModel(nameEn: 'Bid Bond', nameAr: 'سندات المناقصة'),
//   GuaranteeTypeModel(nameEn: 'Performance Bond', nameAr: 'سندات الأداء'),
//   GuaranteeTypeModel(nameEn: 'Advance Payment', nameAr: 'دفع مسبق'),
//   GuaranteeTypeModel(nameEn: 'Final', nameAr: 'نهائي'),
// ];
//
// final List<PeriodModel> periods = [
//   PeriodModel(nameEn: '60 days', nameAr: '60 يوم'),
//   PeriodModel(nameEn: '90 days', nameAr: '90 يوم'),
//   PeriodModel(nameEn: '120 days', nameAr: '120 يوم'),
//   PeriodModel(nameEn: '150 days', nameAr: '150 يوم'),
//   PeriodModel(nameEn: '180 days', nameAr: '180 يوم'),
// ];


class BudgetTypeModel extends GeneralSheetContent {
  String? nameAr;
  String? nameEn;
  String? id;
  List<String> requiredFields;

  BudgetTypeModel({
    this.nameAr,
    this.id,
    this.nameEn,
    this.requiredFields = const [],
  }) {
    nameen = nameEn;
    namear = nameAr;
  }

  factory BudgetTypeModel.fromJson(Map<String, dynamic> json) {
    return BudgetTypeModel(
      nameAr: json['name_ar'],
      nameEn: json['name_en'],
      requiredFields: List<String>.from(json['required_fields'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name_ar': nameAr,
      'name_en': nameEn,
      'required_fields': requiredFields,
    };
  }
}
