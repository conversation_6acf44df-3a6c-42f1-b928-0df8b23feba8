import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/calendar.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../../l10n/app_localizations.dart';

class DateBottomSheet extends StatelessWidget {
  final DateRangePickerController datePickerController;
  final DateTime? maxDate;
  final DateTime? minDate;
  final bool? isWeekendEnable;
  const DateBottomSheet({
    required this.datePickerController,
    this.maxDate,
    this.minDate,
    this.isWeekendEnable,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      expand: false,
      initialChildSize: 0.7,
      maxChildSize: 1,
      minChildSize: 0.3,
      builder: (context, scrollController) {
        return Column(
          children: [
            Expanded(
              flex: 1,
              child: Text(
                AppLocalizations.of(context).translate("Select date"),
                style: FontUtility.getTextStyleForText(TextType.medium,
                    textColor: textleaves, isBold: true, size: 16),
              ),
            ),
            Expanded(
              flex: 14,
              child: Container(
                height: 751.h,
                width: 377.w,
                padding: const EdgeInsets.all(16.0),
                child: CustomCalendar(
                  dateRangePickerController: datePickerController,
                  maxDate: maxDate,
                  minDate: minDate,
                  isWeekendEnable: isWeekendEnable,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
