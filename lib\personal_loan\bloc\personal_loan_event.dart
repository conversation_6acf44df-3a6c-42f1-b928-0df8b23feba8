part of 'personal_loan_bloc.dart';

sealed class PersonalLoanEvent extends Equatable {
  const PersonalLoanEvent();

  @override
  List<Object> get props => [];
}

class PersonalLoanInitialEvent extends PersonalLoanEvent {
  const PersonalLoanInitialEvent();
}

class AcceptTermsAndConditionsEvent extends PersonalLoanEvent {
  const AcceptTermsAndConditionsEvent();
}

class GetEmployeeLoanInfoEvent extends PersonalLoanEvent {
  const GetEmployeeLoanInfoEvent();
}

class SubmitPersonalLoanEvent extends PersonalLoanEvent {
  // final SubmitPersonalLoanRequestBody submitPersonalLoanRequestBody;
  const SubmitPersonalLoanEvent(
      // {
      // required this.submitPersonalLoanRequestBody,
      // }
      );
}
