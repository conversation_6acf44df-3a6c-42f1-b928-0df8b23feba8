import 'package:eeh/allowance_services/model/allawance_list_response_model.dart';
import 'package:flutter/material.dart';

abstract class ExtendAllowanceEvent {}

class ExtendAllowanceInitialEvent extends ExtendAllowanceEvent {
  final bool isGas;
  ExtendAllowanceInitialEvent(this.isGas);
}

class NumberOfMonthsValueChangelEvent extends ExtendAllowanceEvent {
  final NumOfMonths value;
  NumberOfMonthsValueChangelEvent(this.value);
}

class SavedButtonActionEvent extends ExtendAllowanceEvent {
  final BuildContext context;
  final int index;
  SavedButtonActionEvent(this.context, this.index);
}

class EmployeeAllowanceEvent extends ExtendAllowanceEvent {}

class SubmitAllowanceEvent extends ExtendAllowanceEvent {}

class SearchEmployeeEvent extends ExtendAllowanceEvent {
  final String value;
  SearchEmployeeEvent(this.value);
}
