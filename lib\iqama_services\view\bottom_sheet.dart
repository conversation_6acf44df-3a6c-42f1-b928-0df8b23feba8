import 'package:flutter/material.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../shared/utility/font_utility.dart';
import '../bloc/iqama_bloc.dart';
import '../model/reasons_response.dart';

class IqamqPrintingBottomSheetWidget extends StatefulWidget {
  final IqamaBloc bloc;
  final Function(String) onTextFieldChange;
  final Function(Reason) onItemSelect;

  const IqamqPrintingBottomSheetWidget({
    super.key,
    required this.bloc,
    required this.onTextFieldChange,
    required this.onItemSelect,
  });

  @override
  State<IqamqPrintingBottomSheetWidget> createState() =>
      _IqamqPrintingBottomSheetWidgetState();
}

class _IqamqPrintingBottomSheetWidgetState
    extends State<IqamqPrintingBottomSheetWidget> {
  bool isSearchModel = false;

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      maxChildSize: 0.6,
      expand: false,
      builder: (context, scrollController) => Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          getBottomSheetTitle(),
          SizedBox(
            height: 20.h,
          ),
          getBottomSheetTextField(),
          SizedBox(
            height: 10.h,
          ),
          getListViewWidget(),
        ],
      ),
    );
  }

  Widget getBottomSheetTitle() {
    return Center(
      child: Text.rich(TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context).translate("Category"),
          style:FontUtilities.getTextStyle(TextType.regular,fontWeight: FontWeight.w700, size: 16.sp)
          //   style: const TextStyle(
          //       fontWeight: FontWeight.w700, fontSize: 16, color: textColor),
           ),
        ],
      )),
    );
  }

  Widget getBottomSheetTextField() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25.0.w),
      child: TextFormField(
        onChanged: (search) {
          isSearchModel = search.isNotEmpty;
          widget.onTextFieldChange(search);
        },
        decoration: InputDecoration(
            border: const OutlineInputBorder(),
            prefixIcon: Icon(
              Icons.search,
              color: hintSearchColor,
            ),
            hintText:
                AppLocalizations.of(context).translate("search by reason"),
            hintStyle:
            FontUtility.getTextStyleForText(TextType.medium,
            textColor: hintSearchColor, size: 16)),
      ),
    );
  }

  Widget getListViewWidget() {
    return Expanded(
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.0.w),
        itemBuilder: (context, index) {
          return ListTile(
              onTap: () => widget.onItemSelect(getListView()[index]),
              title: Text(AppLocalizations.appLang == 'en'
                  ? getListView()[index].reasonen ?? ''
                  : getListView()[index].reasonar ?? ''));
        },
        itemCount: getListView().length,
      ),
    );
  }

  List getListView() {
    return isSearchModel ? widget.bloc.filteredList : widget.bloc.reasonsList;
  }
}
