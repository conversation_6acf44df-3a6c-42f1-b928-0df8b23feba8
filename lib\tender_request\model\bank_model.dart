import '../../shared/widgets/general_text_form_field.dart';

class BankModel extends GeneralSheetContent{
  final String bankNameEn;
  final String bankKey;
  final int recId;
  final String bankNameAr;

  BankModel ({
    required this.bankNameEn,
    required this.bankKey,
    required this.recId,
    required this.bankNameAr,
  }){
    nameen = bankNameEn;
    namear = bankNameAr;
  }

  factory BankModel.fromJson(Map<String, dynamic> json) {
    return BankModel(
      bankNameEn: json['banknameen'] ?? '',
      bankKey: json['bankkey'] ?? '',
      recId: json['recid'] ?? 0,
      bankNameAr: json['banknamear'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'banknameen': bankNameEn,
      'bankkey': bankKey,
      'recid': recId,
      'banknamear': bankNameAr,
    };
  }
}

class BankListResponse {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final String employeeId;
  final List<BankModel> bankList;
  final String authKey;

  BankListResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeId,
    required this.bankList,
    required this.authKey,
  });

  factory BankListResponse.fromJson(Map<String, dynamic> json) {
    return BankListResponse(
      createdBy: json['createdBy'] ?? '',
      createdById: json['createdById'] ?? '',
      createdDate: json['createdDate'] ?? '',
      recId: json['recId'] ?? 0,
      employeeId: json['employeeid'] ?? '',
      bankList: (json['bank_name'] as List<dynamic>?)
          ?.map((e) => BankModel.fromJson(e))
          .toList() ??
          [],
      authKey: json['authkey'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'employeeid': employeeId,
      'bank_name': bankList.map((e) => e.toJson()).toList(),
      'authkey': authKey,
    };
  }
}
