import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/petty_cash/bloc/petty_cash_events.dart';
import 'package:eeh/petty_cash/bloc/petty_cash_states.dart';
import 'package:eeh/petty_cash/model/cost_center_model.dart';
import 'package:eeh/petty_cash/model/petty_cash_request_response.dart';
import 'package:eeh/petty_cash/repo/petty_cash_repo.dart';
import 'package:eeh/shared/app_constants.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_bloc.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:eeh/tender_request/model/projectWps_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class PettyCashRequestBloc
    extends Bloc<PettyCashRequestEvent, PettyCashRequestState> {
  late IPettyCashRepo _repo;
  TextEditingController cashUsageController = TextEditingController();
  TextEditingController budgetTypeController = TextEditingController(
      text: AppLocalizations.of(navigatorKey.currentContext!)
          .translate("department"));
  TextEditingController projectController = TextEditingController();
  TextEditingController cashAmountController = TextEditingController();
  TextEditingController closingDateController = TextEditingController();
  TextEditingController ibanNumberController = TextEditingController();
  TextEditingController bankNameController = TextEditingController();
  TextEditingController purposeController = TextEditingController();
  TextEditingController costCenterController = TextEditingController();
  DateRangePickerController dateRangePickerController =
      DateRangePickerController();
  bool isPolicyAccepted = false;
  List<PettyCashUsage> usageItems = [];
  late PettyCashUsage usageValue;
  ProjectWps? selectedProject;
  PettyCashRequest? pettyCashRequest;
  EmployeeBankDetails? employeeBankDetails;

  PettyCashRequestBloc({required PettyCashRequestRepo pettyCashRepo})
      : super(PettyCashInitialState()) {
    _repo = pettyCashRepo;

    on<PettyCashRequestEvent>(getCostCenterData);
    on<ChangeBudgetTypeEvent>(changeBudgetTypeValue);
    on<ProjectWbsListEvent>(fetchProjectWbsList);
    on<ChangeValidatorValueEvent>(changeValidatorValue);
    on<UpdatefieldValueEvent>(updatefieldValueEvent);
    on<UploadAttachmentFilesEvent>(uploadAttachmentFiles);
    on<SubmitRequestEvent>(submitRequest);
    on<FilesUpdatedEvent>(onFilesUpdate);
  }

  Future getCostCenterData(
      PettyCashRequestEvent event, Emitter<PettyCashRequestState> emit) async {
    await _repo.getCostCenterData({
      "lang": null,
    }).then((response) => onGetCostCenterResponse(response, emit));
  }

  void onGetCostCenterResponse(
      NetworkResponse response, Emitter<PettyCashRequestState> emitter) {
    response.maybeWhen(ok: (data) {
      onGetCostCenterSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(PettyCashInitialErrorState());
    });
  }

  void onGetCostCenterSuccess(data, Emitter emitter) {
    employeeBankDetails = EmployeeBankDetails.fromJson(data);
    ibanNumberController.text = employeeBankDetails?.iban ?? '';
    bankNameController.text = AppLocalizations.appLang == 'en'
        ? employeeBankDetails?.bankNameEn ?? ''
        : employeeBankDetails?.bankNameAr ?? '';
    costCenterController.text =
        employeeBankDetails?.costCenter?[0].costCenterCode ?? '';
    usageItems = employeeBankDetails?.pettyCashUsage ?? [];
    emitter(PettyCashInitialSuccessState(
        employeeBankDetails: employeeBankDetails ?? EmployeeBankDetails()));
  }

  Future fetchProjectWbsList(
      ProjectWbsListEvent event, Emitter<PettyCashRequestState> emit) async {
    emit(ProjectWbsListLoadingState());
    await _repo
        .getProjectWbsList()
        .then((response) => onfetchProjectWbsList(response, emit));
  }

  void onfetchProjectWbsList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onfetchProjectWbsListSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(ProjectWbsListErrorState());
    });
  }

  void onfetchProjectWbsListSuccess(data, emitter) {
    emitter(ProjectWbsListSuccessState(
        projectWpsResponse: ProjectWpsResponse.fromJson(data)));
  }

  Future uploadAttachmentFiles(UploadAttachmentFilesEvent event,
      Emitter<PettyCashRequestState> emit) async {
    emit(UploadAttachmentLoadingState());
    if (AttachBloc.instance(event.context).files.isNotEmpty &&
        AttachBloc.instance(event.context).isFilesEdited) {
      if (AttachBloc.instance(event.context)
          .files
          .where((element) => element.isNetworkFile == false)
          .toList()
          .isNotEmpty) {
        AttachBloc.instance(event.context).add(UploadFilesEvent(
            false,
            pettyCashRequest?.recId,
            pettyCashRequest?.recId,
            AttachBloc.instance(event.context)
                    .metaDataResponse
                    ?.attachmentId ??
                ''));
      }
    }
    emit(UploadAttachmentFilesSuccessState());
  }

  Future submitRequest(
      SubmitRequestEvent emitter, Emitter<PettyCashRequestState> emit) async {
    emit(SubmitRequestLoadingState());
    await _repo.submitRequest({
      "cashusegeid": usageValue.cashUsageId.toString(),
      "budget_type": budgetTypeController.text ==
              AppLocalizations.of(navigatorKey.currentContext!)
                  .translate("Project WBS")
          ? 'Project WBS'
          : 'Department',
      "projectid": selectedProject?.wbscode,
      "projectnameen": selectedProject?.projectNameEn,
      'projectnamear': selectedProject?.projectNameEn,
      "cash_amount": cashAmountController.text,
      "closed_date": closingDateController.text,
      "iban_number": ibanNumberController.text,
      "bank_name_en": employeeBankDetails?.bankNameEn,
      "bank_name_ar": employeeBankDetails?.bankNameAr,
      "policy_approve": "true",
      "purpose": purposeController.text,
      "cost_center": costCenterController.text
    }).then((response) => onsubmitRequestResponse(response, emit));
  }

  void onsubmitRequestResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onsubmitRequestSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitRequestErrorState());
    });
  }

  void onsubmitRequestSuccess(data, Emitter emitter) {
    pettyCashRequest = PettyCashRequest.fromJson(data);

    emitter(SubmitRequestSuccessState());
  }

  void updatefieldValueEvent(
      UpdatefieldValueEvent event, Emitter<PettyCashRequestState> emit) {
    emit(UpdatefieldValueState());
  }

  void changeBudgetTypeValue(
      ChangeBudgetTypeEvent event, Emitter<PettyCashRequestState> emit) {
    if (budgetTypeController.text ==
        AppLocalizations.of(navigatorKey.currentContext!)
            .translate("Project WBS")) {
      add(ProjectWbsListEvent());
    }
    emit(ChangeBudgetTypeState());
  }

  void changeValidatorValue(
      ChangeValidatorValueEvent event, Emitter<PettyCashRequestState> emit) {
    isPolicyAccepted = event.value;
    emit(ChangeValidatorValueState());
  }

  void onUsageValueSelected(int index) {
    add(UpdatefieldValueEvent());
    cashUsageController.text = AppLocalizations.appLang == 'en'
        ? usageItems[index].cashUsageNameEn ?? ''
        : usageItems[index].cashUsageNameAr ?? '';
    usageValue = usageItems[index];
  }

  void onFilesUpdate(
      FilesUpdatedEvent event, Emitter<PettyCashRequestState> emit) {
    emit(FilesUpdatedState());
  }

  bool isButtonEnable(BuildContext context) {
    return cashUsageController.text.isNotEmpty &&
        budgetTypeController.text.isNotEmpty &&
        ((budgetTypeController.text ==
                    AppLocalizations.of(context).translate('Project WBS') &&
                projectController.text.isNotEmpty) ||
            budgetTypeController.text ==
                AppLocalizations.of(context).translate('department')) &&
        cashAmountController.text.isNotEmpty &&
        closingDateController.text.isNotEmpty &&
        purposeController.text.isNotEmpty &&
        isPolicyAccepted ;
  }
}
