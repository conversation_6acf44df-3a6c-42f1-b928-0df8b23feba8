import 'package:flutter/services.dart';

class DecimalTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    final arabicRegex = RegExp(r'^[\u0660-\u0669]*\٫?[\u0660-\u0669]{0,2}$');
    final engRegex = RegExp(r'^\d*\.?\d{0,2}$');
    if (arabicRegex.hasMatch(newValue.text) || engRegex.hasMatch(newValue.text)) {
      return newValue;
    }
    return oldValue;
  }
}