import '../../shared/widgets/general_text_form_field.dart';

class CustomerName {
  String createdBy;
  String createdById;
  String createdDate;
  int recId;
  String employeeid;
  List<Customerinforma> customerinforma;
  dynamic top;
  dynamic searchTerm;
  dynamic skip;
  String authkey;

  CustomerName({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeid,
    required this.customerinforma,
    required this.top,
    required this.searchTerm,
    required this.skip,
    required this.authkey,
  });

  factory CustomerName.fromJson(Map<String, dynamic> json) => CustomerName(
    createdBy: json["createdBy"],
    createdById: json["createdById"],
    createdDate: json["createdDate"],
    recId: json["recId"],
    employeeid: json["employeeid"],
    customerinforma: List<Customerinforma>.from(json["customerinforma"].map((x) => Customerinforma.fromJson(x))),
    top: json["top"],
    searchTerm: json["search_term"],
    skip: json["skip"],
    authkey: json["authkey"],
  );

  Map<String, dynamic> toJson() => {
    "createdBy": createdBy,
    "createdById": createdById,
    "createdDate": createdDate,
    "recId": recId,
    "employeeid": employeeid,
    "customerinforma": List<dynamic>.from(customerinforma.map((x) => x.toJson())),
    "top": top,
    "search_term": searchTerm,
    "skip": skip,
    "authkey": authkey,
  };
}

class Customerinforma {
  String namear;
  String moiid;
  String id;
  String nameen;
  int recid;

  Customerinforma({
    required this.namear,
    required this.moiid,
    required this.id,
    required this.nameen,
    required this.recid,
  });

  factory Customerinforma.fromJson(Map<String, dynamic> json) => Customerinforma(
    namear: json["namear"],
    moiid: json["moiid"],
    id: json["id"],
    nameen: json["nameen"],
    recid: json["recid"],
  );

  Map<String, dynamic> toJson() => {
    "namear": namear,
    "moiid": moiid,
    "id": id,
    "nameen": nameen,
    "recid": recid,
  };
}

class CustomerInfo extends GeneralSheetContent {
  final String nameAr;
  final String moiId;
  final String id;
  final String nameEn;
  final int recId;

  CustomerInfo({
    required this.nameAr,
    required this.moiId,
    required this.id,
    required this.nameEn,
    required this.recId,
  }) {
    // Initialize GeneralSheetContent fields
    nameen = nameEn;
    extraContent = [];
    // extraContent = [
    extraContent?.add(SheetExtraContent(
        keyEn: 'Name ',
        keyAR: 'الاسم',
        valueEn: nameEn,
        valueAR: nameAr));
    extraContent?.add(SheetExtraContent(
        keyEn: 'EmployeeId',
        keyAR: 'EmployeeId',
        valueEn: id,
        valueAR: id));
  }

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      nameAr: json['namear'],
      moiId: json['moiid'],
      id: json['id'],
      nameEn: json['nameen'],
      recId: json['recid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'namear': nameAr,
      'moiid': moiId,
      'id': id,
      'nameen': nameEn,
      'recid': recId,
    };
  }
}
