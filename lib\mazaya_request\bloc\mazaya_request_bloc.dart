import 'package:eeh/login/models/get_emp_info_response.dart';
import 'package:eeh/mazaya_request/bloc/mazaya_request_events.dart';
import 'package:eeh/mazaya_request/bloc/mazaya_request_states.dart';
import 'package:eeh/mazaya_request/model/benefit_request_body.dart';
import 'package:eeh/mazaya_request/model/benefit_response.dart';
import 'package:eeh/mazaya_request/model/category_request_body.dart';
import 'package:eeh/mazaya_request/model/category_response.dart';
import 'package:eeh/mazaya_request/model/files_model.dart';
import 'package:eeh/mazaya_request/model/fitness_center_model.dart';
import 'package:eeh/mazaya_request/model/fitness_center_response.dart';
import 'package:eeh/mazaya_request/model/general_amounts_response.dart';
import 'package:eeh/mazaya_request/model/mazaya_record_response.dart';
import 'package:eeh/mazaya_request/model/submit_request_body.dart';
import 'package:eeh/mazaya_request/model/submit_response.dart';
import 'package:eeh/mazaya_request/repo/mazaya_request_repo.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_bloc.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:eeh/shared/attachment_uda/model/attachment_request_model.dart';
import 'package:eeh/shared/favorite_component/favorite_utils.dart';
import 'package:eeh/shared/favorite_component/models/add_model.dart';
import 'package:eeh/shared/favorite_component/models/all_favorite_response_model.dart';
import 'package:eeh/shared/favorite_component/models/delete_model.dart';
import 'package:eeh/shared/favorite_component/models/get_model.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/methods.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MazayaRequestBloc extends Bloc<MazayaRequestEvent, MazayaRequestState> {
  final TextEditingController benefitController = TextEditingController();
  final TextEditingController categoryController = TextEditingController();
  final TextEditingController fitnessCenterController = TextEditingController();
  final TextEditingController iqamaController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  final TextEditingController invoiceAmountController = TextEditingController();
  final MazayaRequestRepo _repo = MazayaRequestRepo();
  String? recId;
  late bool isEditMode;
  GetEmployeeInfoResponse? userData;
  BenefitResponse? benefitResponse;

  CategoryResponse? categoryResponse;
  Mazayacategorie? selectedBenefit;
  Mazayasubcatego? selectedCategory;
  FitnessCenterResponse? fitnessCenterResponse;
  Gymsubscription? selectedFitnessCenter;
  List<FilesModel> files = [];
  MazayaSubmitResponse? mazayaSubmitResponse;
  AttachmentRequestBody attachmentRequestBody =
      AttachmentRequestBody(attachmentUdaId: '', objectId: '');
  MazayaRecoredResponse? mazayaRecoredResponse;
  bool isFavoriteService = false;
  late BuildContext ctx;
  MazayaGeneralAmountsResponse? mazayaGeneralAmountsResponse;

  MazayaRequestBloc() : super(MazayaInitialState()) {
    on<MazayaInitialEvent>((event, emit) async {
      isEditMode = event.isEditMode;
      recId = event.recId;
      userData = await getEmpInfo();
      await getFavoriteService();
      await getMazayaGeneralAmountsValues();
      emit(MazayaInitialFinishState());
    });

    on<MazayaBenefitTypeLoadingEvent>((event, emit) async {
      emit(MazayaBenefitTypeLoadingState());
      await getBenefitTypeValues(emit);
    });

    on<MazayaCategoryLoadingEvent>((event, emit) async {
      if (!isEditMode) selectedCategory = null;
      emit(MazayaCategoryLoadingState());
      await getCategoryValues(emit);
    });

    on<MazayaUpdateBenefitValueEvent>((event, emit) async {
      selectedBenefit = event.mazayacategorie;
      clearAllControllers();
      selectedCategory = null;
      selectedFitnessCenter = null;

      emit(MazayaUpdateBenefitValueState());
    });
    on<MazayaUpdateCategoryValueEvent>((event, emit) async {
      selectedFitnessCenter = null;
      selectedCategory = event.mazayasubcatego;
      clearOnCategoryValueChangeControllers();

      emit(MazayaUpdateCategoryValueState());
    });

    on<MazayaFitnessCenterLoadingEvent>((event, emit) async {
      emit(MazayaFitnessCenterLoadingState());
      await getFitnessCenterValues(emit);
    });

    on<MazayaUpdateFitnessCenterValueEvent>((event, emit) async {
      // clearOnFitnessCenterValueChangeControllers();
      selectedFitnessCenter = event.gymsubscription;
      emit(MazayaUpdateFitnessCenterValueState());
    });

    on<MazayaUpdateTextFieldValueEvent>((event, emit) async {
      emit(MazayaUpdateTextFieldValueState());
    });

    on<MazayaSubmitRequestEvent>((event, emit) async {
      emit(MazayaSubmitRequestLoadingState());
      await submitRequest(emit);
    });

    on<UploadAttachmentFilesEvent>((event, emit) async {
      emit(UploadAttachmentFilesLoadingState());
      await uploadAttachmentFiles(emit);
    });

    on<GetMazayaSavedRecordEvent>((event, emit) async {
      emit(GetMazayaSavedRecordLoadingState());
      await getSavedRecord(emit);

      if (mazayaRecoredResponse != null) {
        await getBenefitTypeValues(emit);
        if (selectedCategory?.subcategoryid != null) {
          await getCategoryValues(emit);
        }
        if (selectedFitnessCenter?.fitnesscenterid != null) {
          await getFitnessCenterValues(emit);
        }

        emit(GetMazayaSavedRecordSuccessState());
      }
    });

    on<FavoriteServiceValueChangeEvent>((event, emit) async {
      await onFavoriteItemPressed(emit);
      emit(ChangeFavoriteStatusState());
    });

    on<MazayaFilesUpdatedEvent>((event, emit) {
      emit(MazayaFilesUpdatedState());
    });
  }

  Future getMazayaGeneralAmountsValues() async {
    NetworkResponse networkResponse =
        await _repo.getMazayaGeneralAmountsValues({
      'employeeid': int.parse(userData!.employeeid ?? '-1'),
      'requesttype': 'All_Mazaya'
    });
    networkResponse.maybeWhen(ok: (data) {
      mazayaGeneralAmountsResponse =
          MazayaGeneralAmountsResponse.fromJson(data);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
    });
  }

  Future getBenefitTypeValues(Emitter emitter) async {
    await _repo
        .getBenefitTypeValues(BenefitRequestBody(
            employeeid: userData!.employeeid ?? '', requesttype: 'Mazaya'))
        .then((response) => onGetBenefitTypeValuesResponse(response, emitter));
  }

  String getConsumedValue() {
    return (selectedBenefit?.categoryclaimedamount ?? 0).toString();
  }

  String getRemainigValue() {
    return (selectedCategory?.categoryid == 2 &&
            selectedCategory?.subcategoryid == 5)
        ? '0'
        : (selectedBenefit?.categoryremainingbalance ?? 0).toString();
  }

  String getElmCoveredAmount() {
    if (selectedCategory?.categoryid == 2 &&
        selectedCategory?.subcategoryid == 5) {
      return '0';
    } else {
      num totalAmount = selectedBenefit?.categoryremainingbalance ?? 0;
      if (invoiceAmountController.text.isEmpty &&
          selectedFitnessCenter?.fitnesscenteramount == null) {
        return '0';
      } else {
        num invoiceAmountValue = selectedFitnessCenter != null
            ? selectedFitnessCenter?.fitnesscenteramount ?? 0
            : num.tryParse(getNumberValue(invoiceAmountController.text)) ?? 0;
        if ((totalAmount - invoiceAmountValue) < 0) {
          return (totalAmount < 0 ? 0 : totalAmount).toString();
        } else {
          return invoiceAmountValue.toString();
        }
      }
    }
  }

  void onGetBenefitTypeValuesResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetBenefitTypeValuesSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(MazayaBenefitTypeErrorState());
    });
  }

  void onGetBenefitTypeValuesSuccess(data, Emitter emitter) {
    benefitResponse = BenefitResponse.fromJson(data);
    // mazayacategorieFilter = benefitResponse?.mazayacategorie ?? [];
    emitter(MazayaBenefitTypeSuccessState());
  }

  Future getCategoryValues(Emitter emitter) async {
    await _repo
        .getCategoryValues(CategoryRequestBody(
            employeeid: userData!.employeeid ?? '',
            requesttype: 'Mazaya_Sub_Category',
            categoryId: selectedBenefit?.categoryid))
        .then((response) => onGetCategoryValuesResponse(response, emitter));
  }

  void onGetCategoryValuesResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetCategoryValuesSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(MazayaCategoryErrorState());
    });
  }

  void onGetCategoryValuesSuccess(data, Emitter emitter) {
    categoryResponse = CategoryResponse.fromJson(data);

    emitter(MazayaCategorySuccessState());
  }

  Future getFitnessCenterValues(Emitter emitter) async {
    await _repo
        .getFitnessCenterValues(FitnessCenterRequestBody(
            employeeid: userData!.employeeid ?? '',
            requesttype: 'Mazaya_Fitness',
            categoryId: selectedBenefit?.categoryid,
            subCategoryId: selectedCategory?.subcategoryid))
        .then(
            (response) => onGetFitnessCenterValuesResponse(response, emitter));
  }

  void onGetFitnessCenterValuesResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetFitnessCenterValuesSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(MazayaFitnessCenterErrorState());
    });
  }

  void onGetFitnessCenterValuesSuccess(data, Emitter emitter) {
    fitnessCenterResponse = FitnessCenterResponse.fromJson(data);

    emitter(MazayaFitnessCenterSuccessState());
  }

  Future submitRequest(Emitter emitter) async {
    await _repo
        .submitRequest(MazayaSubmitRequestBody(
          recId: !isEditMode ? null : int.tryParse(recId ?? ''),
          employeeid: int.tryParse(userData?.employeeid ?? ''),
          benefitTypeId: selectedBenefit?.categoryid.toString(),
          benefittype: selectedBenefit?.categorynameen,
          categoryid: selectedCategory?.subcategoryid,
          categoryname: selectedCategory?.subcategorynameen,
          totallimit: selectedBenefit?.categorymaxlimit,
          totalclaimedamo: selectedBenefit?.categoryclaimedamount,
          totalremainingb: selectedBenefit?.categoryremainingbalance,
          categorymaxlimi: selectedCategory?.categorymaxlimit,
          coveredAmount: num.tryParse(getElmCoveredAmount()),
          categoryclaimed: selectedCategory?.categoryclaimedamount,
          categoryremaini: selectedCategory?.categoryremainingbalance,
          isfullamountcov: selectedCategory?.isfullamountcovered,
          subcateBenefit: selectedCategory?.subcategorybenefittype,
          invoiceamount: invoiceAmountController.text.isEmpty
              ? null
              : num.tryParse(getNumberValue(invoiceAmountController.text)),
          fitnesscenterid: selectedFitnessCenter?.fitnesscenterid,
          fitCenterName: selectedFitnessCenter?.fitnesscenternameen,
          fitnesscenteram: selectedFitnessCenter?.fitnesscenteramount,
          beneficiaryname: null,
          beneficiaryid: iqamaController.text.isEmpty
              ? null
              : int.tryParse(iqamaController.text),
          beneficiarymobi: mobileController.text.isEmpty
              ? null
              : int.tryParse(mobileController.text),
          notes: noteController.text.isEmpty ? null : noteController.text,
          mzReqType: selectedCategory?.subcategorybenefittype,
        ))
        .then((response) => onsubmitRequestResponse(response, emitter))
        .whenComplete(() async {
      if (AttachBloc.instance(ctx).deletedFiles.isNotEmpty && isEditMode) {
        await AttachBloc.instance(ctx).deleteFromServer(deleteMazayaAttachment);
      }
    });
    ;
  }

  Future uploadAttachmentFiles(emitter) async {
    if (AttachBloc.instance(ctx).files.isNotEmpty && !isEditMode ||
        AttachBloc.instance(ctx).files.isNotEmpty &&
            isEditMode &&
            AttachBloc.instance(ctx).isFilesEdited) {
      if (AttachBloc.instance(ctx)
          .files
          .where((element) => element.isNetworkFile == false)
          .toList()
          .isNotEmpty) {
        AttachBloc.instance(ctx).add(UploadFilesEvent(
            isEditMode,
            mazayaSubmitResponse?.recId,
            mazayaSubmitResponse!.recId,
            AttachBloc.instance(ctx).metaDataResponse?.attachmentId ?? ''));
      }
    }
    emitter(UploadAttachmentFilesSuccessState());
  }

  Future getSavedRecord(Emitter emitter) async {
    await _repo
        .getSavedRecord(recId.toString())
        .then((response) => onGetSavedRecordResponse(response, emitter))
        .whenComplete(() {
      mazayaRecoredResponse?.selfattachmen?.forEach((attach) async {
        AttachBloc.instance(ctx).add(DownloadFilEvent(attach));
      });
    });
  }

  void onGetSavedRecordResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetSavedRecordSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(GetMazayaSavedRecordErrorState());
    });
  }

  void onGetSavedRecordSuccess(data, Emitter emitter) {
    mazayaRecoredResponse = MazayaRecoredResponse.fromJson(data);
    selectedBenefit = Mazayacategorie(
      categoryclaimedamount: mazayaRecoredResponse?.totalclaimedamo == null
          ? null
          : int.parse(mazayaRecoredResponse?.totalclaimedamo ?? ''),
      categoryid: mazayaRecoredResponse?.benefitTypeId == null
          ? null
          : int.parse(mazayaRecoredResponse?.benefitTypeId ?? ''),
      categorymaxlimit: mazayaRecoredResponse?.totallimit == null
          ? null
          : int.parse(mazayaRecoredResponse?.totallimit ?? ''),
      categorynameen: mazayaRecoredResponse?.benefittype,
      categoryremainingbalance: mazayaRecoredResponse?.totalremainingb == null
          ? null
          : int.parse(mazayaRecoredResponse?.totalremainingb ?? ''),
      isfullamountcovered: mazayaRecoredResponse?.isfullamountcov == null
          ? null
          : mazayaRecoredResponse?.isfullamountcov == 'false'
              ? false
              : true,
    );
    selectedCategory = mazayaRecoredResponse?.categoryid == null
        ? null
        : Mazayasubcatego(
            categoryclaimedamount:
                mazayaRecoredResponse?.categoryclaimed == null
                    ? null
                    : int.parse(mazayaRecoredResponse?.categoryclaimed ?? '0'),
            categoryid: mazayaRecoredResponse?.benefitTypeId == null
                ? null
                : int.parse(mazayaRecoredResponse?.benefitTypeId ?? '0'),
            categorymaxlimit: mazayaRecoredResponse?.categorymaxlimi == null
                ? null
                : int.parse(mazayaRecoredResponse?.categorymaxlimi ?? '0'),
            categorynamear: mazayaRecoredResponse?.benefittype,
            categorynameen: mazayaRecoredResponse?.benefittype,
            categoryremainingbalance:
                mazayaRecoredResponse?.categoryremaini == null
                    ? null
                    : int.parse(mazayaRecoredResponse?.categoryremaini ?? '0'),
            isfullamountcovered: mazayaRecoredResponse?.isfullamountcov == null
                ? null
                : mazayaRecoredResponse?.isfullamountcov == 'false'
                    ? false
                    : true,
            subcategorybenefittype: mazayaRecoredResponse?.subcateBenefit,
            subcategoryid: int.parse(mazayaRecoredResponse?.categoryid ?? '0'),
            subcategorynamear: mazayaRecoredResponse?.categoryname,
            subcategorynameen: mazayaRecoredResponse?.categoryname,
          );
    selectedFitnessCenter = mazayaRecoredResponse?.fitnesscenterid == null
        ? null
        : Gymsubscription(
            categoryid: mazayaRecoredResponse?.benefitTypeId == null
                ? null
                : int.parse(mazayaRecoredResponse?.benefitTypeId ?? '0'),
            fitnesscenteramount: mazayaRecoredResponse?.fitnesscenteram == null
                ? null
                : int.parse(mazayaRecoredResponse?.fitnesscenteram ?? '0'),
            fitnesscenterid:
                int.parse(mazayaRecoredResponse?.fitnesscenterid ?? '0'),
            fitnesscenternamear: mazayaRecoredResponse?.fitCenterName,
            fitnesscenternameen: mazayaRecoredResponse?.fitCenterName,
            subcategoryid: mazayaRecoredResponse?.categoryid == null
                ? null
                : int.parse(mazayaRecoredResponse?.categoryid ?? '0'),
          );
    benefitController.text = selectedBenefit?.categorynameen ?? '';
    categoryController.text = selectedCategory?.subcategorynameen ?? '';
    fitnessCenterController.text =
        selectedFitnessCenter?.fitnesscenternameen ?? '';
    invoiceAmountController.text = mazayaRecoredResponse?.invoiceamount ?? '';

    iqamaController.text = mazayaRecoredResponse?.beneficiaryid ?? '';

    mobileController.text = mazayaRecoredResponse?.beneficiarymobi ?? '';

    noteController.text = mazayaRecoredResponse?.notes ?? '';
    // if (mazayaRecoredResponse?.selfattachmen != null) {
    //   mazayaRecoredResponse?.selfattachmen?.forEach((element) {
    //     files.add(FilesModel(
    //         isFromServer: true,
    //         fileName: element.attachmentName ?? '',
    //         fileSize: 0));
    //   });
    // }
  }

  void onUploadAttachmentFilesFinished(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      emitter(UploadAttachmentFilesSuccessState());
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(UploadAttachmentFilesErrorState());
    });
  }

  void onsubmitRequestResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onsubmitRequestSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(MazayaSubmitRequestErrorState());
    });
  }

  void onsubmitRequestSuccess(data, Emitter emitter) {
    mazayaSubmitResponse = MazayaSubmitResponse.fromJson(data);
    attachmentRequestBody.objectId = mazayaSubmitResponse!.recId.toString();

    emitter(MazayaSubmitRequestSuccessState());
  }

  bool isCategoryWidgetVisible() {
    return selectedBenefit?.categoryid != 4;
  }

  bool isFitnessCenterWidgetVisible() {
    return selectedFitnessCenter?.fitnesscenterid != null ||
        (selectedCategory?.categoryid == 1 &&
            selectedCategory?.subcategoryid == 1) ||
        (selectedCategory?.categoryid == 2 &&
            selectedCategory?.subcategoryid == 5);
  }

  bool isInvoiceAmountWidgetVisible() {
    return selectedFitnessCenter == null &&
        (selectedCategory?.categoryid == 3 ||
            selectedBenefit?.categoryid == 4 ||
            (selectedCategory?.categoryid == 1 &&
                selectedCategory?.subcategoryid != 1) ||
            (selectedCategory?.categoryid == 2 &&
                selectedCategory?.subcategoryid != 5));
  }

  bool isNoteAndAttachmentWidgetVisible() {
    return selectedCategory?.categoryid == 3 ||
        selectedBenefit?.categoryid == 4 ||
        selectedFitnessCenter?.fitnesscenterid != null ||
        (selectedCategory?.categoryid == 2 &&
            selectedCategory?.subcategoryid != 5) ||
        (selectedCategory?.categoryid == 1 &&
            selectedCategory?.subcategoryid != 1);
  }

  bool isAttachmentReceiptCommentVisible() {
    return selectedCategory?.categoryid == 2 &&
        selectedCategory?.subcategoryid == 5;
  }

  bool isIqamaAndMobileVisible() {
    return (selectedFitnessCenter?.fitnesscenterid != null
    //  &&
    //     selectedFitnessCenter?.categoryid != 1 &&
    //     selectedFitnessCenter?.subcategoryid != 1
        );
  }

  void clearAllControllers() {
    categoryController.clear();
    fitnessCenterController.clear();

    if (selectedBenefit?.categoryid != 3 && selectedBenefit?.categoryid != 4) {
      invoiceAmountController.clear();
      iqamaController.clear();
      mobileController.clear();
      noteController.clear();
    }
  }

  void clearOnCategoryValueChangeControllers() {
    fitnessCenterController.clear();
    if ((selectedCategory?.categoryid != 2)) {
      iqamaController.clear();
      mobileController.clear();
      noteController.clear();
      invoiceAmountController.clear();
    }
  }

  void clearOnFitnessCenterValueChangeControllers() {
    iqamaController.clear();
    mobileController.clear();
    noteController.clear();
    invoiceAmountController.clear();
  }

  bool isButtonEnable() {
    return (fitnessCenterController.text.isNotEmpty &&
            iqamaController.text.isNotEmpty) ||
        (AttachBloc.instance(ctx).files.isNotEmpty &&
            ((selectedBenefit?.categoryid == 2 &&
                        (selectedCategory?.subcategoryid != 5 &&
                            invoiceAmountController.text.isNotEmpty) ||
                    ((iqamaController.text.isNotEmpty &&
                        (fitnessCenterController.text.isNotEmpty ||
                            invoiceAmountController.text.isNotEmpty)))) ||
                ((selectedBenefit?.categoryid == 3 ||
                        selectedBenefit?.categoryid == 4 ||
                        (selectedBenefit?.categoryid == 1 &&
                            selectedCategory?.subcategoryid != 1)) &&
                    (fitnessCenterController.text.isNotEmpty ||
                        invoiceAmountController.text.isNotEmpty))));
  }

  bool checkMaximumAmountReached() {
    if (selectedCategory?.categoryid == 2) {
      return false;
    } else if (selectedCategory?.subcategoryid == 1 &&
        selectedCategory?.categoryid == 1) {
      if ((selectedBenefit?.categoryremainingbalance ?? 0) <
          (selectedFitnessCenter?.fitnesscenteramount ?? 0)) {
        return true;
      }
    }
    if (selectedBenefit!.categoryremainingbalance!.toInt() < 1) {
      return true;
    } else {
      return false;
    }
  }

  bool isRequired() {
    return !(selectedBenefit?.categoryid == 1 &&
        selectedCategory?.subcategoryid == 1);
  }

  getFavoriteService() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await FavoriteUtils.getAllFavorite(allFavoriteServiceRequest)
          .then((value) {
        if (value.data != null && value.data['servicesid1'] != null) {
          ResponseModel response = ResponseModel.fromJson(value.data);
          for (var element in response.servicesid1!) {
            if (element.serviceids == "3") {
              isFavoriteService = true;
              return;
            } else {
              isFavoriteService = false;
            }
          }
        } else {
          isFavoriteService = false;
        }
      });
    });
  }

  AllFavoriteServiceRequest allFavoriteServiceRequest =
      AllFavoriteServiceRequest(sessionid: null);
  DeleteFavoriteServiceRequest deleteFavoriteServiceRequest =
      DeleteFavoriteServiceRequest(
    serviceid: "3",
    sessionid: null,
  );

  AddFavoriteServiceRequest addFavoriteServiceRequest =
      AddFavoriteServiceRequest(
    serviceid: "3",
    sessionid: null,
  );

  onFavoriteItemPressed(emitter) async {
    if (isFavoriteService == true) {
      await FavoriteUtils.deleteFavoriteService(deleteFavoriteServiceRequest)
          .then((value) {
        isFavoriteService = false;
      });
      emitter(DeleteFavoriteServiceState());
    } else {
      await FavoriteUtils.addFavoriteService(addFavoriteServiceRequest)
          .then((value) {
        isFavoriteService = true;
      });
      emitter(AddFavoriteServiceState());
    }
  }
}
