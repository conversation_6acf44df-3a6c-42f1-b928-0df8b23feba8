import 'package:eeh/notification/models/all_notification_model.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/secure_storage.dart';

abstract class INotficationRequestRepo {
  Future<NetworkResponse> getAllNotfication(
      {required AllNotificationRequestModel requestBody});
  Future<NetworkResponse> onNotificationItemTapped(
      {required Map<String, dynamic> requestBody});
}

class NotificationRepo implements INotficationRequestRepo {
  @override
  Future<NetworkResponse> getAllNotfication(
      {required AllNotificationRequestModel requestBody}) async {
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: requestBody.toJson(),
        requestType: RequestType.post,
        sessionToken:
            await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Notification List Form",
          "moduleName": "Notification List",
          "appKey": "ACM",
          "Content-Type": "application/json",
        });
    return response;
  }

  @override
  Future<NetworkResponse> onNotificationItemTapped(
      {required Map<String, dynamic> requestBody}) async {
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: requestBody,
        requestType: RequestType.post,
        sessionToken:
            await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Update notification Status Form",
          "moduleName": "Update notification Status",
          "appKey": "ACM",
          "Content-Type": "application/json",
        });
    return response;
  }
}
