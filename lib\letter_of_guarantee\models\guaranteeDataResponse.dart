import 'package:eeh/shared/widgets/general_text_form_field.dart';

class GuaranteeDataResponse {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  String? top;
  String? authkey;
  List<Guaranteetype>? guaranteetype;
  List<Perioddays>? perioddays;
  List<CostCenter>? costCenter;
  List<GuarantPercent>? guarantPercent;
  String? skip;
  String? searchterm;
  String? employeeid;

  GuaranteeDataResponse(
      {this.createdBy,
        this.createdById,
        this.createdDate,
        this.recId,
        this.top,
        this.authkey,
        this.guaranteetype,
        this.perioddays,
        this.costCenter,
        this.guarantPercent,
        this.skip,
        this.searchterm,
        this.employeeid});

  GuaranteeDataResponse.fromJson(Map<String, dynamic> json) {
    createdBy = json['createdBy'];
    createdById = json['createdById'];
    createdDate = json['createdDate'];
    recId = json['recId'];
    top = json['top'];
    authkey = json['authkey'];
    if (json['guaranteetype'] != null) {
      guaranteetype = <Guaranteetype>[];
      json['guaranteetype'].forEach((v) {
        guaranteetype!.add(new Guaranteetype.fromJson(v));
      });
    }
    if (json['perioddays'] != null) {
      perioddays = <Perioddays>[];
      json['perioddays'].forEach((v) {
        perioddays!.add(new Perioddays.fromJson(v));
      });
    }
    if (json['cost_center'] != null) {
      costCenter = <CostCenter>[];
      json['cost_center'].forEach((v) {
        costCenter!.add(new CostCenter.fromJson(v));
      });
    }
    if (json['guarant_percent'] != null) {
      guarantPercent = <GuarantPercent>[];
      json['guarant_percent'].forEach((v) {
        guarantPercent!.add(new GuarantPercent.fromJson(v));
      });
    }
    skip = json['skip'];
    searchterm = json['searchterm'];
    employeeid = json['employeeid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdBy'] = this.createdBy;
    data['createdById'] = this.createdById;
    data['createdDate'] = this.createdDate;
    data['recId'] = this.recId;
    data['top'] = this.top;
    data['authkey'] = this.authkey;
    if (this.guaranteetype != null) {
      data['guaranteetype'] =
          this.guaranteetype!.map((v) => v.toJson()).toList();
    }
    if (this.perioddays != null) {
      data['perioddays'] = this.perioddays!.map((v) => v.toJson()).toList();
    }
    if (this.costCenter != null) {
      data['cost_center'] = this.costCenter!.map((v) => v.toJson()).toList();
    }
    if (this.guarantPercent != null) {
      data['guarant_percent'] =
          this.guarantPercent!.map((v) => v.toJson()).toList();
    }
    data['skip'] = this.skip;
    data['searchterm'] = this.searchterm;
    data['employeeid'] = this.employeeid;
    return data;
  }
}

class Guaranteetype extends GeneralSheetContent {
  String? namear;
  String? id;
  String? nameen;
  int? recid;

  Guaranteetype({this.namear, this.id, this.nameen, this.recid});

  Guaranteetype.fromJson(Map<String, dynamic> json) {
    namear = json['namear'];
    id = json['id'];
    nameen = json['nameen'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['namear'] = this.namear;
    data['id'] = this.id;
    data['nameen'] = this.nameen;
    data['recid'] = this.recid;
    return data;
  }
}

class CostCenter {
  int? recid;
  String? costCenterCode;

  CostCenter({this.recid, this.costCenterCode});

  CostCenter.fromJson(Map<String, dynamic> json) {
    recid = json['recid'];
    costCenterCode = json['cost_center_code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['recid'] = this.recid;
    data['cost_center_code'] = this.costCenterCode;
    return data;
  }
}

class GuarantPercent extends GeneralSheetContent{
  String? id;
  String? nameen;
  String? letterType;
  int? recid;

  GuarantPercent({this.id, this.nameen, this.letterType, this.recid}){
    namear=nameen;
  }

  GuarantPercent.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nameen = json['nameen'];
    letterType = json['letter_type'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nameen'] = this.nameen;
    data['letter_type'] = this.letterType;
    data['recid'] = this.recid;
    return data;
  }
}

class Perioddays extends GeneralSheetContent {
  String? namear;
  String? id;
  String? nameen;
  int? recid;

  Perioddays({this.namear, this.id, this.nameen, this.recid});

  Perioddays.fromJson(Map<String, dynamic> json) {
    namear = json['namear'];
    id = json['id'];
    nameen = json['nameen'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['namear'] = this.namear;
    data['id'] = this.id;
    data['nameen'] = this.nameen;
    data['recid'] = this.recid;
    return data;
  }
}