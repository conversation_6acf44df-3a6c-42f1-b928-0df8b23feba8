import 'package:eeh/shared/widgets/general_text_form_field.dart';
class GenericServiceListModel {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  List<Types>? types;
  List<EsmAttachment>? attachmentsList;
  String? correlationId;
  String? tableName;
  List<ResolutionCategory>? resolutionCate;
  List<PriorityList>? priorityList;
  List<Provider>? provider;
  List<ServicesList>? servicesList;
  List<CategoryList>? categoryList;

  GenericServiceListModel(
      {this.createdBy,
      this.createdById,
      this.createdDate,
      this.recId,
      this.types,
      this.attachmentsList,
      this.correlationId,
      this.tableName,
      this.resolutionCate,
      this.priorityList,
      this.provider,
      this.servicesList,
      this.categoryList});

  GenericServiceListModel.fromJson(Map<String, dynamic> json) {
    createdBy = json['createdBy'];
    createdById = json['createdById'];
    createdDate = json['createdDate'];
    recId = json['recId'];
    if (json['types'] != null) {
      types = <Types>[];
      json['types'].forEach((v) {
        types!.add(Types.fromJson(v));
      });
    }
    if (json['attachments_lis'] != null) {
      attachmentsList = <EsmAttachment>[];
      json['attachments_lis'].forEach((v) {
        attachmentsList!.add(EsmAttachment.fromJson(v));
      });
    }
    correlationId = json['correlationid'];
    tableName = json['table_name'];
    if (json['resolution_cate'] != null) {
      resolutionCate = <ResolutionCategory>[];
      json['resolution_cate'].forEach((v) {
        resolutionCate!.add(ResolutionCategory.fromJson(v));
      });
    }
    if (json['priority_list'] != null) {
      priorityList = <PriorityList>[];
      json['priority_list'].forEach((v) {
        priorityList!.add(PriorityList.fromJson(v));
      });
    }
    if (json['provider'] != null) {
      provider = <Provider>[];
      json['provider'].forEach((v) {
        provider!.add(Provider.fromJson(v));
      });
    }
    if (json['services_list'] != null) {
      servicesList = <ServicesList>[];
      json['services_list'].forEach((v) {
        servicesList!.add(ServicesList.fromJson(v));
      });
    }
    if (json['category_list'] != null) {
      categoryList = <CategoryList>[];
      json['category_list'].forEach((v) {
        categoryList!.add(CategoryList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['createdBy'] = createdBy;
    data['createdById'] = createdById;
    data['createdDate'] = createdDate;
    data['recId'] = recId;
    if (types != null) {
      data['types'] = types!.map((v) => v.toJson()).toList();
    }
    if (attachmentsList != null) {
      data['attachments_lis'] =
          attachmentsList!.map((v) => v.toJson()).toList();
    }
    data['correlationid'] = correlationId;
    data['table_name'] = tableName;
    if (resolutionCate != null) {
      data['resolution_cate'] =
          resolutionCate!.map((v) => v.toJson()).toList();
    }
    if (priorityList != null) {
      data['priority_list'] =
          priorityList!.map((v) => v.toJson()).toList();
    }
    if (provider != null) {
      data['provider'] = provider!.map((v) => v.toJson()).toList();
    }
    if (servicesList != null) {
      data['services_list'] =
          servicesList!.map((v) => v.toJson()).toList();
    }
    if (categoryList != null) {
      data['category_list'] =
          categoryList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Types {
  String? nameen;
  String? provName;
  String? provKey;
  String? provNameAr;
  String? namear;
  int? recid;
  String? typeKey;

  Types(
      {this.nameen,
      this.provName,
      this.provKey,
      this.provNameAr,
      this.namear,
      this.recid,
      this.typeKey});

  Types.fromJson(Map<String, dynamic> json) {
    nameen = json['type_name'];
    provName = json['prov_name'];
    provKey = json['prov_key'];
    provNameAr = json['prov_name_ar'];
    namear = json['type_name_ar'];
    recid = json['recid'];
    typeKey = json['type_key'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type_name'] = nameen;
    data['prov_name'] = provName;
    data['prov_key'] = provKey;
    data['prov_name_ar'] = provNameAr;
    data['type_name_ar'] = namear;
    data['recid'] = recid;
    data['type_key'] = typeKey;
    return data;
  }
}

class ResolutionCategory extends GeneralSheetContent {
  String? nameAr;
  int? recid;
  String? key;
  String? nameEn;

  ResolutionCategory({this.nameAr, this.recid, this.key, this.nameEn}){
    nameen=nameEn;
    namear=nameAr;
  }

 factory ResolutionCategory.fromJson(Map<String, dynamic> json) {
   return ResolutionCategory(
     nameAr: json['name_ar'],
     recid: json['recid'],
     key: json['key'],
     nameEn: json['name_en'],
   );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name_ar'] = nameAr;
    data['recid'] = recid;
    data['key'] = key;
    data['name_en'] = nameEn;
    return data;
  }
}

class PriorityList extends GeneralSheetContent{
  String? priorityEn;
  String? colorx;
  String? priorityAr;
  int? recid;
  String? key;

  PriorityList({this.priorityEn, this.colorx, this.priorityAr, this.recid, this.key}){
    nameen=priorityEn;
    namear=priorityAr;
    color = colorx;
  }

  factory PriorityList.fromJson(Map<String, dynamic> json) {
    return PriorityList(
      priorityEn: json['priority_en'],
      colorx: json['color'],
      priorityAr: json['priority_ar'],
      recid: json['recid'],
      key: json['key'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['priority_en'] = priorityEn;
    data['color'] = colorx;
    data['priority_ar'] = priorityAr;
    data['recid'] = recid;
    data['key'] = key;
    return data;
  }
}

class Provider {
  String? nameen;
  String? provKey;
  String? namear;
  int? recid;

  Provider({this.nameen, this.provKey, this.namear, this.recid});

  Provider.fromJson(Map<String, dynamic> json) {
    nameen = json['prov_name'];
    provKey = json['prov_key'];
    namear = json['prov_name_ar'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['prov_name'] = nameen;
    data['prov_key'] = provKey;
    data['prov_name_ar'] = namear;
    data['recid'] = recid;
    return data;
  }
}

class ServicesList {
  String? nameen;
  String? typeName;
  String? defaultpriority;
  String? serviseKey;
  String? provNameAr;
  String? typeNameAr;
  String? namear;
  String? typeKey;
  String? provName;
  String? provKey;
  String? category;
  int? recid;

  ServicesList(
      {this.nameen,
      this.typeName,
      this.defaultpriority,
      this.serviseKey,
      this.provNameAr,
      this.typeNameAr,
      this.namear,
      this.typeKey,
      this.provName,
      this.provKey,
      this.category,
      this.recid});

  ServicesList.fromJson(Map<String, dynamic> json) {
    nameen = json['srvs_name'];
    typeName = json['type_name'];
    defaultpriority = json['defaultpriority'];
    serviseKey = json['servise_key'];
    provNameAr = json['prov_name_ar'];
    typeNameAr = json['type_name_ar'];
    namear = json['srvs_name_ar'];
    typeKey = json['type_key'];
    provName = json['prov_name'];
    provKey = json['prov_key'];
    category = json['category'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['srvs_name'] = nameen;
    data['type_name'] = typeName;
    data['defaultpriority'] = defaultpriority;
    data['servise_key'] = serviseKey;
    data['prov_name_ar'] = provNameAr;
    data['type_name_ar'] = typeNameAr;
    data['srvs_name_ar'] = namear;
    data['type_key'] = typeKey;
    data['prov_name'] = provName;
    data['prov_key'] = provKey;
    data['category'] = category;
    data['recid'] = recid;
    return data;
  }
}

class CategoryList extends GeneralSheetContent{
  String? categoryNameEn;
  String? categoryNameAr;
  int? recid;
  String? key;

  CategoryList(
      {this.categoryNameEn, this.categoryNameAr, this.recid, this.key}){
        nameen=categoryNameEn;
        namear=categoryNameAr;
      }

 factory CategoryList.fromJson(Map<String, dynamic> json) {
    return CategoryList(
      categoryNameAr: json['category_name_ar'],
      categoryNameEn: json['category_name_en'],
      recid: json['recid'],
      key: json['key'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['category_name_en'] = categoryNameEn;
    data['category_name_ar'] = categoryNameAr;
    data['recid'] = recid;
    data['key'] = key;
    return data;
  }
}


class EsmAttachment {
  int? uploadedbyid;
  String? thumbnail;
  String? uploaddate;
  String? attachmentName;
  int? filesize;
  String? attachmenttypename;
  String? attachmentUrl;
  String? attachmentNote;
  int? attachmentUdaId;
  int? ispublic;
  int? attachmentId;
  String? attachmenttype;
  String? uploadedby;
  int? recid;

  EsmAttachment({
    this.uploadedbyid,
    this.thumbnail,
    this.uploaddate,
    this.attachmentName,
    this.filesize,
    this.attachmenttypename,
    this.attachmentUrl,
    this.attachmentNote,
    this.attachmentUdaId,
    this.ispublic,
    this.attachmentId,
    this.attachmenttype,
    this.uploadedby,
    this.recid,
  });

  factory EsmAttachment.fromJson(Map<String, dynamic> json) {
    return EsmAttachment(
      uploadedbyid: json['uploadedbyid'] == null ? json['uploadedbyid'] : json['uploadedbyid'].toInt(),
      thumbnail: json['thumbnail'],
      uploaddate: json['uploaddate'],
      attachmentName: json['attachment_name'],
      filesize: json['filesize'] == null ? json['filesize'] : json['filesize'].toInt(),
      attachmenttypename: json['attachmenttypename'],
      attachmentUrl: json['attachment_url'],
      attachmentNote: json['attachment_note'],
      attachmentUdaId: json['attachment_uda_id'] == null ? json['attachment_uda_id'] : json['attachment_uda_id'].toInt(),
      ispublic: json['ispublic'] == null ? json['ispublic'] : json['ispublic'].toInt(),
      attachmentId: json['attachment_id'] == null ? json['attachment_id'] : json['attachment_id'].toInt(),
      attachmenttype: json['attachmenttype'],
      uploadedby: json['uploadedby'],
      recid: json['recid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uploadedbyid': uploadedbyid,
      'thumbnail': thumbnail,
      'uploaddate': uploaddate,
      'attachment_name': attachmentName,
      'filesize': filesize,
      'attachmenttypename': attachmenttypename,
      'attachment_url': attachmentUrl,
      'attachment_note': attachmentNote,
      'attachment_uda_id': attachmentUdaId,
      'ispublic': ispublic,
      'attachment_id': attachmentId,
      'attachmenttype': attachmenttype,
      'uploadedby': uploadedby,
      'recid': recid,
    };
  }
}