import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/widgets/general_text_form_field.dart';
import 'package:flutter/material.dart';

class AdminServiceUtils {

    getServiceProvider(
      context, controller, serviceProvider, Function(dynamic)? onSelect,bool isDemmed,) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: GeneralTextFormField(
          labelText: AppLocalizations.of(context).translate("Service Provider"),
          controller: controller,
          hasSearch: true,
          height: 0.65,
          keyboardType: TextInputType.none,
          isReadOnly: true,
          isRequired: false,
          hasBottomSheet: true,
          isDemmed: isDemmed,
          sheetContentList: serviceProvider,
          onSelect: (v) {
            onSelect?.call(v);
          },
          sheetTitle: AppLocalizations.of(context)
              .translate("Service Provider")
              .toString(),
          // validator: (value) {
          //   if (value!.isEmpty) {
          //     return AppLocalizations.of(context)
          //         .translate('You must select Service Provider');
          //   }
          //   return null;
          // },
        ));
  }


    getServiceType(
      context, controller, serviceType, Function(dynamic)? onSelect,bool isDemmed) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: GeneralTextFormField(
          labelText: AppLocalizations.of(context).translate("Service Type"),
          controller: controller,
          hasSearch: true,
          height: 0.65,
          keyboardType: TextInputType.none,
          isReadOnly: true,
          isRequired: false,
          hasBottomSheet: true,
          isDemmed: isDemmed,
          sheetContentList: serviceType,
          onSelect: (v) {
            onSelect?.call(v);
          },
          sheetTitle: AppLocalizations.of(context)
              .translate("Service Type")
              .toString(),
          // validator: (value) {
          //   if (value!.isEmpty) {
          //     return AppLocalizations.of(context)
          //         .translate("You must select Service Type");
          //   }
          //   return null;
          // },
        ));
  }


    getService(
      context, controller, serviceList, Function(dynamic)? onSelect,) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: GeneralTextFormField(
          labelText: AppLocalizations.of(context).translate("Service"),
          controller: controller,
          hasSearch: true,
          height: 0.65,
          keyboardType: TextInputType.none,
          isReadOnly: true,
          isRequired: true,
          hasBottomSheet: true,
          sheetContentList: serviceList,
          onSelect: (v) {
            onSelect?.call(v);
          },
          sheetTitle: AppLocalizations.of(context)
              .translate("Service Name")
              .toString(),
          validator: (value) {
            if (value!.isEmpty) {
              return AppLocalizations.of(context)
                  .translate("You must select Service");
            }
            return null;
          },
        ));
  }  

}