import 'package:eeh/ceo_meetings/model/ceo_meetings_submit_request.dart';
import 'package:eeh/ceo_meetings/model/ceo_meetings_submit_response.dart';
import 'package:eeh/ceo_meetings/model/employee_model.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_bloc.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:eeh/shared/favorite_component/favorite_utils.dart';
import 'package:eeh/shared/favorite_component/models/add_model.dart';
import 'package:eeh/shared/favorite_component/models/delete_model.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../../shared/utility/methods.dart';
import '../model/CEODropdownList.dart';
import '../model/CEOMembers.dart';
import '../repo/ceo_meetings_repo.dart';
import 'ceo_meetings_events.dart';
import 'ceo_meetings_states.dart';

enum EmployeesType {requester, noteTaker, attendees}

class CEOMeetingsBloc extends Bloc<CEOMeetingsEvent, CEOMeetingsState> {
  ///step one
  final TextEditingController ceoGroupsController = TextEditingController();
  final TextEditingController meetingCategoryController =
      TextEditingController();
  final TextEditingController requiredController = TextEditingController();
  final TextEditingController purposeController = TextEditingController();
  final TextEditingController meetingTypeController = TextEditingController();
  final TextEditingController boardController = TextEditingController();
  final TextEditingController priorityController = TextEditingController();

  ///step  two
  final TextEditingController subjectController = TextEditingController();
  final TextEditingController requesterController = TextEditingController();
  final TextEditingController attendeesController = TextEditingController();
  final TextEditingController noteTakerController = TextEditingController();
  final TextEditingController meetingDateController = TextEditingController();
  final TextEditingController fromDateController = TextEditingController();
  final TextEditingController toDateController = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  final TextEditingController agendaController = TextEditingController();
  final TextEditingController expectedController = TextEditingController();

  bool checkBoxValue = false;
  bool validTime = false;

PurposeMeeting? selectedMeetCategoryId;
PurposeMeeting? selectedMeetingTypeId;
PurposeMeeting? selectedPriorityId;
PurposeMeeting? selectedPurposeMeetId;
PurposeMeeting? selectedRequiredMeetId;

  CEODropdownList? ceoDropdownList;
  List<ElmGroupMemeb> selectedCEOMembers = [];
  CEOMembers? ceoMembers;
  TimeOfDay? fromSelectedTime;
  TimeOfDay? toSelectedTime;
  EmployeeResponse? employeesData;
  List<Employee> requestersEmployees = [];
  List<Employee> attendeesEmployees = [];
  List<Employee> noteTakersEmployees = [];

  List<Employee> selectedRequestersEmployees = [];
  List<Employee> selectedAttendeesEmployees = [];
  List<Employee> selectedNoteTakersEmployees = [];
  int skip = 0;
  int top = 10;
  DateRangePickerController dateRangePickerController =
      DateRangePickerController();
  TimeOfDay? fromTimeSelected ;

  bool isFavoriteService = false;
  DeleteFavoriteServiceRequest deleteFavoriteServiceRequest =
      DeleteFavoriteServiceRequest(
    serviceid: "26",
    sessionid: null,
  );

  AddFavoriteServiceRequest addFavoriteServiceRequest =
      AddFavoriteServiceRequest(
    serviceid: "26",
    sessionid: null,
  );
  late ICEOMeetingsRepo _repo;

  CEOMeetingsSubmitResponse? submitRequestResponse;

  CEOMeetingsBloc({required CEOMeetingsRepo cEOMeetingsRepo})
      : super(CEOMeetingsInitialState()) {
    _repo = cEOMeetingsRepo;

    on<CEOMeetingsInitialEvent>((event, emit) async {
      // isFavoriteService = await getFavoriteService("26");
      emit(CEOMeetingsInitialFinishState());
    });

    on<FavoriteServiceValueChangeEvent>((event, emit) async {
      await onFavoriteItemPressed(emit);
      emit(ChangeFavoriteStatusState());
    });

    on<SubmitRequestEvent>((event, emit) async {
      emit(SubmitRequestLoadingState());
      await submitRequest(emit);
    });

    on<GetElmCEOMembersRequestEvent>(_getElmCEOMembers);

    on<GetDropdownListDataRequestEvent>(_getDropdownListData);

    on<GetEmployeesListEvent>(_getEmployeesListData);

    on<GetEmployeesListNextPageEvent>(_getEmployeesListData);

    on<ChangeFieldValueEvent>(_changeFieldValue);

    on<MoveToScreenEvent>(_moveToScreen);

    on<ChangeSearchTextFieldTypeEvent>(_changeSearchTextFieldType);

    on<UploadAttachmentFilesEvent>(uploadAttachmentFiles);

  }

  Future _getElmCEOMembers(
      CEOMeetingsEvent event, Emitter<CEOMeetingsState> emit) async {
    emit(GetElmCEOMembersRequestLoadingState());
    await _repo
        .getElmCEOMembers()
        .then((response) => onGetElmCEOMembersResponse(response, emit));
  }

  void onGetElmCEOMembersResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetElmCEOMembersSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(GetElmCEOMembersRequestErrorState());
    });
  }

  void onGetElmCEOMembersSuccess(data, Emitter emitter) {
    add(GetDropdownListDataRequestEvent());
    ceoMembers = CEOMembers.fromJson(data);
    emitter(GetElmCEOMembersRequestSuccessState(
        ceoRetrieveData: CEOMembers.fromJson(data)));
  }

  ////////////////////

  Future _getDropdownListData(
      CEOMeetingsEvent event, Emitter<CEOMeetingsState> emit) async {
    emit(GetDropdownListDataRequestLoadingState());
    await _repo
        .getDropdownListData()
        .then((response) => onGetDropdownListDataResponse(response, emit));
  }

  void onGetDropdownListDataResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetDropdownListDataSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(GetDropdownListDataRequestErrorState());
    });
  }

  void onGetDropdownListDataSuccess(data, Emitter emitter) {
    ceoDropdownList = CEODropdownList.fromJson(data);
    emitter(GetDropdownListDataRequestSuccessState());
  }

  ////////////////
  Future _getEmployeesListData(event, Emitter<CEOMeetingsState> emit) async {
    if (event is GetEmployeesListNextPageEvent) {
      emit(GetEmployeesListNextPageLoadingState());
    } else {
      skip = 0;
      emit(GetEmployeesListLoadingState());
    }
    await _repo.getEmployeesList({
      "search_term": event.searchKey,
      "top": top,
      "skip": skip
    }).then((response) => onGetEmployeesListDataResponse(event, response, emit));
  }

  Future _changeFieldValue(
      CEOMeetingsEvent event, Emitter<CEOMeetingsState> emit) async {
    emit(ChangeFieldValueState());
  }

  Future _moveToScreen(
      CEOMeetingsEvent event, Emitter<CEOMeetingsState> emit) async {
    emit(MoveToScreenState());
  }

  Future _changeSearchTextFieldType(
      CEOMeetingsEvent event, Emitter<CEOMeetingsState> emit) async {
    emit(ChangeSearchTextFieldTypeState());
  }

  Future uploadAttachmentFiles(
      UploadAttachmentFilesEvent event, Emitter<CEOMeetingsState> emit) async {
    emit(UploadAttachmentLoadingState());
    if (AttachBloc.instance(event.context).files.isNotEmpty &&
        AttachBloc.instance(event.context).isFilesEdited) {
      if (AttachBloc.instance(event.context)
          .files
          .where((element) => element.isNetworkFile == false)
          .toList()
          .isNotEmpty) {
        AttachBloc.instance(event.context).add(UploadFilesEvent(
            false,
            submitRequestResponse?.recId,
            submitRequestResponse?.recId,
            AttachBloc.instance(event.context)
                    .metaDataResponse
                    ?.attachmentId ??
                ''));
      }
    }
    emit(UploadAttachmentFilesSuccessState());
  }

  void onGetEmployeesListDataResponse(
       event, NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onGetEmployeesListDataSuccess(event, data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(GetEmployeesListErrorState());
    });
  }

  void onGetEmployeesListDataSuccess( event, data, Emitter emitter) {
    if(skip == 0 && !(event.isSearchMode ?? false)){
      requestersEmployees = [];
      attendeesEmployees = [];
      noteTakersEmployees = [];
    }
    if (employeesData == null) {
      employeesData = EmployeeResponse.fromJson(data);
    List<Employee> originalList = employeesData?.allElmEmploye ?? [];

    requestersEmployees.addAll(originalList.map((e) => e.copyWith()));
    attendeesEmployees.addAll(originalList.map((e) => e.copyWith()));
    noteTakersEmployees.addAll(originalList.map((e) => e.copyWith()));
    }else {
      employeesData = EmployeeResponse.fromJson(data);
    List<Employee> originalList = employeesData?.allElmEmploye ?? [];
      switch(event.employeesType){
        case EmployeesType.requester:
        if(skip == 0) requestersEmployees = [];
        requestersEmployees.addAll(originalList.map((e) => e.copyWith()));
        requestersEmployees.where((emp) => selectedRequestersEmployees.any((element) => 
        element.employeeId == emp.employeeId,),).forEach((element) => element.isSelected = true,);
        break;
        case EmployeesType.attendees:
        if(skip == 0) attendeesEmployees = [];
        attendeesEmployees.addAll(originalList.map((e) => e.copyWith()));
        attendeesEmployees.where((emp) => selectedAttendeesEmployees.any((element) => 
        element.employeeId == emp.employeeId,),).forEach((element) => element.isSelected = true,);
        break;
        case EmployeesType.noteTaker:
        if(skip == 0) noteTakersEmployees = [];
          noteTakersEmployees.addAll(originalList.map((e) => e.copyWith()));
          noteTakersEmployees.where((emp) => noteTakersEmployees.any((element) => 
        element.employeeId == emp.email,),).forEach((element) => element.isSelected = true,);
        break;
      }
    }
    
    skip = int.parse(employeesData?.skip ?? '0') + 1;

    

    emitter(GetEmployeesListSuccessState(employeeResponse: EmployeeResponse.fromJson(data)));
  }

  Future onFavoriteItemPressed(Emitter emitter) async {
    if (isFavoriteService == true) {
      await FavoriteUtils.deleteFavoriteService(deleteFavoriteServiceRequest)
          .then((value) {
        isFavoriteService = false;
      });
    } else {
      await FavoriteUtils.addFavoriteService(addFavoriteServiceRequest)
          .then((value) {
        isFavoriteService = true;
      });
    }
  }

  Future submitRequest(Emitter emitter) async {
    await _repo.submitRequest(
      CEOMeetingRequest(
        agenda: agendaController.text,
        ceoGroups: selectedCEOMembers.map((e) => CeoEmployee(employeeId: e.employeeid ?? ''),).toList(),
        endTime: toDateController.text,
        meetCategoryId: selectedMeetCategoryId?.id ?? '',
        meetingAttendees: selectedAttendeesEmployees.where((element) => element.isSelected,).toList()
        .map((e) => CeoEmployee(employeeId: e.employeeId ?? ''),).toList(),
        meetingDate: meetingDateController.text,
        meetingSubject: subjectController.text,
        meetingTypeId: selectedMeetingTypeId?.id ?? '',
        noteTakerId: selectedNoteTakersEmployees.firstWhere((element) => element.isSelected,).employeeId ?? '',
        priorityId: selectedPriorityId?.id ?? '',
        purposeMeetId: selectedPurposeMeetId?.id ?? '',
        relatedBoardD: checkBoxValue,
        requesterId: selectedRequestersEmployees.firstWhere((element) => element.isSelected,).employeeId ?? '',
        requiredMeetId: selectedRequiredMeetId?.id ?? '',
        startTime: fromDateController.text,
        whatDoWeExpect: expectedController.text,
        notes: noteController.text,
      ).toJson()
    ).then((response) => onsubmitRequestResponse(response, emitter));
  }

  void onsubmitRequestResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onsubmitRequestSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitRequestErrorState());
    });
  }

  void onsubmitRequestSuccess(data, Emitter emitter) {
    submitRequestResponse = CEOMeetingsSubmitResponse.fromJson(data);

    emitter(SubmitRequestSuccessState());
  }

  void onSelectDate() {
    meetingDateController.text = DateFormat('yyyy-MM-dd')
        .format(dateRangePickerController.selectedDate!);
  }

  String formatTime24Hour(TimeOfDay time) {
    final String hour = time.hour.toString().padLeft(2, '0');
    final String minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  bool isNextButtonEnable() {
    return ceoGroupsController.text.isNotEmpty &&
        meetingCategoryController.text.isNotEmpty &&
        requiredController.text.isNotEmpty &&
        purposeController.text.isNotEmpty &&
        meetingTypeController.text.isNotEmpty &&
        priorityController.text.isNotEmpty;
  }

  bool isSubmitButtonEnable() {
    return (subjectController.text.isNotEmpty &&
        meetingDateController.text.isNotEmpty &&
        requesterController.text.isNotEmpty &&
        attendeesController.text.isNotEmpty &&
        noteTakerController.text.isNotEmpty &&
        fromDateController.text.isNotEmpty &&
        toDateController.text.isNotEmpty &&
        agendaController.text.isNotEmpty &&
        expectedController.text.isNotEmpty &&
        validTime);
  }

  checkDateValidations() {
    if (fromDateController.text.isNotEmpty) {
    TimeOfDay selectedFromTime =  parseTimeOfDay(fromDateController.text);
    checkFromTimeValidations(selectedFromTime);
    } else {
      return;
    }
  }

  checkFromTimeValidations(TimeOfDay selectedTime) {
    if(dateRangePickerController.selectedDate != null) {
      validTime = true;
      final selectedDateTime =
          dateRangePickerController.selectedDate ?? DateTime.now();
      final now = DateTime.now();
      checkValidEndTime(selectedTime);
      if (!validTime) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showMessage("Sorry, this time is invalid.", MessageType.error);
          validTime  = false;
          fromDateController.text = "";
          add(ChangeFieldValueEvent());
          return;
        });
      }

      if (selectedDateTime.day != now.day) {
        validTime = true;
        return;
      }else{
        bool value = selectedTime.hour > now.hour;
        validTime = value;
        if (!validTime) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            showMessage("Sorry,start meeting must be minimum at next hour", MessageType.error);
            validTime  = false;
            add(ChangeFieldValueEvent());
            fromDateController.text = "";
            return;
          });
        }
        return;
      }
    }else{
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showMessage("Sorry, must select meeting Date first.", MessageType.error);
        validTime  = false;
        add(ChangeFieldValueEvent());
        fromDateController.text = "";
      });
    }
  }

  bool checkValidEndTime(TimeOfDay selectedTime){
    if(toDateController.text.isNotEmpty) {
      if (selectedTime.isAfter(parseTimeOfDay(toDateController.text))) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showMessage(
              "Sorry, start time must be before end time.", MessageType.error);
        });
        add(ChangeFieldValueEvent());
        validTime = false;
        return false;
      }
    }
    return true;
  }

  checkToTimeValidations(TimeOfDay toSelectedTime) {
    if (fromTimeSelected != null && dateRangePickerController.selectedDate != null) {
      if (fromTimeSelected!.isAfter(toSelectedTime!)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showMessage("Sorry, end time must be after start time.", MessageType.error);
          validTime = false;
          toDateController.text = "";
          add(ChangeFieldValueEvent());
        });
      } else {
        validTime = true;
      }
    }
    else{
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showMessage("Sorry, must select meeting start time first.", MessageType.error);
        validTime = false;
        add(ChangeFieldValueEvent());
        toDateController.text = "";
      });
    }
  }



}
