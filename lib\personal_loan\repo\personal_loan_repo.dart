import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/secure_storage.dart';
import 'package:eeh/personal_loan/models/employee_loan_info_response_model/employee_loan_info_response_model.dart';

import '../models/submit_personal_loan_request_body.dart';

abstract class IPersonalLoanRepo {
  Future<NetworkResponse> getEmpLoanInfo({required String empId});
  Future<NetworkResponse> submitPersonalEmpLoanInfo(
      {required SubmitPersonalLoanRequestBody submitPersonalLoanRequestBody});
}

class PersonalLoanRepo implements IPersonalLoanRepo {
  PersonalLoanRepo._internal();

  static final PersonalLoanRepo _instance = PersonalLoanRepo._internal();

  static PersonalLoanRepo get instance => _instance;

  @override
  Future<NetworkResponse> getEmpLoanInfo({required String empId}) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";

    NetworkResponse<dynamic> response = await Api<PERSON>elper().apiCall(genericObject,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'formName': 'Employee Loan Info Form',
          'moduleName': 'Employee Loan Info',
          'appKey': 'EPL',
          'Content-Type': 'application/json'
        },
        body: {
          "employeeid": empId
        });
    return response;
  }

  @override
  Future<NetworkResponse> submitPersonalEmpLoanInfo(
      {required SubmitPersonalLoanRequestBody
          submitPersonalLoanRequestBody}) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";

    NetworkResponse<dynamic> response = await ApiHelper().apiCall(
      genericObject,
      requestType: RequestType.post,
      sessionToken: sessionToken,
      headers: {
        'formName': 'Service Data Form',
        'moduleName': 'Service Data',
        'appKey': 'EPL',
        'Content-Type': 'application/json'
      },
      body: submitPersonalLoanRequestBody.toJson(),
    );
    return response;
  }
}
