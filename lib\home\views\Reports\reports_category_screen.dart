import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_bloc.dart';
import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_events.dart';
import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_states.dart';
import 'package:eeh/home/<USER>/Reports/reports_layout.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/services_screen/bloc/services_state.dart';
import 'package:eeh/services_screen/screens/services_utils.dart';
import 'package:eeh/services_screen/widgets/elm_service_list_item.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/widgets/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../services_screen/models/services_response.dart';
import '../../../shared/utility/methods.dart';



class ReportsCategoryScreen extends StatefulWidget {


  const ReportsCategoryScreen(
      {super.key,});

  @override
  State<ReportsCategoryScreen> createState() => _ReportsCategoryScreenState();
}

class _ReportsCategoryScreenState extends State<ReportsCategoryScreen> {
  late ReportsBloc bloc;
  bool isSearchTapped = false;
  TextEditingController searchController=TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title:AppLocalizations.of(context).translate('Reports'),
        appbar: AppbarType.searchWithDownArrow /*widget.screenType == ServiceScreenType.mainScreen
            ? AppbarType.search
            : (widget.screenType == ServiceScreenType.navigateToAnotherScreen
                ? AppbarType.titleWithBackArrow
                : AppbarType.titleWithBackArrow)*/,
        onActionIconPressed: () {
          setState(() {
            isSearchTapped = !isSearchTapped;
          });
        },
      ),
      body: BlocProvider<ReportsBloc>(
          create: (context) => ReportsBloc()..add(ReportsInitialEvent()),
          child: BlocConsumer<ReportsBloc, ReportsState>(
              listener: (ctx,state){
                if(state is ReportsInitialState) {
                  bloc?.add(ReportsLoaderEvent());
                }
              },
              builder: (context, state) {
            bloc = context.read<ReportsBloc>();
            return state is ServicesListErrorState
                ? SizedBox.shrink()
                : (bloc.servicesResponse == null ||
                        bloc.servicesResponse?.servicescategor == null ||
                (bloc.servicesResponse?.servicescategor ?? []).isEmpty ||
                    state is ServicesListLoadingState)
                ? Center(
                    child: CircularProgressIndicator(),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isSearchTapped)
                  Padding(
                    padding: EdgeInsets.all(4.0.h),
                    child: TextField(
                      controller: searchController,
                      decoration: InputDecoration(
                          hintText:
                              AppLocalizations.of(context).translate('Search'),
                          border: OutlineInputBorder()),
                      onChanged: (v) {
                        bloc.add(ServiceSearchEvent(v));
                      },
                    ),
                  ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0),
                    child: searchController.text.isEmpty?ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        Servicescategor? categoryItem= bloc.servicesResponse?.servicescategor?[index];

                                  return ((categoryItem?.servicesSubCategory ??
                                                  [])
                                              .isEmpty &&
                                          (categoryItem?.isActive ?? false))
                                      ?   SizedBox.shrink():buildCategoryItem(categoryItem);
                                },
                      itemCount: bloc.servicesResponse?.servicescategor?.length,
                    ):bloc.filteredList.isNotEmpty?ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {

                        return buildServiceListItem(bloc.filteredList[index],context);
                      },
                      itemCount:bloc.filteredList.length,
                    ):getEmptyListView(context,true),
                  ),
                ),
              ],
            );
          })),
    );
  }
  buildCategoryItem( Servicescategor? categoryItem)=>SizedBox(
    width: 375.w,
    height: 80.h,
    child: Card(
      elevation: 2,
      color: Colors.white,
      shadowColor: Colors.white,
      surfaceTintColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
            Radius.circular(8.0)),
        side: BorderSide(
            color: Color(0xffF6F6F6),
            width: 1),
      ),
      child: ElmServicesListItemComponent(
        serviceDescription: AppLocalizations.appLang=="en"?categoryItem?.descriptionEN:categoryItem?.descriptionAR,
        onTap: () {
          if ((categoryItem
              ?.servicesSubCategory ??
              [])
              .isNotEmpty &&
              (categoryItem?.isActive ??
                  false)) {
            Navigation.navigateFromBottomToTop(
                context,
                ReportsScreen(
                  bloc: bloc,
                  servicesCategory:
                  categoryItem,
                ));
          }
        },
        serviceTitle: AppLocalizations
            .appLang ==
            "en"
            ? categoryItem?.categoryNameEn
            : categoryItem
            ?.categoryNameAr,
        previousServiceName:
            AppLocalizations.of(context)
                .translate('Reports'),
        serviceImageClass:categoryItem?.categoryIconClass??"",
        serviceImageURL:
        "${categoryItem?.categoryIconName}",
        backgroundColor: categoryItem
            ?.backgroundColor ??
            '',
        isServiceEnabled:
        categoryItem?.isActive ??
            true,
      ),
    ),
  );
}
