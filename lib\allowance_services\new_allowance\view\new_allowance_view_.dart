import 'package:eeh/allowance_services/new_allowance/bloc/new_allowance_bloc.dart';
import 'package:eeh/allowance_services/new_allowance/bloc/new_allowance_event.dart';
import 'package:eeh/allowance_services/new_allowance/bloc/new_allowance_state.dart';
import 'package:eeh/allowance_services/utils.dart';
import 'package:eeh/allowance_services/widget/allowance_card.dart';
import 'package:eeh/allowance_services/widget/success_screen_content.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_event.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/widgets/cancel_button_component.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NewAllowanceView extends StatefulWidget {
  final bool isGas;
  const NewAllowanceView({super.key, this.isGas = false});

  @override
  State<NewAllowanceView> createState() => _NewAllowanceViewState();
}

class _NewAllowanceViewState extends State<NewAllowanceView> {
  late NewAllowanceBloc bloc;
  AllowanceUtils utils = AllowanceUtils();

  @override
  void dispose() {
    bloc.beneficialEmployeeController.dispose();
    bloc.startDateTextController.dispose();
    bloc.endDateTextController.dispose();
    bloc.commentController.dispose();
    bloc.eligableController.dispose();
    bloc.startDateController.dispose();
    bloc.endDateController.dispose();
    bloc.numberOfMonthsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<NewAllowanceBloc>(
          create: (context) => NewAllowanceBloc()
            ..add(NewAllowanceInitialEvent(isGas: widget.isGas)),
        ),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
      ],
      child: Scaffold(
        appBar: getAppBarWidget(),
        body: BlocConsumer<NewAllowanceBloc, NewAllowanceState>(
          listenWhen: (previous, current) =>
              current is NewAllowanceInitialState,
          listener: (context, state) {
            bloc.add(EmployeeAllowanceEvent());
          },
          buildWhen: (previous, current) =>
              current is NewAllowanceInitialState ||
              current is EmployeeAllowanceSuccessState,
          builder: (context, state) {
            bloc = context.read<NewAllowanceBloc>();
            bloc.ctx = context;
            if (state is NewAllowanceInitialState ||
                state is EmployeeAllowanceLoadingState) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return getBody();
            }
          },
        ),
      ),
    );
  }

  PreferredSize getAppBarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: widget.isGas
          ? FavoriteComponent(
              title:
                  AppLocalizations.of(context).translate('New Gas Allowance'),
              id: '40',
            )
          : FavoriteComponent(
              title: AppLocalizations.of(context)
                  .translate('New Communication Allowance'),
              id: '39',
            ),
    );
  }

  getBody() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height - 220.h,
            child: Form(
              key: bloc.formKey,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      getBeneficialEmployeeWidget(),
                      utils.getEligableAmountWidget(
                          context, bloc.eligableController),
                      getNumberOfMonthsWidget(),
                      getStartDate(),
                      getEndDate(),
                      utils.getCommentsWidget(context, bloc.commentController),
                      utils.getImportantNotes(context),
                      addEmplyeeButton(),
                      Divider(
                        thickness: 1,
                        color: borderLightColor,
                        indent: 15,
                        endIndent: 15,
                      ),
                      getAllowanceCardWidget(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: getSubmitButton(),
          ),
        ],
      ),
    );
  }

  Widget getBeneficialEmployeeWidget() {
    return utils.getBeneficialEmployeeWidget(
      content: bloc.allowanceListResponse?.employees ?? [],
      context: context,
      onSelect: (value) {
        bloc.add(BeneficialEmployeeValueChangelEvent(value));
      },
      controller: bloc.beneficialEmployeeController,
    );
  }

  Widget getNumberOfMonthsWidget() {
    return utils.getNumberOfMonthsWidget(
      context: context,
      onSelect: (value) {
        bloc.add(NumberOfMonthsValueChangelEvent(value));
        if (bloc.startDateTextController.text.isNotEmpty) {
          bloc.add(CalculateEndDateEvent());
        }
      },
      controller: bloc.numberOfMonthsController,
    );
  }

  Widget getStartDate() {
    return utils.getStartDate(
      context: context,
      textController: bloc.startDateTextController,
      dateController: bloc.startDateController,
      onDateChange: () {
        bloc.add(StartDateValueChangelEvent());
      },
    );
  }

  Widget getEndDate() {
    return utils.getEndDate(
      context: context,
      textController: bloc.endDateTextController,
      dateController: bloc.endDateController,
      onDateChange: () {
        bloc.add(EndDateValueChangelEvent());
      },
    );
  }

  Widget addEmplyeeButton() {
    return BlocBuilder<NewAllowanceBloc, NewAllowanceState>(
        buildWhen: (previous, current) =>
            current is BeneficialEmployeeValueChangelState ||
            current is CalculateEndDateState ||
            current is NumberOfMonthsValueChangelState ||
            current is StartDateValueChangelState ||
            current is EndDateValueChangelState ||
            current is AddEmployeeState,
        builder: (context, state) {
          Color color =
              (bloc.isAddButtonEnabled()) ? primaryColor : Colors.grey;
          return Padding(
            padding: const EdgeInsets.only(
                bottom: 0.0, left: 15.0, right: 15.0, top: 15.0),
            child: InkWell(
                onTap: () {
                  if (bloc.formKey.currentState!.validate()) {
                    if (bloc.isAddButtonEnabled()) {
                      bloc.add(AddEmployeeEvent());
                    } else {
                      return;
                    }
                  }
                },
                child: Column(children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 5,
                    children: [
                      Text(
                        AppLocalizations.of(context).translate('Add Employee'),
                        style: TextStyle(
                          color: color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Icon(
                        Icons.add_circle,
                        color: color,
                      ),
                    ],
                  ),
                  Divider(
                    thickness: 1,
                    color: color,
                    indent: 100,
                    endIndent: 100,
                  ),
                ])),
          );
        });
  }

  getAllowanceCardWidget() {
    return BlocBuilder<NewAllowanceBloc, NewAllowanceState>(
        buildWhen: (previous, current) =>
            current is AddEmployeeState || current is ActionTappedState,
        builder: (context, state) {
          return Column(
            children: [
              Visibility(
                child: utils.getEmplyeeAllowanceNumber(
                  context,
                  bloc.allowanceCardList.length,
                ),
                visible: bloc.allowanceCardList.isNotEmpty,
              ),
              Padding(
                padding: const EdgeInsets.all(15.0),
                child: SizedBox(
                  width: double.infinity,
                  height: 400.h,
                  child: ListView.builder(
                      itemBuilder: (context, index) {
                        final item = bloc.allowanceCardList[index];
                        return AllowanceCard(
                          actionType: ActionType.delete,
                          employeeEn: item.employeenameen ?? '',
                          employeeAr: item.employeenamear ?? '',
                          numOfMonths: item.numberofmonths.toString(),
                          targetAmount: item.eligibleamount.toString(),
                          startDate: item.startdate ?? '',
                          endDate: item.enddate ?? "",
                          comment: item.comment ?? "",
                          onActionTap: () {
                            bloc.add(ActionTappedEvent(index));
                          },
                        );
                      },
                      itemCount: bloc.allowanceCardList.length),
                ),
              ),
            ],
          );
        });
  }

  Widget getSubmitButton() {
    return BlocConsumer<NewAllowanceBloc, NewAllowanceState>(
        listenWhen: (previous, current) =>
            current is SubmitAllowanceSuccessState,
        listener: (context, state) {
          if (state is SubmitAllowanceSuccessState) {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => SuccessScreen(
                          title: AppLocalizations.of(context)
                              .translate("Request Submitted Successfully"),
                          contentWidget: SuccessScreenContentWidget(
                            submitResponse: bloc.submitResponse,
                          ),
                        )));
          }
        },
        buildWhen: (previous, current) =>
            current is SubmitAllowanceLoadingState ||
            current is SubmitAllowanceErrorState ||
            current is SubmitAllowanceSuccessState ||
            current is AddEmployeeState ||
            current is ActionTappedState,
        builder: (context, state) {
          return Column(
            children: [
              CancelButtonComponent(),
              SubmitButtonComponent(
                text: AppLocalizations.of(context).translate('Submit'),
                onPressed: () {
                  if (bloc.allowanceCardList.isNotEmpty) {
                    bloc.add(SubmitAllowanceEvent());
                  }
                },
                backGroung: (bloc.allowanceCardList.isNotEmpty)
                    ? primaryColor
                    : primaryColor.withOpacity(0.2),
                isLoading: state is SubmitAllowanceLoadingState,
              ),
            ],
          );
        });
  }
}
