import '../models/guaranteeDataResponse.dart';
import '../models/projectWbsModel.dart';

abstract class LetterState {}

class LetterInitialState extends LetterState {}

// Loading states
class GuaranteeDataLoadingState extends LetterState {}
class GuaranteeDataSuccessState extends LetterState {
  final GuaranteeDataResponse response;
  GuaranteeDataSuccessState({required this.response});
}
class GuaranteeDataErrorState extends LetterState {}

class ProjectsLoadingState extends LetterState {}
class ProjectsSuccessState extends LetterState {
  final ProjectWpsResponse projectWpsResponse;
  ProjectsSuccessState({required this.projectWpsResponse});
}
class ProjectsErrorState extends LetterState {}

class ChangeBudgetTypeState extends LetterState {}

class ChangeGuaranteeTypeState extends LetterState {}

class ValidateFieldsState extends LetterState {
  final bool isValid;
  ValidateFieldsState(this.isValid);
}

class ChangeGuaranteePercentState extends LetterState {}

class ChangeGuaranteeValueState extends LetterState {}

class LetterProjectNameLoadingState extends LetterState {}
class CustomerListLoadingState extends LetterState {}

class LetterSuccessState extends LetterState {
  final List<Perioddays> periodDays;
  final List<CostCenter> costCenter;
  final List<Guaranteetype> guaranteeTypes;
  final List<GuarantPercent> percentages;

  LetterSuccessState({
    required this.periodDays,
    required this.costCenter,
    required this.guaranteeTypes,
    required this.percentages,
  });
}

 class LetterDataState extends LetterState {
  final List<Perioddays> periodDays;
  final List<ProjectWps> projectWbs;

  LetterDataState({
    required this.periodDays,
    required this.projectWbs,
  });

  LetterDataState copyWith({
    List<Perioddays>? periodDays,
    List<ProjectWps>? projectWbs,
  }) {
    return LetterDataState(
      periodDays: periodDays ?? this.periodDays,
      projectWbs: projectWbs ?? this.projectWbs,
    );
  }
}
////////////////////////
class LetterProjectNameSuccessState extends LetterState {}
class CustomerListSuccessState extends LetterState {}

// Error states
class LetterErrorState extends LetterState {
  final String error;
  LetterErrorState({required this.error});
}

class LetterProjectNameErrorState extends LetterState {
  final String error;
  LetterProjectNameErrorState({required this.error});
}

class CustomerListErrorState extends LetterState {
  final String error;
  CustomerListErrorState({required this.error});
}

// Add these to your letter_state.dart

class SubmitLoadingState extends LetterState {}
class SubmitSuccessState extends LetterState {
}
class SubmitErrorState extends LetterState {
}


class UploadAttachmentLoadingState extends LetterState{}

class UploadAttachmentFilesSuccessState extends LetterState{}

class UploadAttachmentFilesErrorState extends LetterState{}

class FilesUpdatedState extends LetterState{}