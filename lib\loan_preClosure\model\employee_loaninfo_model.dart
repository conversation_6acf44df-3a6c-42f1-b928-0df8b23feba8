import 'loan_model.dart';

class LoanStatusModel {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final String employeeId;
  final String elmibanNumber;
  final bool activeLoan;
  final List<Loan> loans;
  final String maximumLoanLimit;

  LoanStatusModel({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeId,
    required this.elmibanNumber,
    required this.activeLoan,
    required this.loans,
    required this.maximumLoanLimit,
  });

  factory LoanStatusModel.fromJson(Map<String, dynamic> json) {
    //    print('Raw activeLoan value: ${json['activeloan']}');
    return LoanStatusModel(
      createdBy: json['createdBy'] ?? '',
      createdById: json['createdById'] ?? '',
      createdDate: json['createdDate'] ?? '',
      recId: json['recId'] ?? 0,
      employeeId: json['employeeid'] ?? '',
      elmibanNumber: json['elmibannumber'] ?? '',
      activeLoan: json['activeloan'].toString().toLowerCase() == 'true',
      loans: (json['loans'] as List<dynamic>?)
          ?.map((e) => Loan.fromJson(e as Map<String, dynamic>))
          .toList() ??
          [],
      maximumLoanLimit: json['maximumloanlimi'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'employeeid': employeeId,
      'elmibannumber': elmibanNumber,
      'activeloan': activeLoan.toString(),
      'loans': loans.map((e) => e.toJson()).toList(),
      'maximumloanlimi': maximumLoanLimit,
    };
  }
}
