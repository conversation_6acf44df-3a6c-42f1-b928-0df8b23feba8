import 'package:eeh/my_requests_revamp/bloc/requests_events.dart';
import 'package:eeh/my_requests_revamp/bloc/requests_states.dart';
import 'package:eeh/my_requests_revamp/services_repo.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rxdart/rxdart.dart';

import '../../../shared/widgets/toast.dart';
import '../../l10n/app_localizations.dart';
import '../../leave_request/models/delegates_model.dart';
import '../../shared/app_constants.dart';
import '../../shared/mapper.dart';
import '../../shared/utility/methods.dart';
import '../../shared/utility/text_helper.dart';
import '../models/service_response.dart';

class RequestsBlocRevamp extends Bloc<RequestsEvent, RequestsState> {
  RequestsBlocRevamp({required ServicesRepoRevamp mRequestsRepoRevamp})
      : super(RequestsInitialState()) {
    _repo = mRequestsRepoRevamp;
    on<PendingRequestsEvent>(_loadPendingRequests);
    on<HistoryRequestsEvent>(
      _loadHistoryRequests,
    );
    on<AllRequestsEvent>(
      _loadAllRequests,
    );
    on<SearchInRequestsEvent>(_searchInRequests);
    on<WithdrawRequestEvent>(_withdraw);
    on<SendReminderEvent>(_sendReminder);
  }

  static RequestsBlocRevamp instance(BuildContext context) =>
      BlocProvider.of(context);
  ServicesResponseRevamp? pendingServicesResponse;
  ServicesResponseRevamp? historyServicesResponse;
  ServicesResponseRevamp? allServicesResponse;
  List<ServicesDatum>? filteredPendingServices;
  List<ServicesDatum>? filteredHistoryServices;
  List<ServicesDatum>? filteredAllServices;
  final _request = "request";
  late ServicesRepoRevamp _repo;
  TextEditingController searchController = TextEditingController();
  TextEditingController reasonController = TextEditingController();
  List<Delegatablelist> search = [];
  int? pageNumber = 1;
  bool lastPage = false;
  SortType sortType = SortType.desc;

  EventTransformer<RequestsEvent> switchMap<RequestsEvent>() {
    return (events, mapper) => events.switchMap(mapper);
  }

  Future<void> _loadPendingRequests(
      PendingRequestsEvent event, Emitter<RequestsState> emit) async {
    pendingServicesResponse = null;
   if (pageNumber == 1){
     filteredPendingServices?.clear();
     emit(PendingRequestsLoadingState());
   }else{
     emit(SubmitLoadingState());
   }
    await _repo
        .getPendingServicesRevamp(
          _request,pageNumber.toString(),event.sortType,event.searchKey
        )
        .then((response) => onPendingRequestResponse(response, emit));
  }

  void onPendingRequestResponse(
      NetworkResponse response, Emitter<RequestsState> emitter) {
    response.maybeWhen(ok: (data) {
      onPendingRequestSuccess(data, emitter);
      emitter(SubmitSuccessState());

          }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(PendingRequestsErrorState());
      emitter(SubmitErrorState());
    });
  }

  void onPendingRequestSuccess(data, Emitter<RequestsState> emitter) {
    pendingServicesResponse =
        Mapper(ServicesResponseRevamp(), data) as ServicesResponseRevamp;
    if ((pageNumber ?? 1) > 1) {
      filteredPendingServices =  (filteredPendingServices ?? []) + (pendingServicesResponse?.servicesData ?? []);
    }else{
      filteredPendingServices = pendingServicesResponse?.servicesData;
    }
    if((pendingServicesResponse?.servicesData ?? []).length < 19){
      lastPage = true;
    }
    emitter(PendingRequestsSuccessState());
  }

  Future<void> _loadHistoryRequests(
      HistoryRequestsEvent event, Emitter<RequestsState> emit) async {
    historyServicesResponse = null;
    if (pageNumber == 1){
      filteredHistoryServices?.clear();
      emit(HistoryRequestsLoadingState());
    }else{
      emit(SubmitLoadingState());
    }
    await _repo
        .getHistoryServicesRevamp(
          _request,pageNumber.toString(),event.sortType,event.searchKey
        )
        .then((response) => onHistoryRequestsResponse(response, emit));
  }

  void onHistoryRequestsResponse(
      NetworkResponse response, Emitter<RequestsState> emitter) {
    response.maybeWhen(ok: (data) {
      onHistoryRequestsSuccess(data, emitter);
      emitter(SubmitSuccessState());
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(HistoryRequestsErrorState());
      emitter(SubmitErrorState());
    });
  }

  void onHistoryRequestsSuccess(data, Emitter emitter) {
    historyServicesResponse =
        Mapper(ServicesResponseRevamp(), data) as ServicesResponseRevamp;
    if ((pageNumber ?? 1)> 1) {
      filteredHistoryServices =  (filteredHistoryServices ?? []) + (historyServicesResponse?.servicesData ?? []);
    }else{
      filteredHistoryServices = historyServicesResponse?.servicesData;
    }
    if((historyServicesResponse?.servicesData ?? []).length < 19){lastPage = true;}
    emitter(HistoryRequestsSuccessState());
  }

  bool isInProgressRequest(String requestStatus, String requestType) {
    if (requestType.toLowerCase().contains('leave')) {
      return requestStatus.toLowerCase().contains('pending') ||
          requestStatus.toLowerCase().contains('approve');
    } else {
      return requestStatus.toLowerCase().contains('pending');
    }
  }


  Future<void> _loadAllRequests(
      AllRequestsEvent event, Emitter<RequestsState> emit) async {
    allServicesResponse = null;
    if (pageNumber == 1){
      filteredAllServices?.clear();
      emit(AllRequestsLoadingState());
    }else{
      emit(SubmitLoadingState());
    }
    await _repo
        .getAllServicesRevamp(
          _request, pageNumber.toString(),event.sortType,event.searchKey
        )
        .then((response) => onAllRequestsResponse(response, emit));
  }

  void onAllRequestsResponse(
      NetworkResponse response, Emitter<RequestsState> emitter) {
    response.maybeWhen(ok: (data) {
      onAllRequestsSuccess(data, emitter);
      emitter(SubmitSuccessState());
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(AllRequestsErrorState());
      emitter(SubmitErrorState());
    });
  }

  void onAllRequestsSuccess(data, Emitter<RequestsState> emitter) {
    allServicesResponse =
        Mapper(ServicesResponseRevamp(), data) as ServicesResponseRevamp;
    if ((pageNumber ?? 1) > 1) {
      filteredAllServices =  (filteredAllServices ?? []) + (allServicesResponse?.servicesData ?? []);
    }else{
      filteredAllServices = allServicesResponse?.servicesData;
    }
    if((allServicesResponse?.servicesData ?? []).length < 19){lastPage = true;}
    emitter(AllRequestsSuccessState());
  }
  Future<void> _withdraw(
      WithdrawRequestEvent event, Emitter<RequestsState> emit) async {
    emit(WithdrawRequestLoadingState());
    try {
      NetworkResponse response = await _repo.withdrawRequest(
          mutableContext: event.mutableContext,
          reason: event.reason);
      if (response.isSuccess) {
        emit(WithdrawRequestSuccessState(response.data["mutable_context"]));
        showMessage( AppLocalizations.appLang == "en"
            ? 'Request withdrawn  Successfully'
            : 'تم الغاء الطلب بنجاح',
            MessageType.success);
      } else {
        emit(WithdrawRequestErrorState());
        showMessage('${response.error}', MessageType.error);
      }
      reasonController.text = "";
    } catch (e) {
      showMessage('Request Withdrawn Failed', MessageType.error);
    }
  }

  Future<void> _sendReminder(
      SendReminderEvent event, Emitter<RequestsState> emit) async {
    emit(SendReminderLoadingState());
    try {
      NetworkResponse response = await _repo.sendReminder(
        empId: event.empId,
        workId: event.encWorkId,
        message: event.message,
      );
      if (response.isSuccess) {
        showMessage(
            AppLocalizations.appLang == "en"
                ? "Reminder has sent"
                : "تم التذكير",
            MessageType.success);
        emit(SendReminderSuccessState());
      } else {
        showMessage(response.error.toString(), MessageType.error);
        emit(SendReminderErrorState());
      }
    } catch (e) {
      showMessage(e.toString(), MessageType.error);

      emit(SendReminderErrorState());
    }
  }

  Future<void> _searchInRequests(
      RequestsEvent event, Emitter<RequestsState> emit) async {
    emit(SearchInRequestsLoadingState());
    try {
      if (searchController.text.isEmpty) {
        filteredAllServices = allServicesResponse?.servicesData;
        filteredHistoryServices = historyServicesResponse?.servicesData;
        filteredPendingServices = pendingServicesResponse?.servicesData;
      } else {
        if (TextHelper.checkInputNumeric(searchController.text)) {
          _searchUsingRequestId();
        } else {
          _searchUsingRequestType();
        }
      }
      emit(SearchInRequestsSuccessState());
    } catch (e) {
      emit(SearchInRequestsErrorState());
    }
  }

  _searchUsingRequestId() {
    if (searchController.text.isNotEmpty) {
      final suggestedPendingRequestList = _getFilteredPendingRequestList();
      filteredPendingServices = suggestedPendingRequestList;
      final suggestedAllRequestList = _getFilteredAllRequestList();
      filteredAllServices = suggestedAllRequestList;
      final suggestedHistoryRequestList = _getFilteredHistoryRequestList();
      filteredHistoryServices = suggestedHistoryRequestList;
    } else {
      filteredPendingServices = pendingServicesResponse?.servicesData;
      filteredAllServices = allServicesResponse?.servicesData;
      filteredHistoryServices = historyServicesResponse?.servicesData;
    }
  }

  _getFilteredPendingRequestList() =>
      pendingServicesResponse?.servicesData?.where((ServicesDatum service) {
        final taskId = service.requestid.toString().toLowerCase();
        final input = searchController.text.toLowerCase();
        return taskId.contains(input);
      }).toList();

  _getFilteredAllRequestList() =>
      allServicesResponse?.servicesData?.where((ServicesDatum service) {
        final taskId = service.requestid.toString().toLowerCase();
        final input = searchController.text.toLowerCase();
        return taskId.contains(input);
      }).toList();

  _getFilteredHistoryRequestList() =>
      historyServicesResponse?.servicesData?.where((ServicesDatum service) {
        final taskId = service.requestid.toString().toLowerCase();
        final input = searchController.text.toLowerCase();
        return taskId.contains(input);
      }).toList();
  _getLocalItemValue(item) =>
      containsOnlyEnglishLetters(searchController.text.toLowerCase())
          ? item["value_en"]
          : (AppLocalizations.appLang == 'en'
              ? item["value_en"]
              : item["value_ar"]);

  isMatchedCard(ServicesDatum element) {
    return (element.serviceCardListItems?.any((item) => _getLocalItemValue(item)
                .toString()
                .toLowerCase()
                .contains(searchController.text.toLowerCase())) ??
            false) ||
        _searchUsingNextApproval(element) ||
        _searchUsingRequestStatus(element);
  }

  bool _searchUsingNextApproval(ServicesDatum element) =>
      (AppLocalizations.appLang == 'en'
          ? element.nextapprovalname
              ?.toLowerCase()
              .contains(searchController.text.toLowerCase())
          : element.nextapprovalnameAr
              ?.toLowerCase()
              .contains(searchController.text.toLowerCase())) ??
      false;

  bool _searchUsingRequestStatus(ServicesDatum element) =>
      (AppLocalizations.appLang == 'en'
          ? element.requeststatus
              ?.toLowerCase()
              .contains(searchController.text.toLowerCase())
          : element.requeststatusAr
              ?.toLowerCase()
              .contains(searchController.text.toLowerCase())) ??
      false;

  _searchUsingRequestType() {
    filteredPendingServices = [];
    pendingServicesResponse?.servicesData?.forEach((element) {
      if (isMatchedCard(element)) {
        filteredPendingServices?.add(element);
      }
    });

    filteredHistoryServices = [];
    historyServicesResponse?.servicesData?.forEach((element) {
      if (isMatchedCard(element)) {
        filteredHistoryServices?.add(element);
      }
    });

    filteredAllServices = [];
    allServicesResponse?.servicesData?.forEach((element) {
      if (isMatchedCard(element)) {
        filteredAllServices?.add(element);
      }
    });
  }
  incrementPageNumber(){
    pageNumber = (pageNumber ?? 1) + 1;
  }
  clearPageNumber(){
    pageNumber = 1;
    lastPage = false;
  }
}
