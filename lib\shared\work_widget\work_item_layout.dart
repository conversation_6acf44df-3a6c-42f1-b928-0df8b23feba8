import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/work_widget/utility_widgets/utility_methods.dart';
import 'package:eeh/shared/work_widget/utility_widgets/withdraw_sheet.dart';
import 'package:eeh/shared/work_widget/work_bloc/work_bloc.dart';
import 'package:eeh/shared/work_widget/work_bloc/work_event.dart';
import 'package:eeh/shared/work_widget/work_bloc/work_states.dart';
import 'package:eeh/shared/work_widget/work_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../l10n/app_localizations.dart';

import '../../service_details_revamp/details_layout_revamp.dart';
import 'package:eeh/shared/styles/colors.dart';
import '../utility/navigation_utility.dart';
import 'models/work_model.dart';

class WorkItemLayout extends StatefulWidget {
  String? createdDate;
  bool? isOverdue;
  WorkModel workModel;

  WorkItemLayout({
    super.key,
    this.isOverdue = false,
    required this.workModel,
  });

  @override
  State<WorkItemLayout> createState() => _WorkItemLayoutState();
}

class _WorkItemLayoutState extends State<WorkItemLayout> {
  Color borderColor = Colors.grey;
  String cardOwnerName = '';
  late WorkBloc workBloc;
  List workItems = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    workItems = widget.workModel.workItems;

    return BlocProvider(
      create: (context) => WorkBloc()..add(WorkInitialEvent()),
      child: BlocBuilder<WorkBloc, WorkState>(
          buildWhen: (current, _) => current is WorkInitialState,
          builder: (context, state) {
            workBloc = WorkBloc.instance(context);
            return InkWell(
              onLongPress: (widget.workModel.workType == WorkType.myTasks ||
                      widget.workModel.workType == WorkType.myRequests)
                  ? () {
                      openDetailsDialog(
                        context,
                        widget.workModel,
                        widget.workModel.workType == WorkType.myTasks
                            ? WorkType.taskDetailsDialog
                            : WorkType.requestDetailsDialog,
                      );
                    }
                  : null,
              onTap: _handleDetailsNavigation,
              child: Container(
                padding: const EdgeInsets.all(10),
                margin: EdgeInsets.only(bottom: 10.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: (widget.workModel.workType == WorkType.myTasks ||
                          widget.workModel.workType == WorkType.myRequests)
                      ? Border.all(
                          color:getStatusColor(),
                          width: .6.w,
                        )
                      : Border.all(
                    color:Colors.grey.withOpacity(0.4),
                    width: .6.w,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if((widget.workModel.slaEn??"").isNotEmpty||(widget.workModel.slaAr??"").isNotEmpty)
                        getSLAWidget(),
                        // buildOverdue(widget.isOverdue ?? false, context),
                        Expanded(
                          flex: 1,
                          child: Align(
                            alignment: AlignmentDirectional.centerEnd,
                            child: Text(
                              (widget.workModel.createdDate ?? "").toString(),
                              style:TextStyle(
                                color: secondTextColor,
                                fontSize: 12.sp,
                                fontFamily: appFontFamily,
                                fontWeight: FontWeight.w400,
                                height: 1.50,
                                letterSpacing: -0.24,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10.w,
                    ),
                    WorkWidget(
                      workModel: widget.workModel,
                      // serviceData: widget.serviceData,
                    ),
                  ],
                ),
              ),
            );
          }),
    );
  }
  Color getStatusColor() {
    return (widget.workModel.serviceStatusEn ?? '')
        .toLowerCase()
        .contains("assign")
        ? primaryColor
        : (widget.workModel.serviceStatusEn ?? '')
        .toLowerCase()
        .contains("resolve")
        ? alertSuccess
        : workBloc.statusColorFromHex(widget.workModel.statusColor ?? '');
  }
getSLAWidget()=>
    Expanded(
      flex: 3,
      child: SizedBox(
        height: 18.sp,
        child: Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    FontAwesomeIcons.clock,
                    color: secondTextColor,
                    size: 12.sp,
                  ),
                  SizedBox(width: 2.sp),
                  Text(
                   "${AppLocalizations.of(context).translate("SLA")} :",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: secondTextColor,
                      fontSize: 12.sp,
                      fontFamily: appFontFamily,
                      fontWeight: FontWeight.w400,
                      height: 1.50,
                      letterSpacing: -0.24,
                    ),
                  ),
                ],
              ),
              SizedBox(width: 2.sp),
              Text(
                AppLocalizations.appLang == "en"
                    ? (widget.workModel.slaEn ?? "")
                    : (widget.workModel.slaAr ?? ""),
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: secondTextColor,
                  fontSize: 12.sp,
                  fontFamily: appFontFamily,
                  fontWeight: FontWeight.w500,
                  height: 1.50,
                  letterSpacing: -0.24,
                ),
              ),
            ],
        ),
      ),
    );

  Container buildOverdue(bool isOverdue, context) {
    return isOverdue
        ? Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.yellow.withOpacity(.1),
              borderRadius: BorderRadius.circular(100),
            ),
            child: Text(
              AppLocalizations.of(context).translate("Overdue"),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.yellow.shade800,
                fontWeight: FontWeight.bold,
              ),
            ),
          )
        : Container();
  }

  _handleDetailsNavigation() {
    // ServiceDetailsBloc.instance(context).add(LoadDetailsEvent(widget.workModel.mutableContext??''));
    switch (widget.workModel.workType) {
      case WorkType.myTasks:
        Navigation.navigateToScreen(
            context,
            DetailsLayoutRevamp(
              workType: WorkType.taskDetails,
              mutableContext: widget.workModel.mutableContext,
              requestType: widget.workModel.requestType ?? '',
              requestId: widget.workModel.discussionRequestId,
              taskTableName: widget.workModel.tableName ?? '',
              internalDiscussionFlag: widget.workModel.internalDiscussionFlag,
            ));
        break;
      case WorkType.myRequests:
        Navigation.navigateToScreen(
            context,
            DetailsLayoutRevamp(
              workType: WorkType.requestDetails,
              requestType: widget.workModel.requestType ?? '',
              requestId: widget.workModel.discussionRequestId,
              mutableContext: widget.workModel.mutableContext,
              taskTableName: widget.workModel.tableName ?? '',
              internalDiscussionFlag: widget.workModel.internalDiscussionFlag,
            ));
        break;
      default:
        break;
    }
  }
}

enum StatusType {
  pending,
  approved,
  rejected,
  canceled,
  overdue,
  nothing,
}

enum WorkType {
  myRequests,
  myTasks,
  taskDetails,
  requestDetails,
  taskDetailsDialog,
  requestDetailsDialog,
}
