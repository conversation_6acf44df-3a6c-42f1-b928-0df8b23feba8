import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

class WarningMessageWidget extends StatelessWidget {
  final String message;
  final String? iconName;
  final Color? iconColor;
  final Color? containerColor;
  const WarningMessageWidget(
      {super.key, required this.message, this.iconName, this.iconColor, this.containerColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.all(16.w),
      decoration: ShapeDecoration(
        color: Color.fromRGBO(185, 200, 252, 0.1),
        shape: RoundedRectangleBorder(
          side: BorderSide(
            width: 1,
            strokeAlign: BorderSide.strokeAlignCenter,
            color: borderColor,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 25.sp,
            height: 25.sp,
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              color: containerColor ?? Color(0xffe0e0e1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              getIconFromCss(iconName ?? "fa-solid fa-info"),
              size: 14.sp,
              color: iconColor ?? Colors.white,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(message,
                style: FontUtilities.getTextStyle(
                  TextType.textConsumed,
                  size: 12.sp,
                  fontWeight: FontWeight.w500,
                  textColor: mainTextColor,
                )),
          ),
        ],
      ),
    );
  }
}
