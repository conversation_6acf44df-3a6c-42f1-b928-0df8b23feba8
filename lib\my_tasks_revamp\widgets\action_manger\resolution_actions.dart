import 'package:eeh/my_tasks_revamp/widgets/action_manger/actions.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import '../../../shared/utility/navigation_utility.dart';
import '../../../shared/widgets/custom_elevated_button.dart';
import '../../../shared/work_widget/utility_widgets/action_sheets_utils.dart';
import '../../../shared/work_widget/utility_widgets/feeback_actions.dart';
import '../../../shared/work_widget/utility_widgets/withdraw_sheet.dart';
import '../../bloc/my_task_bloc_revamp.dart';
import '../../bloc/tasks_states.dart';

class AcceptResolution extends IAdminAction {
  AcceptResolution(super.actionData, super.context, super.workModel);
  TextEditingController noteController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  int serviceRate = 0;
  bool isValidate = false;
  _feedbackCallback(int index, bool isValid) {
    serviceRate = index;
    isValidate = isValid;
  }

  @override
  mapTaskActionPayload() {
    taskActionPayload.serviceRate = serviceRate.toString();
    taskActionPayload.extraComment =
        noteController.text.isNotEmpty ? noteController.text : "";
    super.mapTaskActionPayload();
    Navigation.popScreen(context);
  }

  bool isAllFieldsValid() {
    return (noteController.text.isNotEmpty || serviceRate > 0);
  }

  @override
  onActionTapped() {
    genericActionsSheet(
      context,
      validationText:
          AppLocalizations.of(context).translate('validation_failed'),
      endContent: [
        SafeArea(
          bottom: false,
          child: StatefulBuilder(builder: (context, setState) {
            return Form(
                key: formKey,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 2.0,right: 16,left: 16,bottom: 8),
                      child: FeedbackActions(
                        feedbackIndexCallback: (int x , bool isValid) {
                          _feedbackCallback(x, isValid);
                          setState(() {});
                        },
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                      ),
                      child: noteTextField(
                        context: context,
                        shouldValidate: isValidate,
                        commentText:
                            AppLocalizations.of(context).translate("comment"),
                        controller: noteController,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        AppLocalizations.of(context).translate('Cancel'),
                        style: TextStyle(
                          color: primaryColor,
                          fontSize: 18.sp,
                        ),
                      ),
                    ),
                    BlocConsumer<TasksBlocRevamp, TasksState>(
                        listener: (context, state) {},
                        builder: (context, state) {
                          return Transform.translate(
                            offset: const Offset(0, 4),
                            child: CustomElevatedButton(
                              fontSize: 18.sp,
                              isLoading: state is UpdateEsmLoadingState,
                              backgroundColor: primaryColor,
                              width: double.infinity,
                              title: AppLocalizations.of(context)
                                  .translate('Submit'),
                              onPressed: state is! UpdateEsmLoadingState
                                  ? () {
                                      if (serviceRate != 0) {
                                        if (formKey.currentState!.validate() ||
                                            serviceRate == 5) {
                                          mapTaskActionPayload();
                                          noteController.clear();
                                        }
                                      } else {
                                        showMessage('Please select your rate',MessageType.error);
                                      }
                                    }
                                  : () {},
                            ),
                          );
                        }),
                  ],
                ));
          }),
        )
      ],
      commentText: AppLocalizations.of(context).translate("comment"),
      isCancelable: false,
      headerTitle: AppLocalizations.of(context).translate("acceptResolution"),
      headerDescription:
          AppLocalizations.of(context).translate("acceptResolutionDescription"),
      headerIcon: FontAwesomeIcons.circleCheck,
      headerIconColor: primaryColor,
    );
  }
}

class RejectResolution extends IAdminAction {
  RejectResolution(super.actionData, super.context, super.workModel);
  TextEditingController noteController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool isAllFieldsValid() {
    return (noteController.text.isNotEmpty);
  }

  @override
  mapTaskActionPayload() {
    taskActionPayload.rejectionreason =
        noteController.text.isNotEmpty ? noteController.text : "";
    super.mapTaskActionPayload();
    Navigation.popScreen(context);
  }

  @override
  onActionTapped() {
    Color actionBtnColor = red.withOpacity(0.4);
    genericActionsSheet(
      context,
      validationText:
          AppLocalizations.of(context).translate('validation_failed'),
      actionBtnColor: rejectionColor,
      isCancelable: false,
      headerTitle: AppLocalizations.of(context).translate("reject_resolution"),
      headerDescription:
          AppLocalizations.of(context).translate("feedback_improvement"),
      endContent: [
        SafeArea(
          bottom: false,
          child: StatefulBuilder(builder: (context, setState) {
            return Form(
                onChanged: () {
                  if (isAllFieldsValid()) {
                    actionBtnColor = red;
                  } else {
                    actionBtnColor = red.withOpacity(.40);
                  }
                  setState(() {});
                },
                key: formKey,
                child: Column(
                  children: [
                    Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          child: noteTextField(
                            context: context,
                            shouldValidate: true,
                            commentText: AppLocalizations.of(context)
                                .translate("comment"),
                            controller: noteController,
                          ),
                        ),
                      ],
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        AppLocalizations.of(context).translate('Cancel'),
                        style: TextStyle(
                          color: primaryColor,
                          fontSize: 18.sp,
                        ),
                      ),
                    ),
                    BlocConsumer<TasksBlocRevamp, TasksState>(
                        listener: (context, state) {},
                        builder: (context, state) {
                          return Transform.translate(
                            offset: const Offset(0, 4),
                            child: CustomElevatedButton(
                              fontSize: 18.sp,
                              isLoading: state is UpdateEsmLoadingState,
                              backgroundColor: actionBtnColor,
                              width: double.infinity,
                              title: AppLocalizations.of(context)
                                  .translate("Reject"),
                              onPressed: state is! UpdateEsmLoadingState
                                  ? () {
                                      if (formKey.currentState!.validate()) {
                                        mapTaskActionPayload();
                                      }
                                    }
                                  : () {},
                            ),
                          );
                        }),
                  ],
                ));
          }),
        )
      ],
      headerIcon: FontAwesomeIcons.circleInfo,
      headerIconColor: rejectionColor,
      onActionPressed: () {
        mapTaskActionPayload();
        noteController.clear();
      },
    );
  }
}
