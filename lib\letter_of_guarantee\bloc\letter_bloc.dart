// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:eeh/letter_of_guarantee/repo/repo.dart';
// import '../../shared/network/api_helper.dart';
// import '../../shared/widgets/toast.dart';
// import '../models/customerInformationRespone.dart';
// import '../models/guaranteeDataResponse.dart';
// import '../models/projectWbsModel.dart';
// import 'letter_event.dart';
// import 'letter_state.dart';
//
// class LetterBloc extends Bloc<LetterEvent, LetterState> {
//   final GuaranteeRepo repo;
//   List<Guaranteetype> guaranteeTypes = [];
//   List<Perioddays> periodDays = [];
//   List<GuarantPercent> percentages = [];
//   List<ProjectWps> projectNames = [];
//   List<Customerinforma> customers = [];
//
//   LetterBloc({required this.repo}) : super(LetterInitialState()) {
//     on<LoadGuaranteeDataEvent>(_loadGuaranteeData);
//     on<LoadProjectNameEvent>(_loadProjectName);
//     on<LoadCustomerNameEvent>(_loadCustomerName);
//   }
//
//   Future<void> _loadGuaranteeData(LoadGuaranteeDataEvent event, Emitter<LetterState> emit) async {
//     await fetchGuaranteeData(emit);
//   }
//
//   Future<void> _loadProjectName(LoadProjectNameEvent event, Emitter<LetterState> emit) async {
//     await fetchProjectName(emit);
//   }
//
//   Future<void> _loadCustomerName(LoadCustomerNameEvent event, Emitter<LetterState> emit) async {
//     await fetchCustomerName(emit);
//   }
//
//   Future<void> fetchGuaranteeData(Emitter<LetterState> emitter) async {
//     emitter(LetterLoadingState());
//     await repo.getGuaranteeDropdownData()
//         .then((response) => onFetchGuaranteeData(response, emitter));
//   }
//
//   void onFetchGuaranteeData(NetworkResponse response, Emitter<LetterState> emitter) {
//     response.maybeWhen(
//       ok: (data) => onFetchGuaranteeDataSuccess(data, emitter),
//       onError: (error) {
//         showMessage(error.toString(), MessageType.error);
//         emitter(LetterErrorState(error: error.toString()));
//       },
//     );
//   }
//
//   void onFetchGuaranteeDataSuccess(dynamic data, Emitter<LetterState> emitter) {
//     final response = GuaranteeDataResponse.fromJson(data);
//     guaranteeTypes = response.guaranteetype ?? [];
//     periodDays = response.perioddays ?? [];
//     percentages = response.guarantPercent ?? [];
//
//     emitter(LetterSuccessState());
//
//   }
//
//   Future<void> fetchProjectName(Emitter<LetterState> emitter) async {
//     emitter(LetterProjectNameLoadingState());
//     await repo.getProjectName()
//         .then((response) => onFetchProjectName(response, emitter));
//   }
//
//   void onFetchProjectName(NetworkResponse response, Emitter<LetterState> emitter) {
//     response.maybeWhen(
//       ok: (data) => onFetchProjectNameSuccess(data, emitter),
//       onError: (error) {
//         showMessage(error.toString(), MessageType.error);
//         emitter(LetterProjectNameErrorState(error: error.toString()));
//       },
//     );
//   }
//
//   void onFetchProjectNameSuccess(dynamic data, Emitter<LetterState> emitter) {
//     final response = ProjectWpsResponse.fromJson(data);
//     projectNames = response.projectWps ?? [];
//     emitter(LetterProjectNameSuccessState());
//   }
//
//   Future<void> fetchCustomerName(Emitter<LetterState> emitter) async {
//     emitter(LetterCustomerNameLoadingState());
//     await repo.getCustomerName()
//         .then((response) => onFetchCustomerName(response, emitter));
//   }
//
//   void onFetchCustomerName(NetworkResponse response, Emitter<LetterState> emitter) {
//     response.maybeWhen(
//       ok: (data) => onFetchCustomerNameSuccess(data, emitter),
//       onError: (error) {
//         showMessage(error.toString(), MessageType.error);
//         emitter(LetterCustomerNameErrorState(error: error.toString()));
//       },
//     );
//   }
//
//   void onFetchCustomerNameSuccess(dynamic data, Emitter<LetterState> emitter) {
//     final response = CustomerName.fromJson(data);
//     customers = response.customerinforma ?? [];
//     emitter(LetterCustomerNameLoadedState());
//   }
// }
//

import 'package:eeh/letter_of_guarantee/models/Lists.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:eeh/letter_of_guarantee/repo/repo.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../../l10n/app_localizations.dart';
import '../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../shared/attachment_uda/model/attachment_request_model.dart';
import '../../shared/network/api_helper.dart';
import '../../shared/widgets/toast.dart';
//import '../models/customerInfoModel.dart';
//import '../models/customerInformationRespone.dart';
import '../models/guaranteeDataResponse.dart';
import '../models/projectWbsModel.dart';
import '../models/submitBodyModel.dart';
import 'letter_event.dart';
import 'letter_state.dart';

import '../../shared/widgets/customer_info_component/model/customer_Info_model.dart';

//AppLocalizations.appLang == 'en' ? "Department" : "الإدارة"

class LetterBloc extends Bloc<LetterEvent, LetterState> {
  LetterDataState _dataState = LetterDataState(periodDays: [], projectWbs: []);
  final GuaranteeRepo repo;
  final TextEditingController costCenterController = TextEditingController();
  final TextEditingController budgetTypeController = TextEditingController(text: AppLocalizations.appLang == 'en' ? departmentList.first.nameEn : departmentList.first.nameAr);
  final TextEditingController guaranteeTypeController = TextEditingController();
  final TextEditingController guaranteePercentageController = TextEditingController();
  final TextEditingController guaranteeValueController = TextEditingController();
  final TextEditingController tenderValueController = TextEditingController();
  final TextEditingController periodController = TextEditingController();
  final DateRangePickerController dateController = DateRangePickerController();
  final TextEditingController projectController = TextEditingController();
   final TextEditingController customerController = TextEditingController();
  final TextEditingController commentController = TextEditingController();
  AttachmentRequestBody attachmentRequestBody =
  AttachmentRequestBody(attachmentUdaId: '', objectId: '');
 // CustomerNameResponse? customerInfoResponse;
 // List<Customerinforma> customerInfoList=[];
 // CustomerName? CcstomerNameResponse;
  SubmitResponse?  submitResponse;
  CustomerInfoResponse? customerInfoResponse;
  CustomerInfo? customerInfo;
  List<CustomerInfo> customerInfoList=[];
  String guaranteeTypeID="";
  String selectedGuaranteePercent="";
  String selectedPeriodID="";
  String selectedProjectID="";
  BudgetTypeModel? _selectedBudgetType = departmentList.first;
  bool isProject = false;
  late BuildContext ctx;

  bool isAllFieldsValid() {
    return  (budgetTypeController.text.isNotEmpty &&
        guaranteeTypeController.text.isNotEmpty  &&
        guaranteePercentageController.text.isNotEmpty &&
        guaranteeValueController.text.isNotEmpty &&
        tenderValueController.text.isNotEmpty  &&
        periodController.text.isNotEmpty &&
        dateController.selectedDate.toString().isNotEmpty &&
        budgetTypeController.text.isNotEmpty
        && (isProject?projectController.text.isNotEmpty :true ));
  }
  LetterBloc(this.repo) : super(LetterInitialState()) {
    on<LetterOfGuaranteeInitialEvent>(
        (LetterOfGuaranteeInitialEvent event, Emitter<LetterState> emit) async {
      emit(LetterInitialState());
    });
    on<LoadGuaranteeDataEvent>(_loadGuaranteeData);
    on<LoadProjectsEvent>(_loadProjects);

    on<ChangeGuaranteeTypeEvent>(
        (ChangeGuaranteeTypeEvent event, Emitter<LetterState> emit) async {
          guaranteeTypeID=event.guaranteeType.id??'';
         guaranteePercentageController.clear();
         guaranteeValueController.clear();
      emit(ChangeGuaranteeTypeState());
    });
    on<ValidateFieldsEvent>(
        (ValidateFieldsEvent event, Emitter<LetterState> emit) async {
      emit(ValidateFieldsState(isAllFieldsValid()));
    });
    on<ChangeGuaranteePercentEvent>(
        (ChangeGuaranteePercentEvent event, Emitter<LetterState> emit) async {
          selectedGuaranteePercent=event.guaranteePercent.id??'';
          _updateGuaranteeController();
      emit(ChangeGuaranteePercentState());
    });
    on<ChangeBudgetTypeEvent>(
        (ChangeBudgetTypeEvent event, Emitter<LetterState> emit) async {
          _selectedBudgetType=event.budgetType;
          isProject =_selectedBudgetType?.id== project;
          if(isProject){
            costCenterController.clear();
          }else{
            projectController.clear();
          }
          emit(ChangeBudgetTypeState());
    });
    on<ChangeGuaranteeValueEvent>(
        (ChangeGuaranteeValueEvent event, Emitter<LetterState> emit) async {
          _updateGuaranteeController();
      emit(ChangeGuaranteeValueState());
    });
    on<SubmitLetterOfGuaranteeEvent>(submitLetterOfGuaranteeRequest);



    on<UploadAttachmentFilesEvent>((event, emit) async {
      emit(UploadAttachmentLoadingState());
      await uploadAttachmentFiles(emit);
    });


    on<FilesUpdatedEvent>((event, emit) async {
      emit(FilesUpdatedState());
    });

  }
  List<GuarantPercent> getFilteredPercentages( List<GuarantPercent>  percentages) {
    return percentages
        .where((percent) => percent.letterType == guaranteeTypeID).toList();
  }
  void _updateGuaranteeController() {
    if(selectedGuaranteePercent.isNotEmpty&&tenderValueController.text.isNotEmpty) {
      final num1 = double.tryParse(tenderValueController.text) ?? 0;
      final num2 =
          double.tryParse(selectedGuaranteePercent.replaceAll('%', '')) ?? 0;
      final result = (num1 * num2) / 100;
      guaranteeValueController.text = result.toStringAsFixed(2);
    }
  }

  Future<void> _loadGuaranteeData(
      LoadGuaranteeDataEvent event, Emitter<LetterState> emit) async {
    emit(GuaranteeDataLoadingState());
    try {
     await repo.getGuaranteeDropdownData().then((response)=>onLoadGuaranteeResponse(response, emit));
    } catch (e) {
      emit(GuaranteeDataErrorState());
    }
  }

  void onLoadGuaranteeResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onLoadGuaranteeSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(GuaranteeDataErrorState());
    });
  }

  void onLoadGuaranteeSuccess(data, Emitter emitter) {
    emitter(GuaranteeDataSuccessState(response: GuaranteeDataResponse.fromJson(data)));
  }

  Future<void> _loadProjects(
      LoadProjectsEvent event, Emitter<LetterState> emit) async {
    emit(ProjectsLoadingState());
    try {
      await repo.getProjectName().then((response)=>onLoadProjectsResponse(response, emit));
    } catch (e) {
      emit(ProjectsErrorState());
    }
  }

  void onLoadProjectsResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onLoadProjectsSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(ProjectsErrorState());
    });
  }

  void onLoadProjectsSuccess(data, Emitter emitter) {
    emitter(ProjectsSuccessState(projectWpsResponse: ProjectWpsResponse.fromJson(data)));
  }
  fetchCustomerList(emitter)async{
    await repo.getCustomerList(searchTerm: ' ', skip: ' ', top: '').then
      ((response)=>onFetchCustomerList(response, emitter));
  }

  void onFetchCustomerList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchCustomerSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(CustomerListErrorState(error: error));

    });
  }

  void onFetchCustomerSuccess(data,emitter){

  }

  Future<void> submitLetterOfGuaranteeRequest(SubmitLetterOfGuaranteeEvent event ,Emitter<LetterState> emit) async {
    emit(SubmitLoadingState());
    final submitBody = LetterOfGuaranteeBody(
        budgetType: _selectedBudgetType?.id ?? '',
        guaranteePercent: selectedGuaranteePercent,
        comments: commentController.text,
        costCenterId: isProject?null:costCenterController.text,
        customerId: customerInfo?.id ?? '',
        guaranteeValue: guaranteeValueController.text,
        guarantTypeId: guaranteeTypeID,
        periodDaysId: selectedPeriodID,
        projectNameId: selectedProjectID,
        startDate: DateFormat('yyyy-MM-dd')
            .format(dateController.selectedDate ?? DateTime.now()),
        tenderValue: tenderValueController.text);

    try {
      await repo
          .getSubmit(submitBodyModel: submitBody)
          .then((response) => onSubmitResponse(response, emit));
    } catch (e) {
      emit(SubmitErrorState());
    }
  }

  void onSubmitResponse(
      NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitResponseSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(ProjectsErrorState());
    });
  }

  void onSubmitResponseSuccess(data, Emitter emitter) {
    submitResponse = SubmitResponse.fromJson(data);
    attachmentRequestBody.objectId = submitResponse!.recId.toString();
    emitter(SubmitSuccessState());
  }

  Future uploadAttachmentFiles(emitter) async {
    emitter(UploadAttachmentLoadingState());
    if (AttachBloc.instance(ctx).files.isNotEmpty) {
      {
        AttachBloc.instance(ctx).add(UploadFilesEvent(
            false,
            submitResponse?.recId,
            submitResponse?.recId,
            AttachBloc.instance(ctx).metaDataResponse?.attachmentId ?? ''));
      }
    }

    emitter(UploadAttachmentFilesSuccessState());

  }
}


