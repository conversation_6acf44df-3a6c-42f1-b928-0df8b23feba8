import 'package:eeh/iqama_services/bloc/iqama_bloc.dart';
import 'package:eeh/iqama_services/bloc/iqama_state.dart';
import 'package:eeh/iqama_services/repo/iqama_repo.dart';
import 'package:eeh/shared/style.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eeh/shared/styles/colors.dart';
import '../../../l10n/app_localizations.dart';
import '../../../shared/utility/font_utility.dart';
import '../../../shared/utility/navigation_utility.dart';
import '../../../shared/widgets/app_bar.dart';
import '../../bloc/iqama_event.dart';
import '../../model/submit_emp_cancellation_family_response.dart';
import '../SuccessScreenIqamaCancellationFamilyContentWidget.dart';
import '../family_bottom_sheet.dart';
import '../iqama_cancellation_family_card_widget.dart';
import '../iqama_text_field.dart';

class IqamaCancellationEmployeeFamilyScreen extends StatefulWidget {
  const IqamaCancellationEmployeeFamilyScreen({super.key});

  @override
  State<IqamaCancellationEmployeeFamilyScreen> createState() =>
      _IqamaCancellationState();
}

class _IqamaCancellationState
    extends State<IqamaCancellationEmployeeFamilyScreen> {
  Color submitBtnColor = primaryColor.withOpacity(0.2);
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late IqamaBloc bloc;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<IqamaBloc>(
          create: (BuildContext context) {
            return IqamaBloc(iqamaRepo: IqamaRepo.instance)
              ..add(IqamaInitialEvent("15"));
          },
        )
      ],
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: getAppBarWidget(),
        body: BlocConsumer<IqamaBloc, IqamaState>(
          listenWhen: (previous, current) => current is IqamaInitialFinishState,
          listener: (context, state) {
            bloc.add(FetchIqamaPrintingEvent());
            bloc.add(FetchEmployeeDependentByIdEvent());
          },
          buildWhen: (previous, current) =>
              current is FetchEmployeeDependentByIdSuccessState ||
              current is FetchEmployeeDependentByIdErrorState ,
          builder: (context, state) {
            bloc = context.read<IqamaBloc>();
            bloc.ctx = context;
            if (state is FetchEmployeeDependentByIdSuccessState) {
              return getIqamaCancellationBody();
            } else if (state is FetchEmployeeDependentByIdErrorState) {
              return SizedBox.shrink();
            } else {
              return Center(
                child: const CircularProgressIndicator(),
              );
            }
          },
        ),
      ),
    );
  }

  getIqamaCancellationBody() {
    return Column(
      children: [
        // getServicesFlowNamesWidget(),
        Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 17.w),
            child: Form(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 8.h,
                  ),
                  IqamaTextField(
                    showBottomSheet: () => getBottomSheet(context),
                    controller: bloc.employeeID,
                    haveBottomSheet: true,
                    isReadOnly: true,
                    title:
                        AppLocalizations.of(context).translate("Family Member"),
                    suffixWidget: Icon(
                      Icons.keyboard_arrow_down,
                    ),
                  ),
                  SizedBox(
                    height: 18.h,
                  ),
                  iqama_cancellation_family_card_widget(bloc: bloc),
                  SizedBox(
                    height: 18.h,
                  ),
                ],
              ),
            ),
          ),
        ),
        getButtons(context)
      ],
    );
  }

  getAppBarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: BlocBuilder<IqamaBloc, IqamaState>(
          buildWhen: (previous, current) =>
              current is IqamaInitialFinishState ||
              current is ChangeFavoriteStatusState ||
              current is ChangeFavoriteStatusLoadingState,
          builder: (context, state) {
            return CustomAppBar(
              title: AppLocalizations.of(context)
                  .translate("Cancel Iqama -  Family"),
              appbar: bloc.isFavoriteService
                  ? AppbarType.favourableScreen
                  : AppbarType.unFavourableScreen,
              onActionIconPressed: state is ChangeFavoriteStatusLoadingState
                  ? null
                  : () {
                      bloc.add(FavoriteServiceValueChangeEvent());
                    },
            );
          }),
    );
  }

  Widget getServicesFlowNamesWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 17.w),
      child: Column(
        children: [
          SizedBox(
            height: 21.h,
          ),
          Row(
            children: [
              Text(
                AppLocalizations.of(context).translate('Government Relations'),
                style: FontUtility.getTextStyleForText(TextType.medium,
                    textColor: textColor, isBold: true, size: 12),
              ),
              SizedBox(
                width: 11.w,
              ),
              Padding(
                padding: EdgeInsets.only(top: 2.h),
                child: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 12.sp,
                ),
              ),
              SizedBox(
                width: 11.w,
              ),
            ],
          ),
          SizedBox(
            height: 12.h,
          ),
          Row(
            children: [
              Text(
                AppLocalizations.of(context).translate(
                    "Iqama Cancelation for employee’s family Request"),
                style: FontUtility.getTextStyleForText(TextType.medium,
                    textColor: primaryColor, isBold: true, size: 12),
              ),
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
        ],
      ),
    );
  }

  Column getButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: () => Navigation.popScreen(context),
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(0.0),
              ),
            ),
            child: Text(AppLocalizations.of(context).translate('Cancel'),
               // style:FontUtilities.getTextStyle(TextType.regular,fontWeight: FontWeight.w400, size: 18.sp),
                style: FontUtility.getTextStyleForText(TextType.medium,
                    textColor: const Color(0xffCB2027), size: 18)),
          ),
        ),
        BlocConsumer<IqamaBloc, IqamaState>(
            listener: (context, state) => Navigation.navigateToScreen(
                context,
                SuccessScreen(
                  contentWidget: SuccessScreenIqamaCancellationContentWidget(
                    submitResponse:
                        bloc.submitIqamaEmpCancellationFamilyResponse ??
                            SubmitIqamaEmpCancellationFamilyResponse(),
                    familyNames: bloc.getSelectedFamilyFullNames(),
                  ),
                  title: AppLocalizations.of(context)
                      .translate("Request Submitted Successfully"),
                )),
            listenWhen: (previous, current) => current is SubmitSuccessState,
            buildWhen: (previous, current) =>
                current is SubmitLoadingState ||
                current is SubmitSuccessState ||
                current is SubmitErrorState,
            builder: (context, state) => InkWell(
                onTap: () => state is! SubmitLoadingState &&
                        bloc.isCancellationFamilyButtonEnable()
                    ? bloc.add(SubmitCancellationEmployeeFamilyRequestEvent())
                    : null,
                child: BlocBuilder<IqamaBloc, IqamaState>(
                  bloc: bloc,
                  buildWhen: (previous, current) =>
                      current is SubmitLoadingState ||
                      current is SubmitSuccessState ||
                      current is SubmitErrorState ||
                      current is UpdateFamilyMembersValueState,
                  builder: (BuildContext context, IqamaState state) =>
                      Column(
                        children: [
                          Container(
                            height: 48.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(0),
                              color: bloc.isCancellationFamilyButtonEnable()
                                  ? primaryColor
                                  : primaryColor.withOpacity(0.2),
                            ),
                            child: Center(
                              child: state is SubmitLoadingState
                                  ? const CircularProgressIndicator()
                                  : Text(
                                AppLocalizations.of(context)
                                    .translate('Submit'),
                                style:FontUtilities.getTextStyle(TextType.regular,fontWeight: FontWeight.w500, size: 18.sp),
                               // style: submitBtnStyle,
                              ),
                            ),
                          ),
                          Container(
                            height: 1,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(0),
                              color: Colors.white,
                            ),
                          ),
                          Container(
                              height: 22.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(0),
                                color: bloc.isCancellationFamilyButtonEnable()
                                    ? primaryColor
                                    : primaryColor.withOpacity(0.2),
                              ))
                        ],
                      ),
                ))),
      ],
    );
  }

  Future<void> getBottomSheet(BuildContext context) async {
    bloc.isSearchMode = false;
    FocusScope.of(context).unfocus();
    await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        showDragHandle: true,
        backgroundColor: Colors.white,
        builder: (context) {
          return BlocBuilder<IqamaBloc, IqamaState>(
            bloc: bloc,
            buildWhen: (previous, current) =>
                current is SearchFamilyTextFildChangedState,
            builder: (BuildContext context, IqamaState state) =>
                IqamqFamilyBottomSheetWidget(
              bloc: bloc,
              onTextFieldChange: (key) {
                bloc.add(SearchFamilyTextFildChangedEvent(key));
              },
              onItemSelect: (item) {},
            ),
          );
        });
  }
}
