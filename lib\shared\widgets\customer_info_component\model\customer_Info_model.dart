
import '../../general_text_form_field.dart';

class CustomerInfoResponse {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final String employeeId;
  final List<CustomerInfo> customerInforma;
  final String? searchTerm;
  final String? skip;
  final String? top;
  final String authKey;


  CustomerInfoResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.employeeId,
    required this.customerInforma,
    this.searchTerm,
    this.skip,
    this.top,
    required this.authKey,
  });

  factory CustomerInfoResponse.fromJson(Map<String, dynamic> json) {
    return CustomerInfoResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      employeeId: json['employeeid'],
      customerInforma: (json['customerinforma'] as List)
          .map((e) => CustomerInfo.fromJson(e))
          .toList(),
      searchTerm: json['search_term'],
      skip: json['skip'],
      top: json['top'],
      authKey: json['authkey'],
    );
  }

  Map<String, dynamic> toJson() => {
    'createdBy': createdBy,
    'createdById': createdById,
    'createdDate': createdDate,
    'recId': recId,
    'employeeid': employeeId,
    'customerinforma': customerInforma.map((e) => e.toJson()).toList(),
    'search_term': searchTerm,
    'skip': skip,
    'top': top,
    'authkey': authKey,
  };
}

class CustomerInfo extends GeneralSheetContent{
  final String nameAr;
  final String moiId;
  final String id;
  final String nameEn;
  final int recId;

  CustomerInfo({
    required this.nameAr,
    required this.moiId,
    required this.id,
    required this.nameEn,
    required this.recId,
  }){
    nameen = nameEn;
    namear = nameAr.isNotEmpty? nameAr:nameEn;
    extraContent=[];
    extraContent?.add(SheetExtraContent(keyEn:'Name',keyAR: 'الاسم',valueAR:nameAr ,valueEn:nameEn ));
    extraContent?.add(SheetExtraContent(keyEn:'ID',keyAR: 'الرقم التعريفي' ,valueAR:id ,valueEn:id ));

  }

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      nameAr: json['namear'] ?? '',
      moiId: json['moiid'] ?? '',
      id: json['id'] ?? '',
      nameEn: json['nameen']?? '',
      recId: json['recid'] ??'',
    );
  }

  Map<String, dynamic> toJson() => {
    'namear': nameAr,
    'moiid': moiId,
    'id': id,
    'nameen': nameEn,
    'recid': recId,
  };
}
