import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../passport/presentation/widgets/submit_button.dart';
import '../../../shared/widgets/cancel_button_component.dart';
import '../../../shared/widgets/submit_button_component.dart';

class ReminderAlert extends StatelessWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onConfirm;

  const ReminderAlert({
    Key? key,
    this.onCancel,
    this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(40.r),
          topRight: Radius.circular(40.r),
        ),
      ),
      child: <PERSON>umn(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top handle indicator
          getHeadDivider(),

          SizedBox(height: 24.h),
          getAlertIcon(),

          SizedBox(height: 24.h),

          // Title
          Text(
            'Are you sure you want to send this reminder?',
            style: FontUtilities.getTextStyle(
              TextType.regular,
              size: 16.sp,
              fontWeight: FontWeight.w500,
              textColor: textMain,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 16),

          // Description
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16).w,
            child: Text(
              'This will notify all team members with overdue courses or quizzes to complete their pending learning.',
              style: FontUtilities.getTextStyle(
                TextType.regular,
                size: 14.sp,
                fontWeight: FontWeight.w400,
                textColor: secondTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          SizedBox(height: 32),

          // Buttons
          getButtons(context),
        ],
      ),
    );
  }

  Container getAlertIcon() {
    return Container(
      padding: EdgeInsets.all(16).w,
      decoration: BoxDecoration(
        color: iconRedColor,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.notifications,
        color: Colors.white,
        size: 28.sp,
      ),
    );
  }

  Container getHeadDivider() {
    return Container(
      margin: EdgeInsets.only(top: 12),
      width: 55.w,
      height: 4,
      decoration: BoxDecoration(
        color: grey,
      ),
    );
  }

  Column getButtons(BuildContext context) {
    return Column(
      children: [
        // Cancel Button
        CancelButtonComponent(
          onPressed: onCancel ?? () => Navigator.of(context).pop(),
          title: 'Cancel',
        ),
        SizedBox(height: 12),
        // Confirm & Send Button
        SubmitButtonComponent(
          onPressed: onConfirm ??
              () {
                Navigator.of(context).pop();
              },
          isLoading: false,
          backGroung: primaryColor,
          text: 'Confirm & Send',
        ),
      ],
    );
  }

  // Static method to show the bottom sheet
  static Future<void> show(
    BuildContext context, {
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return ReminderAlert(
          onCancel: onCancel,
          onConfirm: onConfirm,
        );
      },
    );
  }
}

// Example usage method
void showReminderAlert(BuildContext context) {
  ReminderAlert.show(
    context,
    onCancel: () {
      print('Reminder cancelled');
      Navigator.of(context).pop();
    },
    onConfirm: () {
      print('Reminder sent');
      Navigator.of(context).pop();
      // Add your send reminder logic here
    },
  );
}
