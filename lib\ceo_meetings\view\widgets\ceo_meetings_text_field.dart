import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CEOMeetingsCustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final Function? showBottomSheetOnTap;
  final Function? onChange;
  final Function? validator;
  final bool isReadOnly;
  final bool haveBottomSheet;
  final String title;
  final bool isRequired;
  final bool? isDemmed;
  final bool? isLoading;
  final List<TextInputFormatter>? textInputFormatter;
  final TextInputType? textInputType;
  final Widget? suffixWidget;

  const CEOMeetingsCustomTextField({
    super.key,
    required this.controller,
    required this.isReadOnly,
    required this.title,
    required this.isRequired,
    this.isDemmed,
    this.isLoading,
    this.showBottomSheetOnTap,
    this.onChange,
    this.validator,
    this.haveBottomSheet = false,
    this.textInputFormatter,
    this.textInputType,
    this.suffixWidget,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      inputFormatters: textInputFormatter,
      onChanged: (e) => onChange != null ? onChange!(e) : null,
      validator: (e) => validator != null ? validator!() : null,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      controller: controller,
      keyboardType: textInputType ?? TextInputType.text,
      readOnly: isReadOnly,
      style: const TextStyle(
          fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      decoration: InputDecoration(
        floatingLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 15,
            color: secondTextColor),
        labelStyle: TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 15,
            color:
                isDemmed ?? false ? const Color(0xff939393) : secondTextColor),
        label: Text.rich(TextSpan(children: <TextSpan>[
          TextSpan(
            text: title,
          ),
          isRequired
              ? TextSpan(
                  text: ' *',
                  style: FontUtility.getTextStyleForText(TextType.regular,
                      textColor: isDemmed ?? false
                          ? const Color(0xff939393)
                          : rejectedColor,
                      size: 15))
              : const TextSpan(),
        ])),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color.fromRGBO(185, 200, 252, 0.5),
          ),
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color.fromRGBO(185, 200, 252, 0.5),
          ),
        ),
        suffixIconConstraints:
                BoxConstraints(minWidth: 24.w, minHeight: 24.w),
        suffixIcon: haveBottomSheet
            ? suffixWidget ?? Icon(Icons.keyboard_arrow_down_sharp)
            : null,
        alignLabelWithHint: true,
        contentPadding: EdgeInsets.only(bottom: 7.w),
      ),
      textAlignVertical: TextAlignVertical.bottom,
      onTap: () async => (haveBottomSheet && showBottomSheetOnTap != null)
          ? await showBottomSheetOnTap!()
          : null,
    );
  }
}
