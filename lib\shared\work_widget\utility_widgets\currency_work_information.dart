import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/sar_component.dart';
import 'package:eeh/shared/work_widget/utility_widgets/utility_methods.dart';
import 'package:eeh/shared/work_widget/work_item_layout.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../l10n/app_localizations.dart';

class CurrencyWorkInfoText extends StatelessWidget {
  final String titleEN;
  final String dataEN;
  final String titleAR;
  final String dataAR;
  final bool isDataBold;
  final bool isTitleBold;
  final bool isSARHasBrackets;
  final Color titleColor;
  final Color dataColor;
  final Color? sarColor;
  final double? sarSize;
  final double? sarOpacity;
  WorkType? workType;

  CurrencyWorkInfoText({
    super.key,
    this.workType = WorkType.myTasks,
    required this.titleEN,
    required this.dataEN,
    required this.titleAR,
    required this.dataAR,
    this.isDataBold = false,
    this.isTitleBold = false,
    this.isSARHasBrackets = false,
    this.titleColor = secondTextColor,
    this.dataColor = secondTextColor,
    this.sarColor,
    this.sarSize,
    this.sarOpacity,
  });
  @override
  Widget build(BuildContext context) {
    final bool isEnglishLang = AppLocalizations.appLang == 'en';
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.sp),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Text(
            "${isEnglishLang ? titleEN : titleAR} : ",
            style: TextStyle(
              fontSize: getMediumFontSize(workType),
              fontFamily: appFontFamily,
              color: titleColor,
              fontWeight: isTitleBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Flexible(
                  child: Text(
                    "${isEnglishLang ? dataEN : dataAR} ",
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    style: TextStyle(
                      fontSize: getMediumFontSize(workType),
                      fontFamily: appFontFamily,
                      color: dataColor,
                      fontWeight:
                          isDataBold ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                ),
                SARComponent(
                  color: sarColor,
                  hasBrackets: isSARHasBrackets,
                  size: sarSize ?? getMediumFontSize(workType) - 2.sp,
                  opacity: sarOpacity ?? 1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
