abstract class TenderRequestState{}


class TenderRequestInitialState extends TenderRequestState{}

class PaymentTypeListLoadingState extends TenderRequestState {}

class PaymentTypeListSuccessState extends TenderRequestState {}

class PaymentTypeListErrorState extends TenderRequestState {}


class ProjectWpsListLoadingState extends TenderRequestState {}

class ProjectWpsListSuccessState extends TenderRequestState {}

class ProjectWpsListErrorState extends TenderRequestState {
  final String message;
  ProjectWpsListErrorState({required this.message});
}

class BankListLoadingState extends TenderRequestState {}

class BankListSuccessState extends TenderRequestState {}

class BankListErrorState extends TenderRequestState {}


class CustomerListLoadingState extends TenderRequestState {}

class CustomerListSuccessState extends TenderRequestState {}

class CustomerListErrorState extends TenderRequestState {}


class UploadAttachmentLoadingState extends TenderRequestState{}

class UploadAttachmentFilesSuccessState extends TenderRequestState{}

class UploadAttachmentFilesErrorState extends TenderRequestState{}

class FilesUpdatedState extends TenderRequestState{}

class SubmitLoadingState extends TenderRequestState {}

class SubmitSuccessState extends TenderRequestState{}

class SubmitErrorState extends TenderRequestState{}

class ChangeBudgetTypeState extends TenderRequestState{}

class ChangePaymentTypeState extends TenderRequestState{}


class ValidateFieldsState extends TenderRequestState {
  final bool isValid;
  ValidateFieldsState(this.isValid);
}