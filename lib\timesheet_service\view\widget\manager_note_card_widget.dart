import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../shared/styles/colors.dart';
import 'header_card_my_time_sheet.dart';

class TimeSheetManagerNoteWidget extends StatelessWidget {
  final String headerTitle;
  final String headerIconName;
  final Color? headerIconColor;
  final Color? headerTitleColor;
  final String bodyText;
  const TimeSheetManagerNoteWidget({
    required this.headerTitle,
    required this.headerIconName,
    this.headerIconColor,
    this.headerTitleColor,
    required this.bodyText,
    super.key,
  });
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(
          side: BorderSide(width: 1, color: borderCardColor),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HeaderCardMyTimeSheet(
                title: headerTitle,
                image: headerIconName,
                iconColor: headerIconColor ?? primaryColor,
                titleColor: headerTitleColor ?? secondTextColor,
              ),
              SizedBox(height: 8.h),
              Text(bodyText,
                  style: FontUtilities.getTextStyle(
                    TextType.textConsumed,
                    size: 12.sp,
                    fontWeight: FontWeight.w500,
                    textColor: secondTextColor,
                  ))
            ],
          ),
        ],
      ),
    );
  }
}
