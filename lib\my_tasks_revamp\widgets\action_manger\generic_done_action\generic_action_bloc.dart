import 'package:eeh/admin_service/utils/field_type_constant.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../shared/network/api_helper.dart';
import 'generic_action_events.dart';
import 'generic_action_repo.dart';
import 'generic_action_states.dart';
import 'generic_popup_metaData_model.dart';

class GenericActionBloc extends Bloc<GenericActionEvent, GenericActionState> {
  GenericActionRepo repo =GenericActionRepo();
  String? serviceKey;
  int? recId;
  bool? shouldCallSubmit;
  DateTime? selectedDate;
  TimeOfDay? time1;
  TimeOfDay? time2;
  Map<String, dynamic> submitBody = {};
  bool? switchValue = false;
  bool? isSwitchMandatory = false;
  String? label;

  GenericActionBloc()
      : super(GenericActionInitState()) {
    on<GenericActionInitEvent>(_onInit);
    on<GenericActionContentEvent>(_onLoadGenericActionContent);
    on<SubmitGenericActionEvent>(_onSubmitGenericAction);
    on<UpdateInputFieldsEvent>(_onUpdateInputField);

     on<ChangeSwitchEvent>(
      (event, emit) {
        updateSwitchData(event.item, event.value);
        emit(ChangeSwitchState());
      },
    );
  }

  _onInit(GenericActionInitEvent event, Emitter<GenericActionState> emitter){
    emitter(GenericActionInitState());
  }

  _onUpdateInputField(
      UpdateInputFieldsEvent event, Emitter<GenericActionState> emitter) async{
    submitBody[event.serviceFieldItem?.parameterName ?? ''] = event.value;
  }

  _onSubmitGenericAction(
      SubmitGenericActionEvent event, Emitter<GenericActionState> emitter) async{
    emitter(SubmitGenericActionLoadingState());
   if(shouldCallSubmit??false) {
      try {
        submitBody["recId"] = int.tryParse(event.requestID);
        await repo.submitGenericAction(submitBody, serviceKey ?? '').then(
            (response) => onSubmitGenericActionResponse(response, emitter)).whenComplete((){

        });
      } catch (e) {
        emitter(SubmitGenericActionErrorState());
      }
    }else {
      emitter(SubmitGenericActionDoneState());
    }
  }

  onSubmitGenericActionResponse(NetworkResponse response, Emitter emitter){
      response.maybeWhen(ok: (data){
        onSubmitGenericActionSuccess(data,emitter);
      }, onError: (error){
        showMessage(error.toString(), MessageType.error);
        emitter(SubmitGenericActionErrorState());
      });
  }
  onSubmitGenericActionSuccess(data, Emitter emitter) {
    emitter(SubmitGenericActionSuccessState());

  }

  _onLoadGenericActionContent(
      GenericActionContentEvent event, Emitter<GenericActionState> emitter) async{
    emitter(GenericActionContentLoadingState());

    try {
      await repo.loadPopupContent(event.actionRoute, event.mutableContext).then(
          (response) => onLoadGenericActionContentResponse(response, emitter));
    } catch (e) {
      emitter(GenericActionContentErrorState());
    }
  }
  onLoadGenericActionContentResponse(NetworkResponse response, Emitter emitter){
      response.maybeWhen(ok: (data){
        onLoadGenericActionContentSuccess(data,emitter);
      }, onError: (error){
        showMessage(error.toString(), MessageType.error);
        emitter(GenericActionContentErrorState());
      });
  }
  onLoadGenericActionContentSuccess(data, Emitter emitter) {
    final GenericActionMetaData genericActionMetaData= GenericActionMetaData.fromJson(data);
    emitter(GenericActionContentSuccessState(genericActionMetaData:genericActionMetaData));
    recId= (genericActionMetaData.recId??-1);
    shouldCallSubmit= (genericActionMetaData.submitAPI??false);
    serviceKey= (genericActionMetaData.srvsId??'');
        genericActionMetaData.actionMetadata?.popupMetaData
        ?.forEach((element) {
      if (element.filedType == FieldType.disclaimer) {
        switchValue = element.defaultValue == 'true' ? true : false;
        isSwitchMandatory = element.mandatory ?? false;
      } else if(element.filedType == FieldType.time && 
      (element.validationObject?.complexValidation?[0].compareInput == null|| element.validationObject==null)){
        label = AppLocalizations.appLang == 'en' ? element.labelEn ??'' : element.labelAr ??'';
      }
    });

  }
  void updateDropDownBlocData(item, selectedValue) {
    submitBody[item?.parameterName ?? ''] = selectedValue.id;
  }

  void updateFieldBlocData(item, selectedValue) {
    submitBody[item?.parameterName ?? ''] = selectedValue;
  }

   void updateSwitchData(item, v) {
    if (item?.filedType == FieldType.disclaimer) {
      switchValue = v;
      submitBody[item?.parameterName ?? ''] = v;
    } 
  }

  DateTime currentDate = DateTime(
    DateTime.now().year,
    DateTime.now().month,
    DateTime.now().day,
  );

  dateValidation(item, value, context) {
    if ((value == null || value.trim().isEmpty) && item?.mandatory == true) {
      return AppLocalizations.of(context).translate("Please select date");
    }
    if (value != null) {
      selectedDate = DateTime.parse(value);
      if (
        // (item?.validationObject?.allowFutureDate != null &&
        //       item?.validationObject?.allowFutureDate == false) ||
          (item?.validationObject?.allowOldDate != null &&
              item?.validationObject?.allowOldDate == false)) {
        // if (selectedDate!.isAfter(currentDate)) {
        //   return AppLocalizations.of(context)
        //       .translate('Future date is not allowed');
        // } else 
        if (selectedDate!.isBefore(currentDate)) {
          return AppLocalizations.of(context)
              .translate('Old date is not allowed');
        }
      }
    }
  }


  timeValidation(item, value, context) {
    TimeOfDay currentTime = TimeOfDay(
      hour: TimeOfDay.now().hour,
      minute: TimeOfDay.now().minute,
    );
    selectedDate == null
        ? selectedDate = DateTime(
            DateTime.now().year,
            DateTime.now().month,
            DateTime.now().day,
          )
        : selectedDate = selectedDate;
    String date = DateFormat('yyyy-MM-dd').format(selectedDate!);
    if ((value == null || value.trim().isEmpty) && item?.mandatory == true) {
      return AppLocalizations.of(context).translate("Please select time");
    }
    if (item?.validationObject?.complexValidation[0].compareInput != null) {
      if (item?.parameterName !=
          item?.validationObject?.complexValidation[0].compareInput) {
        time1 = TimeOfDay.fromDateTime(DateTime.parse("$date $value"));
        time2 = TimeOfDay.fromDateTime(DateTime.parse(
            "$date ${submitBody[item?.validationObject?.complexValidation[0].compareInput] ?? '00:00'}"));
        bool result = compareTime(
            item?.validationObject?.complexValidation[0].operator ?? '>',
            time1!,
            time2!);
        if (result == false) {
          return getTimeValidationMessage(
              item?.validationObject?.complexValidation[0].operator ?? '>',label??'',context);
        }
      }
    } 
      TimeOfDay time = TimeOfDay.fromDateTime(DateTime.parse("$date $value"));
      if ((
        // item?.validationObject?.allowFutureTime != null &&
        //       item?.validationObject?.allowFutureTime == false ||
          (item?.validationObject?.allowOldTime != null &&
              item?.validationObject?.allowOldTime == false))) {
        // if ((time.isAfter(currentTime) &&
        //         !selectedDate!.isBefore(currentDate)) ||
        //     (time.isBefore(currentTime) &&
        //         selectedDate!.isAfter(currentDate))) {
        //   return AppLocalizations.of(context)
        //       .translate('Future time is not allowed');
        // } else
         if ((time.isBefore(currentTime) &&
                !selectedDate!.isAfter(currentDate)) ||
            (time.isAfter(currentTime) &&
                selectedDate!.isBefore(currentDate))) {
          return AppLocalizations.of(context)
              .translate('Old time is not allowed');
        }
      }else{
         if ((time.isBefore(currentTime) &&
                !selectedDate!.isAfter(currentDate)) ||
            (time.isAfter(currentTime) &&
                selectedDate!.isBefore(currentDate))) {
          return AppLocalizations.of(context)
              .translate('Old time is not allowed');
      }}
    // }
  }

  compareTime(String operator, TimeOfDay time1, TimeOfDay time2) {
    switch (operator) {
      case '>':
        return time1.isAfter(time2);
      case '<':
        return time1.isBefore(time2);
      case '>=':
        return time1.isAfter(time2) || time1.isAtSameTimeAs(time2);
      case '<=':
        return time1.isBefore(time2) || time1.isAtSameTimeAs(time2);
      case '==':
        return time1.isAtSameTimeAs(time2);
      case '!=':
        return !time1.isAtSameTimeAs(time2);
      default:
        return false;
    }
  }
 
 getTimeValidationMessage(String operator,String label,context){
    switch (operator) {
       case '>':
        return '${AppLocalizations.of(context)
              .translate('must be greater than')} $label';
      case '<':
        return '${AppLocalizations.of(context)
              .translate('must be less than')} $label';
      case '>=':
        return '${AppLocalizations.of(context)
              .translate('must be greater than or equal to')} $label';
      case '<=':
        return '${AppLocalizations.of(context)
              .translate('must be less than or equal to')} $label';
      case '==':
        return '${AppLocalizations.of(context)
              .translate('must be equal to')} $label';
      case '!=':
        return '${AppLocalizations.of(context)
              .translate('must be not equal to')} $label';
      default:
        return false;
    }
 }

}