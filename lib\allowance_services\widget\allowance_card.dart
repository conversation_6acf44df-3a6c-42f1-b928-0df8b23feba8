import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

enum ActionType { edit, delete, check }

class AllowanceCard extends StatefulWidget {
  final ActionType actionType;
  final String employeeEn;
  final String employeeAr;
  final String targetAmount;
  final String numOfMonths;
  final String startDate;
  final String endDate;
  final String comment;
  final Function()? onActionTap;
  final String? extendedMonths;
  final bool? isCheck;
  final Function(bool?)? onCheckBoxChanged;

  const AllowanceCard({
    super.key,
    required this.actionType,
    required this.employeeEn,
    required this.employeeAr,
    required this.targetAmount,
    required this.numOfMonths,
    required this.startDate,
    required this.endDate,
    required this.comment,
    this.onActionTap,
    this.extendedMonths,
    this.isCheck,
    this.onCheckBoxChanged,
  });

  @override
  State<AllowanceCard> createState() => _AllowanceCardState();
}

class _AllowanceCardState extends State<AllowanceCard> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.sp),
      child: Container(
          padding: EdgeInsets.all(10.sp),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: borderLightColor, width: 1.sp),
          ),
          child: Column(children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.actionType == ActionType.check ? '' : AppLocalizations.of(context).translate('Action'),
                  style: FontUtilities.getTextStyle(
                    TextType.medium,
                  ),
                ),
                Transform.translate(
                  offset: AppLocalizations.appLang == 'en' ? Offset(12, 0) :Offset(-12, 0),
                  child: IconButton(
                    onPressed: widget.onActionTap,
                    icon:getActionIcon(widget.actionType, widget.isCheck ?? false),
                    color: widget.actionType == ActionType.check
                        ? primaryColor
                        : darkPrimary,
                    iconSize: 15.sp,
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(),    
                  ),
                )
              ],
            ),
            cardItem('Beneficial Employee',AppLocalizations.appLang == 'en'
                        ? widget.employeeEn
                        : widget.employeeAr),
            cardItem('Number Of Months', widget.numOfMonths),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  spacing: 5.w,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Eligible Amount'),
                      style: FontUtilities.getTextStyle(
                        TextType.medium,
                      ),
                    ),
                    Icon(
                      getIconFromCss("fa-kit fa-riyal"),
                      color: mainTextColor,
                      size: 14.sp,
                    )
                  ],
                ),
                Text(
                  widget.targetAmount,
                  style:
                      FontUtilities.getTextStyle(TextType.regular, size: 12.sp),
                ),
              ],
            ),
            cardItem('Start Date', widget.startDate),
            cardItem('End Date', widget.endDate),
            cardItem('comment', widget.comment),
            Visibility(
                visible: widget.extendedMonths?.trim().isNotEmpty ?? false,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppLocalizations.of(context).translate('Extended Months'),
                      style: FontUtilities.getTextStyle(
                        TextType.medium,
                      ),
                    ),
                    Text(
                      '${widget.extendedMonths ??'3Months'} ${AppLocalizations.of(context).translate('extend request')}',
                      style: FontUtilities.getTextStyle(TextType.regular,
                          size: 12.sp),
                    ),
                  ],
                )),
          ])),
    );
  }

  getActionIcon(ActionType actionType, bool isChecked) {
    switch (actionType) {
      case ActionType.edit:
        return Icon(getIconFromCss("fa-light fa-pen-to-square"));
      case ActionType.delete:
        return Icon(getIconFromCss("fa-light fa-trash"));
      case ActionType.check:
        return Checkbox(
          checkColor: primaryColor,
          fillColor: WidgetStateProperty.resolveWith(
            (states) => Colors.transparent,
          ),
          side: WidgetStateBorderSide.resolveWith(
            (states) => states.contains(WidgetState.selected)
                ? BorderSide(
                    width: 1.8,
                    color: primaryColor,
                    strokeAlign: 0.1,
                  )
                : BorderSide(width: 2.0, color: Colors.black),
          ),
          value: isChecked,
          onChanged: (newValue) {
            widget.onCheckBoxChanged?.call(newValue);
          },
        );
    }
  }

  Widget cardItem(String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).translate(title),
          style: FontUtilities.getTextStyle(
            TextType.medium,
          ),
        ),
        SizedBox(
          width: 160.w,
          child: Text(
            value,
            style: FontUtilities.getTextStyle(TextType.regular, size: 12.sp),
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
            maxLines:getMaxLines(value),
          ),
        ),
      ],
    );
  }

  int getMaxLines(String text, {int maxLineLength = 30}) {
  if (text.isEmpty) return 1;
  return (text.length / maxLineLength).ceil();
}
}
