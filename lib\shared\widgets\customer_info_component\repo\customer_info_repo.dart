
import '../../../network/api_helper.dart';
import '../../../network/api_utilities.dart';
import '../../../network/endpoints.dart';
import '../../../utility/secure_storage.dart';

abstract class ICustomerInfoRepo{

  Future<NetworkResponse> getCustomerList({
    required String searchTerm,
    required String skip,
    required String top,
    appKey
  });


}


class  CustomerInfoRepo implements ICustomerInfoRepo{
  @override
  Future<NetworkResponse> getCustomerList({required String searchTerm,
    required String skip,
    required String top,appKey}) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'search_term': searchTerm,
          'skip': skip,
          'top': top},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "CustomerInformation Form",
          "moduleName": "CustomerInformation",
          "appKey": "${appKey}",
          "Content-Type": "application/json",
        });

    return response;

  }

}