import 'dart:convert';

import '../../../../admin_service/Model/service details models/generic_service_details_model.dart';
const map={"mobile_style_enabler" : "{\"icon_color\":\"#f8914f\", \"icon_class\":\"fa-solid fa-check\"}"};

class GenericActionMetaData {
  String? attachmentKey ;
  String? serviceName ;
  String? actionButton;
  String? srvsId;
  int ?recId;
  bool? submitAPI;
  PopupMetadata? actionMetadata;


  GenericActionMetaData({
    this.actionMetadata,
    this.serviceName,
    this.srvsId,
    this.attachmentKey,
    this.submitAPI,
    this.recId,
    this.actionButton,
  });

  factory GenericActionMetaData.fromJson(Map<String, dynamic> json) {
    return GenericActionMetaData(
      attachmentKey: json['attachment_key'],
      srvsId: json['srvs_id'],
      recId: json['recId'],
      serviceName: json['service_name'],
      submitAPI: bool.tryParse(json['submit_api'])??false ,
      actionButton: json['action_button'],
      actionMetadata: json['popup_metadata'] != null
          ? PopupMetadata.fromJson(jsonDecode(json['popup_metadata']))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['popup_metadata'] = actionMetadata;
    return data;
  }
}

class PopupMetadata {
  List<ServiceField>? popupMetaData;

  PopupMetadata({this.popupMetaData});

  factory PopupMetadata.fromJson(Map<String, dynamic> json) {
    return PopupMetadata(
      popupMetaData: (json['Task_Popup_Metadata'] as List<dynamic>?)
          ?.map((e) => ServiceField.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Task_Popup_Metadata': popupMetaData?.map((e) => e.toJson()).toList(),
    };
  }
}


