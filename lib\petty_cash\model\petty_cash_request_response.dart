class PettyCashRequest {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final Map<String, dynamic>? meetingParameters;
  final String? displayRecId;
  final String? mangerNameArabi;
  final String? groupsEmails;
  final String? nxtAprNameAr;
  final String? requestDetails;
  final String? employeeId;
  final String? elmWebUrl;
  final String? managerEmail;
  final String? requestStatus;
  final String? requesterEmail;
  final String? cashJorNum;
  final String? reqStatusAr;
  final String? code;
  final List<dynamic>? attachments;
  final String? resultMessageEn;
  final String? taskWebUrl;
  final String? rejectedByAr;
  final String? approvedByAr;
  final String? closedDate;
  final String? resultMessage;
  final String? myTypeId;
  final String? projectNameEn;
  final String? rejectedByEn;
  final String? cashUsageNameEn;
  final String? userId;
  final String? costCenter;
  final String? budgetType;
  final String? approvedByEn;
  final String? cashUsageNameAr;
  final String? bankNameEn;
  final String? notes;
  final String? requestType;
  final String? groupId;
  final String? projectId;
  final String? withdrawFlag;
  final String? withdrawReason;
  final String? cashAmount;
  final String? requesterNameAr;
  final String? resultMessageAr;
  final List<dynamic>? nextApprovalList;
  final List<ApprovalProcess>? approvalProce;
  final String? purpose;
  final String? pendingRequest;
  final String? rejectionReason;
  final String? mangerNameEngli;
  final String? exception;
  final dynamic reqCardData;
  final String? authKey;
  final String? cashUsegeId;
  final String? tableName;
  final String? ibanNumber;
  final String? nxtAprNameEn;
  final UserManager? userManager;
  final String? requestTypeAr;
  final String? requesterNameEn;
  final String? bankNameAr;
  final String? policyApprove;
  final String? projectNameAr;
  final String? createdDateFormatted;
  final String? serviceKey;
  final String? budgetTypeAr;

  PettyCashRequest({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.meetingParameters,
    this.displayRecId,
    this.mangerNameArabi,
    this.groupsEmails,
    this.nxtAprNameAr,
    this.requestDetails,
    this.employeeId,
    this.elmWebUrl,
    this.managerEmail,
    this.requestStatus,
    this.requesterEmail,
    this.cashJorNum,
    this.reqStatusAr,
    this.code,
    this.attachments,
    this.resultMessageEn,
    this.taskWebUrl,
    this.rejectedByAr,
    this.approvedByAr,
    this.closedDate,
    this.resultMessage,
    this.myTypeId,
    this.projectNameEn,
    this.rejectedByEn,
    this.cashUsageNameEn,
    this.userId,
    this.costCenter,
    this.budgetType,
    this.approvedByEn,
    this.cashUsageNameAr,
    this.bankNameEn,
    this.notes,
    this.requestType,
    this.groupId,
    this.projectId,
    this.withdrawFlag,
    this.withdrawReason,
    this.cashAmount,
    this.requesterNameAr,
    this.resultMessageAr,
    this.nextApprovalList,
    this.approvalProce,
    this.purpose,
    this.pendingRequest,
    this.rejectionReason,
    this.mangerNameEngli,
    this.exception,
    this.reqCardData,
    this.authKey,
    this.cashUsegeId,
    this.tableName,
    this.ibanNumber,
    this.nxtAprNameEn,
    this.userManager,
    this.requestTypeAr,
    this.requesterNameEn,
    this.bankNameAr,
    this.policyApprove,
    this.projectNameAr,
    this.createdDateFormatted,
    this.serviceKey,
    this.budgetTypeAr,
  });

  factory PettyCashRequest.fromJson(Map<String, dynamic> json) {
    return PettyCashRequest(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      meetingParameters: json['meetingParameters'] ?? {},
      displayRecId: json['display_recid'],
      mangerNameArabi: json['mangernamearabi'],
      groupsEmails: json['groups_emails'],
      nxtAprNameAr: json['nxt_apr_name_ar'],
      requestDetails: json['request_details'],
      employeeId: json['employeeid'],
      elmWebUrl: json['elm_web_url'],
      managerEmail: json['manageremail'],
      requestStatus: json['requeststatus'],
      requesterEmail: json['requesteremail'],
      cashJorNum: json['cash_jor_num'],
      reqStatusAr: json['req_status_ar'],
      code: json['code'],
      attachments: json['attachments'] ?? [],
      resultMessageEn: json['resultmessageen'],
      taskWebUrl: json['task_web_url'],
      rejectedByAr: json['rejectedbyar'],
      approvedByAr: json['approved_by_ar'],
      closedDate: json['closed_date'],
      resultMessage: json['resultmessage'],
      myTypeId: json['mytypeid'],
      projectNameEn: json['projectnameen'],
      rejectedByEn: json['rejectedbyen'],
      cashUsageNameEn: json['cashusagenameen'],
      userId: json['userid'],
      costCenter: json['cost_center'],
      budgetType: json['budget_type'],
      approvedByEn: json['approved_by_en'],
      cashUsageNameAr: json['cashusagenamear'],
      bankNameEn: json['bank_name_en'],
      notes: json['notes'],
      requestType: json['request_type'],
      groupId: json['group_id'],
      projectId: json['projectid'],
      withdrawFlag: json['withdrawflag'],
      withdrawReason: json['withdrawreason'],
      cashAmount: json['cash_amount'],
      requesterNameAr: json['requesternamear'],
      resultMessageAr: json['resultmessagear'],
      nextApprovalList: json['nextapprovallis'] ?? [],
      approvalProce: (json['approvalproce'] as List)
          .map((e) => ApprovalProcess.fromJson(e))
          .toList(),
      purpose: json['purpose'],
      pendingRequest: json['pending_request'],
      rejectionReason: json['rejectionreason'],
      mangerNameEngli: json['mangernameengli'],
      exception: json['exception'],
      reqCardData: json['req_card_data'],
      authKey: json['authkey'],
      cashUsegeId: json['cashusegeid'],
      tableName: json['table_name'],
      ibanNumber: json['iban_number'],
      nxtAprNameEn: json['nxt_apr_name_en'],
      userManager: UserManager.fromJson(json['usermanager']),
      requestTypeAr: json['request_type_ar'],
      requesterNameEn: json['requesternameen'],
      bankNameAr: json['bank_name_ar'],
      policyApprove: json['policy_approve'],
      projectNameAr: json['projectnamear'],
      createdDateFormatted: json['createddate'],
      serviceKey: json['service_key'],
      budgetTypeAr: json['budget_type_ar'],
    );
  }
}

class ApprovalProcess {
  final String? taskName;
  final String? processName;
  final int? taskSeq;
  final String? plannedStartDate;
  final String? plannedEndDate;
  final int? duration;
  final Template? template;

  ApprovalProcess({
    this.taskName,
    this.processName,
    this.taskSeq,
    this.plannedStartDate,
    this.plannedEndDate,
    this.duration,
    this.template,
  });

  factory ApprovalProcess.fromJson(Map<String, dynamic> json) {
    return ApprovalProcess(
      taskName: json['taskName'],
      processName: json['processName'],
      taskSeq: json['taskSeq'],
      plannedStartDate: json['plannedStartDate'],
      plannedEndDate: json['plannedEndDate'],
      duration: json['duration'],
      template: Template.fromJson(json['template']),
    );
  }
}

class Template {
  final int? createdById;
  final String? createdBy;
  final int? createdDate;
  final String? companyName;
  final String? uuid;
  final int? recId;
  final String? templateName;
  final int? typeId;

  Template({
    this.createdById,
    this.createdBy,
    this.createdDate,
    this.companyName,
    this.uuid,
    this.recId,
    this.templateName,
    this.typeId,
  });

  factory Template.fromJson(Map<String, dynamic> json) {
    return Template(
      createdById: json['createdById'],
      createdBy: json['createdBy'],
      createdDate: json['createdDate'],
      companyName: json['companyName'],
      uuid: json['uuid'],
      recId: json['recId'],
      templateName: json['templateName'],
      typeId: json['typeId'],
    );
  }
}

class UserManager {
  final String? organization;
  final String? groupId;
  final String? member;
  final String? company;
  final String? group;
  final String? memberId;

  UserManager({
    this.organization,
    this.groupId,
    this.member,
    this.company,
    this.group,
    this.memberId,
  });

  factory UserManager.fromJson(Map<String, dynamic> json) {
    return UserManager(
      organization: json['organization'],
      groupId: json['groupId'],
      member: json['member'],
      company: json['company'],
      group: json['group'],
      memberId: json['memberId'],
    );
  }
}
