import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/widgets/sar_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../styles/colors.dart';
import '../utility/font_utility.dart';

class WorkAlertWidget extends StatelessWidget {
  const WorkAlertWidget({
    super.key,
    required this.valueEn,
    required this.valueAR,
  });

  final String valueEn;
  final String valueAR;
  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        decoration: ShapeDecoration(
          color: consumedTextColor.withValues(alpha: 0.2),
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              color: consumedTextColor,
            ),
            borderRadius: BorderRadius.circular(4.r),
          ),
        ),
        child: Row(
          children: [
            Icon(getIconFromCss("fa-solid fa-circle-info"),
                color: consumedTextColor),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(AppLocalizations.appLang == "en" ? valueEn : valueAR,
                  maxLines: 5,
                  style: FontUtilities.getTextStyle(
                    TextType.medium,
                    size: 12.sp,
                    textColor: consumedTextColor,
                    fontWeight: FontWeight.w500,
                  )),
            ),
          ],
        ));
  }
}
