class SubmitPersonalLoanRequestBody {
  String? loanamount;
  String? policyapprove;
  String? numberofinstall;
  String? maximumloanlimi;
  String? monthinstallval;

  SubmitPersonalLoanRequestBody({
    this.loanamount,
    this.policyapprove,
    this.numberofinstall,
    this.maximumloanlimi,
    this.monthinstallval,
  });

  factory SubmitPersonalLoanRequestBody.fromJson(Map<String, dynamic> json) {
    return SubmitPersonalLoanRequestBody(
      loanamount: json['loanamount'] as String?,
      policyapprove: json['policyapprove'] as String?,
      numberofinstall: json['numberofinstall'] as String?,
      maximumloanlimi: json['maximumloanlimi'] as String?,
      monthinstallval: json['monthinstallval'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'loanamount': loanamount,
        'policyapprove': policyapprove,
        'numberofinstall': numberofinstall,
        'maximumloanlimi': maximumloanlimi,
        'monthinstallval': monthinstallval,
      };
}
