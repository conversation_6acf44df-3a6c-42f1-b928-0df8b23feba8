import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/widgets/sar_component.dart';
import '../../../success_view/success_screen_view.dart';
import '../../models/submitBodyModel.dart';

class SuccessScreencontentList extends StatelessWidget {
  final   SubmitResponse?  submitResponse;

  const SuccessScreencontentList({super.key, this.submitResponse});

  @override
  Widget build(BuildContext context) {
    return submitResponse == null
        ? SizedBox.shrink()
        :  Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildHeaderText(AppLocalizations.of(context).translate('Request Type')),
        buildDetailsText((AppLocalizations.appLang == 'en' ?submitResponse?.requestType:submitResponse?.requestTypeAr) ??''),
        buildDivider(),

        buildHeaderText(AppLocalizations.of(context).translate('Request ID')),
        buildDetailsText(submitResponse?.displayRecid.toString() ??''),
        buildDivider(),

       buildHeaderText(AppLocalizations.of(context).translate('Budget Type')),
       buildDetailsText((AppLocalizations.appLang == 'en' ?submitResponse?.budgetTypeEn:submitResponse?.budgetTypeAr) ??''),
       buildDivider(),

        ((submitResponse?.costCenterId != null ) && (submitResponse?.budgetTypeEn == "Department") ) ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderText(AppLocalizations.of(context).translate('Cost Center')),
            buildDetailsText(submitResponse?.costCenterId ?? ''),
            buildDivider(),
          ],
        ): SizedBox.shrink(),

        ( submitResponse?.projectNameEn !=null ) ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderText(AppLocalizations.of(context).translate("Project Name")),
            buildDetailsText(submitResponse?.projectNameEn  ?? ''),
            buildDivider(),
          ],
        ): SizedBox.shrink(),

        buildHeaderText(AppLocalizations.of(context).translate('Customer Name')),
        buildDetailsText((AppLocalizations.appLang == 'en' ?submitResponse?.customerNameE:submitResponse?.customerNameA) ??''),
        buildDivider(),

        buildHeaderText(AppLocalizations.of(context).translate('Period (days)')),
        buildDetailsText((AppLocalizations.appLang == 'en' ?submitResponse?.periodDaysEn:submitResponse?.periodDaysAr) ??''),
        buildDivider(),

        buildHeaderText(AppLocalizations.of(context).translate('Start Date')),
        buildDetailsText(submitResponse?.startdate),
        buildDivider(),

        buildHeaderText(AppLocalizations.of(context).translate('Guarantee Type')),
        buildDetailsText((AppLocalizations.appLang == 'en' ?submitResponse?.guarantTypeEn:submitResponse?.guarantTypeAr) ??''),
        buildDivider(),


        buildHeaderText(AppLocalizations.of(context).translate("Tender Value")),
        buildCurrencyDetailsText(submitResponse?.tendervalue),
        buildDivider(),

        buildHeaderText(AppLocalizations.of(context).translate('Guarantee Percentage (%)')),
        buildDetailsText(submitResponse?.guaranteePerce),
        buildDivider(),

        buildHeaderText(AppLocalizations.of(context).translate('Guarantee Value')),
        buildCurrencyDetailsText(submitResponse?.guaranteevalue),
        buildDivider(),



      ],
    );
  }
}
