import '../../../../shared/network/api_helper.dart';
import '../../../../shared/network/api_utilities.dart';
import '../../../../shared/network/endpoints.dart';
import '../../../../shared/utility/secure_storage.dart';

abstract class IGenericActionRepo {
  Future<NetworkResponse> loadPopupContent(
      String actionButton, String mutableContext);
  Future<NetworkResponse> submitGenericAction(
      Map<String,dynamic> submitBody,String serviceId);
}

class GenericActionRepo extends IGenericActionRepo {
  @override
  Future<NetworkResponse> loadPopupContent(
      String actionButton, String mutableContext) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {
          "action_button": actionButton,
          "mutable_context": mutableContext
        },
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'SessionToken': sessionToken,
          'formName': 'Acc Task Definition Form',
          'moduleName': 'Acc Task Definition',
          'appKey': 'NOT',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> submitGenericAction(Map<String, dynamic> submitBody,String serviceId) async{
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body:submitBody,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers:getHeaders(serviceId));
    return response;
  }

  Map<String,String> getHeaders(String serviceId){
    Map<String,String> headers={
      'Content-Type': 'application/json',
      'appKey': serviceId.substring(0,(serviceId).indexOf("_")),
    };
    switch(serviceId){
      case leaveRequestID:
        headers['formName']="Leave Request Form";
        headers['moduleName']="Leave Request";
        break;
      case generalExpenseID:
        headers['formName']="Expense Claim Request Form";
        headers['moduleName']="Expense Claim Request";
        break;
      case schoolAllowanceID:
        headers['formName']="School Allowance Form";
        headers['moduleName']="School Allowance";
        break;
      case mazayaID:
        headers['formName']="Mazaya Benefits Form";
        headers['moduleName']="Mazaya Benefits";
        break;
      case travelID:
        headers['formName']="Submit Travel Form";
        headers['moduleName']="Submit Travel";
        break;
      case updateIbanID:
        headers['formName']="IBAN Service Form";
        headers['moduleName']="IBAN Service";
        break;
      case passportUpdateID:
        headers['formName']="Passport Update Form";
        headers['moduleName']="Passport Update";
        break;
      case businessCardID:
        headers['formName']="Submit Business Card Form";
        headers['moduleName']="Submit Business Card";
        break;
      case iqamaRenewalID:
        headers['formName']="Iqama Renewal Request Form";
        headers['moduleName']="Iqama Renewal Request";
        break;
      case iqamaPrintingID:
        headers['formName']="Iqama Printing Request Form";
        headers['moduleName']="Iqama Printing Request";
        break;
      case cancelIqamaForEmployeeID:
        headers['formName']="Iqama Cancelation for Employee Form";
        headers['moduleName']="Iqama Cancelation for Employee";
        break;
      case iqamaIssueEmployeeFamilyID:
        headers['formName']="Iqama Issue for Employee Family Form";
        headers['moduleName']="Iqama Issue for Employee Family";
        break;
      case cancelIqamaForEmployeeFamilyID:
        headers['formName']="Service Data Form";
        headers['moduleName']="submit Iqama Cancellation by employee family";
        break;
      case iqamaIssueForEmployeeID:
        headers['formName']="Iqama Issue For Employee Request";
        headers['moduleName']="Iqama Issue For Employee Request";
        break;
      case deparmentlaunchID:
        headers['formName']="Department Lunch Form";
        headers['moduleName']="Department Lunch";
        break;
      case updateEducationID:
        headers['formName']="Update Education Details Form";
        headers['moduleName']="Update Education Details";
        break;
      case cancelReentryVisaID:
        headers['formName']="Cancel Reentry Exit Visa Form";
        headers['moduleName']="Cancel Reentry Exit Visa ";
        break;
      case issueExitReentryVisaID:
        headers['formName']="Issue Exit Re-Entry Visa Form";
        headers['moduleName']="Issue Exit Re-Entry Visa";
        break;
      case extendExitReentryVisaID:
        headers['formName']="Extend Exit Re-Entry Visa";
        headers['moduleName']="Extend Exit Re-Entry Visa";
        break;
      default:
        headers['formName']='Service Data Form';
        headers['moduleName']='Service Data';

    }
    return headers;
  }
}
const String leaveRequestID="HRS_LEAVEREQUEST";
const String generalExpenseID="HRS_EXPENSECLAIMREQUEST";
const String schoolAllowanceID="HRS_SCHOOLALLOWANCE";
const String mazayaID="HRS_MAZAYABENEFITS";
const String travelID="HTS_SUBMITTRAVEL";
const String updateIbanID="BAN_IBANSERVICE";
const String passportUpdateID="PAS_PASSPORTUPDATE";
const String businessCardID="BUS_SUBMITBUSINESSCARD";
const String iqamaRenewalID="IRN_IQAMARENEWALREQUEST";
const String iqamaPrintingID="IPT_IQAMAPRINTINGREQUEST";
const String cancelIqamaForEmployeeID="ICH_SERVICEDATA";
const String iqamaIssueEmployeeFamilyID="IIF_SERVICEDATA";
const String cancelIqamaForEmployeeFamilyID="ICF_SERVICEDATA";
const String iqamaIssueForEmployeeID="IIE_SERVICEDATA";
const String deparmentlaunchID="DEP_DEPARTMENTLUNCH";
const String updateEducationID="EDU_UPDATEEDUCATIONDETAILS";
const String cancelReentryVisaID="CRV_SERVICEDATA";
const String issueExitReentryVisaID="IER_SERVICEDATA";
const String extendExitReentryVisaID="EXV_SERVICEDATA";
