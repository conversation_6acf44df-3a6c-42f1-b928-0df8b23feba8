import 'dart:convert';
import 'package:eeh/business_card/bloc/business_card_event.dart';
import 'package:eeh/business_card/bloc/business_card_state.dart';
import 'package:eeh/business_card/model/business_card_details_model.dart';
import 'package:eeh/business_card/model/business_card_submit_response_model.dart';
import 'package:eeh/business_card/model/office_location_model.dart';
import 'package:eeh/business_card/repo/business_card_repo.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/login/models/get_emp_info_response.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/utility/secure_storage.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BusinessCardBloc extends Bloc<BusinessCardEvent, BusinessCardState> {
  TextEditingController mobileNumController = TextEditingController();
  TextEditingController officeLocationController = TextEditingController();
  GetEmployeeInfoResponse? empInfo;
  BusinessCardRepo repo = BusinessCardRepo();
  EmployeeDetailsResponse? employeeDetails;
  OfficeLocationResponse? officeLocation;
  OfficeLocation? selectedOffice;
  SubmitResponse? submitResponse;
  bool isValidMobileNum = false;
  BusinessCardBloc() : super(BusinessCardInitialState()) {
    on<BusinessCardInitialEvent>((event, emit) async {
      await getEmpInfo();
      emit(BusinessCardInitialState());
    });

    on<FetchEmployeeDetailsEvent>((event, emit) async {
      emit(FetchEmployeeDetailsLoadingState());
      await fetchEmployeeDetails(emit);
    });

    on<FetchOfficeLocationEvent>((event, emit) async {
      emit(FetchOfficeLocationLoadingState());
      await fetchOfficeLocation(emit);
    });

    on<OfficeLocationValueChangeEvent>((event, emit) async {
      emit(OfficeLocationValueChangeState());
      await onOfficeLocationValueChange(event.index, emit);
    });

     on<MobileNumberValueChangeEvent>((event, emit) async {
      onMobileValueChange(event.value,emit);
      emit(MobileNumberValuechangeState());
    });

    on<SubmitRequestEvent>((event, emit) async {
      emit(SubmitBusinessCardRequestLoadingState());
      await submitBusinessCardRequest(emit);
    });
  }

  bool isButtonEnable() {
    return (mobileNumController.text != '' ||
            mobileNumController.text.isNotEmpty) &&
        (officeLocationController.text != '' ||
            officeLocationController.text.isNotEmpty);
  }

  Future getEmpInfo() async {
    String? jsonString =
        await SecureStorageService().readSecureData(key: "emp_info_response");
    if (jsonString != null) {
      Map<String, dynamic> jsonMap = jsonDecode(jsonString);
      empInfo = GetEmployeeInfoResponse.fromJson(jsonMap);
    }
  }

  fetchEmployeeDetails(emitter) async {
    await repo
        .fetchEmployeeDetails(employeeid: empInfo?.employeeid ?? '')
        .then((response) => onFetchEmployeeDetails(response, emitter));
  }

  void onFetchEmployeeDetails(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchEmployeeDetailsSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(FetchEmployeeDetailsErrorState());
    });
  }

  void onFetchEmployeeDetailsSuccess(data, emitter) {
    employeeDetails = EmployeeDetailsResponse.fromJson(data);
    emitter(FetchEmployeeDetailsSuccessState());
  }

  fetchOfficeLocation(emitter) async {
    await repo
        .fetchOfficeLocation(lang: '')
        .then((response) => onFetchOfficeLocation(response, emitter));
  }

  void onFetchOfficeLocation(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchOfficeLocationSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(FetchOfficeLocationErrorState());
    });
  }

  void onFetchOfficeLocationSuccess(data, emitter) {
    officeLocation = OfficeLocationResponse.fromJson(data);
    emitter(FetchOfficeLocationSuccessState());
  }

  onOfficeLocationValueChange(
    int index,
    emitter,
  ) {
    final item = officeLocation?.getofficelocati![index];
    officeLocationController.text =
        (AppLocalizations.appLang == 'en' ? item?.nameen : item?.namear) ?? '';
    selectedOffice = item;
    emitter(OfficeLocationValueChangeSuccessState());
  } 

  onMobileValueChange(String value , emitter){
    if(value.isEmpty || value == ''){
      emitter(MobileNumberValuechangeErrorState());
    }else{
      emitter(MobileNumberValuechangeSuccessState());
    }
  } 

  submitBusinessCardRequest(emitter) async {
    await repo
        .submitBusinessCardRequest(
          mobileNumber: mobileNumController.text,
          officeLocationID:selectedOffice?.id ??'')
        .then((response) => onSubmitRequest(response, emitter));
  }

  void onSubmitRequest(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitBusinessCardRequestErrorState());
    });
  }

  void onSubmitSuccess(data, emitter) {
    submitResponse = SubmitResponse.fromJson(data);
    emitter(SubmitBusinessCardRequestSuccessState());
  }
}
