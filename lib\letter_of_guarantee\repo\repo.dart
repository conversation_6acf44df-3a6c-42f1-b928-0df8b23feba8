import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/letter_of_guarantee/models/guaranteeDataResponse.dart';
import '../../shared/network/endpoints.dart';
import '../../shared/utility/methods.dart';
import '../../shared/utility/secure_storage.dart';
import '../models/submitBodyModel.dart';

abstract class IGuaranteeRepo {
  Future<NetworkResponse> getGuaranteeDropdownData();
  Future<NetworkResponse> getCustomerName();
  Future<NetworkResponse> getProjectName();
  Future<NetworkResponse> getCustomerList({
    required String searchTerm,
    required String skip,
    required String top,
  });

  Future<NetworkResponse> getSubmit({
    required LetterOfGuaranteeBody submitBodyModel ,
  });

}

class GuaranteeRepo implements IGuaranteeRepo {

  @override
  Future<NetworkResponse> getGuaranteeDropdownData() async {
    final sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? '';

    NetworkResponse<dynamic> response = await Api<PERSON>elper().apiCall(
      genericObject,
      body: {
        "optimizedUdasValueMap": ""
      },
      requestType: RequestType.post,
      sessionToken: sessionToken,
      headers: {
        'formName': 'Get Dropdown list data Form',
        'moduleName': 'Get Dropdown list data',
        'appKey': 'LEG',
        'Content-Type': 'application/json'
      },
    );

    // Return the response directly
    return response;
  }

  @override
  Future<NetworkResponse> getCustomerName()async {
    final sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? '';

    NetworkResponse<dynamic> response = await ApiHelper().apiCall(
      genericObject,
      requestType: RequestType.post,
      sessionToken: sessionToken,
      body: {"search_term": '', "skip": '', "top": ''},
      headers: {
        'formName': 'CustomerInformation Form',
        'moduleName': 'CustomerInformation',
        'appKey': 'NOT',
        'Content-Type': 'application/json'
      },
    );

    // Return the response directly
    return response;
  }


  @override
  Future<NetworkResponse> getProjectName() async {
    final sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? '';

    NetworkResponse<dynamic> response = await ApiHelper().apiCall(
      genericObject,
      body: {
        "employeeid":  (await getEmpId())??''
      },
      requestType: RequestType.post,
      sessionToken: sessionToken,
        headers: {
        'formName': 'Project WPS Form',
        'moduleName': 'Project WPS',
        'appKey': 'NOT',
        'Content-Type': 'application/json'
               },
    );


    return response;
    }

  @override
  Future<NetworkResponse> getCustomerList({required String searchTerm,
    required String skip,
    required String top,}) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'search_term': searchTerm,
          'skip': skip,
          'top': top},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "CustomerInformation Form",
          "moduleName": "CustomerInformation",
          "appKey": "NOT",
          "Content-Type": "application/json",
        });

    return response;

  }
  @override
  Future<NetworkResponse> getSubmit({required LetterOfGuaranteeBody submitBodyModel }) async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: submitBodyModel.toJson(),
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Service Data Form",
          "moduleName": "Service Data",
          "appKey": "LEG",
          "Content-Type": "application/json",
        });
    return response;

  }
}



    // final guaranteeRepo = GuaranteeRepo();










