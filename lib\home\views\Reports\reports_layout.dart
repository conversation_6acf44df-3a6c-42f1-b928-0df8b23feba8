import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_bloc.dart';
import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_states.dart';
import 'package:eeh/home/<USER>/Reports/tableau_web_view.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/services_screen/widgets/elm_service_list_item.dart';

import 'package:eeh/shared/widgets/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../services_screen/models/services_response.dart';
import '../../../shared/routes/route_generator.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/utility/font_utility.dart';

// ignore: must_be_immutable
class ReportsScreen extends StatefulWidget {
  final String? currentServiceName;
  final String? previousServiceName;
  Servicescategor? servicesCategory;
  ReportsBloc bloc;
  ReportsScreen(
      {super.key,
        this.currentServiceName,
        this.servicesCategory,
        required this.bloc,
        this.previousServiceName});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  bool isSearchTapped = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.appLang == 'en'
            ? widget.servicesCategory?.categoryNameEn ??''
            : widget.servicesCategory?.categoryNameAr ??'',
        appbar: AppbarType.present ,
        onActionIconPressed: () {
          setState(() {
            isSearchTapped = !isSearchTapped;
          });
        },
      ),
      body:BlocConsumer<ReportsBloc, ReportsState>(
          bloc: widget.bloc,
          listener: (ctx,state){
          },
          builder: (context, state) {
            // widget.bloc = context.read<ServicesBloc>();
            return (
                widget.servicesCategory == null ||
                    (widget.servicesCategory?.servicesSubCategory ?? []).isEmpty )
                ? Center(
              child: CircularProgressIndicator(),
            )
                : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getServicesFlowNamesWidget(),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0),
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        Elmservice? report=widget.servicesCategory?.servicesSubCategory[index];
                        return  SizedBox(
                          width: 375.w,
                          height: 80.h,
                          child: Card(
                            elevation: 2,
                            color: Colors.white,
                            shadowColor: Colors.white,
                            surfaceTintColor: Colors.white,
                            shape: const RoundedRectangleBorder(
                              borderRadius:
                              BorderRadius.all(Radius.circular(8.0)),
                              side:
                              BorderSide(color: Color(0xffF6F6F6), width: 1),
                            ),
                            child: ElmServicesListItemComponent(
                              onTap: (){
                                final route=  RouteGenerator. buildRoute( TableauWebView(report: report,), settings: RouteSettings(name:report?.serviceKey??'' ));
                                if ((report?.isActive??false)) {
                                  Navigator.of(context).push(route);
                                }
                              },

                              serviceTitle:AppLocalizations.appLang=="en"?  report?.serviceNameEn:report?.serviceNameAr,
                              previousServiceName: widget.currentServiceName ??
                                  AppLocalizations.of(context)
                                      .translate('Reports'),
                              serviceImageURL: "${report?.serviceIconName}",
                              serviceDescription:AppLocalizations.appLang=="en"? report?.serviceDescriptionEn:report?.serviceDescriptionAr,
                              backgroundColor:report?.backgroundColor??'' ,
                              isServiceEnabled: report?.isActive??true,
                            ),
                          ),
                        );
                      },
                      itemCount: widget.servicesCategory?.servicesSubCategory.length,
                    ),
                  ),
                ),
              ],
            );
          }),
    );
  }

  Widget getServicesFlowNamesWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        children: [
          SizedBox(
            height: 21.h,
          ),
          Row(
            children: [
              Text(
                AppLocalizations.of(context).translate("Services"),
                style: FontUtility.getTextStyleForText(TextType.medium,
                    textColor: textColor, isBold: true, size: 12.sp),
              ),
              SizedBox(
                width: 11.w,
              ),
              Padding(
                padding: EdgeInsets.only(top: 2.h),
                child: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 12.sp,
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(  AppLocalizations.appLang == 'en'
                  ? widget.servicesCategory?.categoryNameEn ?? ''
                  : widget.servicesCategory?.categoryNameAr ?? '',
                style: FontUtility.getTextStyleForText(TextType.medium,
                    textColor: primaryColor, isBold: true, size: 12.sp),
              ),
            ],
          ),
          SizedBox(
            height: 12.h,
          ),
        ],
      ),
    );
  }
}

