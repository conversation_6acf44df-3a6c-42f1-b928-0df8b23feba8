import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/my_tasks_revamp/bloc/my_task_bloc_revamp.dart';
import 'package:eeh/my_tasks_revamp/bloc/tasks_events.dart';
import 'package:eeh/my_tasks_revamp/bloc/tasks_states.dart';
import 'package:eeh/my_tasks_revamp/models/approval_templates_model.dart';
import 'package:eeh/my_tasks_revamp/widgets/action_manger/actions.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/custom_elevated_button.dart';
import 'package:eeh/shared/widgets/general_text_form_field.dart';
import 'package:eeh/shared/work_widget/utility_widgets/withdraw_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class ApprovelTemplate extends IAdminAction {
  ApprovelTemplate(super.actionData, super.context, super.workModel);
  final TextEditingController approvalTemplateController =
      TextEditingController();
  GlobalKey<FormFieldState> approvalTemplateKey =
      GlobalKey<FormFieldState>(debugLabel: "approvalTemplate");
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  Approval? approval;
  Template? selectedTamplate;
  List<ApprovalTemplate>? approvalTemplates = [];
  onApprovalSelection(value) {
    selectedTamplate = value;
    approvalTemplates = filteredList(approval?.approvaltemplat ?? [],
        (esmType) => esmType.templateKey == value.key);
  }

 List<T> filteredList<T>(
      List<T> sourceList, bool Function(T) filterCondition) {
    return sourceList.where(filterCondition).toList();
  }
    _clearControllers() {
    approvalTemplateController.clear();
  }
  bool checkVisibility() {
    return approvalTemplateController.text.isNotEmpty;
  }
  @override
  onActionTapped() {
    _clearControllers();
    Color actionBtnColor = primaryColor.withOpacity(0.4);
    TasksBlocRevamp.instance(context).add(ApprovalTemplatesEvent(requestID: int.tryParse(workModel.requestID)??-1));
    return genericActionsSheet(
      context,
      validationText: AppLocalizations.of(context).translate('validation_failed'),
      isCancelable: false,
      headerTitle: AppLocalizations.of(context).translate("Get approval"),
      headerDescription: AppLocalizations.of(context).translate("Please get require approval"),
      headerIcon: FontAwesomeIcons.circleCheck,
      headerIconColor: alertSuccess,
      endContent: [
        BlocConsumer<TasksBlocRevamp,TasksState>(
          buildWhen: (current, previous) {
         return current is TemplatesLoadingState ||
                current is TemplatesSuccessState ||
                current is TemplatesErrorState;
          },
          listener: (context, state) { },
          builder: (context, state) {
            return (state is TemplatesLoadingState)
                ? Center(
                    child: CircularProgressIndicator(),
                  )
                : SafeArea(
                    bottom: false,
                    child: StatefulBuilder(
                      builder: (context, setState) {
                        approval = TasksBlocRevamp.instance(context).approvalTemplates;
                        return Form(
                            key: formKey,
                            onChanged: () {
                              if (approvalTemplateController.text.isNotEmpty) {
                                actionBtnColor = primaryColor;
                              } else {
                                actionBtnColor = primaryColor.withOpacity(.40);
                              }
                              setState(() {});
                            },
                            child: Column(
                              children: [
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 14.w),
                                  child: Column(
                                    spacing: 12.h,
                                    children: [
                                      buildApprovalField(),
                                      if(checkVisibility())
                                      getTable(),
                                    ],
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Text(
                                    AppLocalizations.of(context)
                                        .translate('Cancel'),
                                    style: TextStyle(
                                      color: rejectionColor,
                                      fontSize: 18.sp,
                                    ),
                                  ),
                                ),
                                BlocConsumer<TasksBlocRevamp, TasksState>(
                                    listener: (context, state) {},
                                    builder: (context, state) {
                                      return Transform.translate(
                                        offset: const Offset(0, 4),
                                        child:
                                         CustomElevatedButton(
                                          fontSize: 18.sp,
                                          isLoading:state is SubmitTemplatesLoadingState,
                                          backgroundColor: actionBtnColor,
                                          width: double.infinity,
                                          title: AppLocalizations.of(context).translate("Submit"),
                                          onPressed:
                                              state is! SubmitTemplatesLoadingState
                                                  ? () {
                                                      if (formKey.currentState!.validate()) {
                                                        TasksBlocRevamp.instance(context).add(
                                                          SubmitTemplatesEvent(
                                                            recId: int.tryParse(workModel.requestID)??-1,
                                                            templatekey: selectedTamplate?.key??'',
                                                          ),
                                                        );
                                                        Navigator.of(context).pop();
                                                      }
                                                      Navigator.of(context).pop();
                                                    }
                                                  : () {},
                                        ),
                                      );
                                    }),
                              ],
                            ));
                      },
                    ));
          },
        ),
      ],
    );
  }

  buildApprovalField() {
    return GeneralTextFormField(
      fieldKey: approvalTemplateKey,
      labelText: AppLocalizations.of(context).translate("Approval Template"),
      controller: approvalTemplateController,
      keyboardType: TextInputType.none,
      hasSearch: true,
      isReadOnly: true,
      isRequired: true,
      isLoading: false,
      onSelect: onApprovalSelection,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return AppLocalizations.of(context).translate("must select template");
        }
        return null;
      },
      hasBottomSheet: true,
      sheetTitle: AppLocalizations.of(context).translate("Approval Template"),
      sheetContentList:approval?.templates,
    );
  }

  getTable() {
    Radius radius = Radius.circular(8);
    return Table(
      border: TableBorder.all(
        borderRadius: BorderRadius.circular(8),
        color: Color(0xffB9C8FC).withOpacity(0.6),
        width: 1,),
      columnWidths: {
        0: FlexColumnWidth(3), 
        1: FlexColumnWidth(1),
      },
      children: [
        TableRow(
          decoration: BoxDecoration(color: Colors.blue.shade50,
              borderRadius: BorderRadius.only(topRight:radius,topLeft:radius)),
          children: [
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                AppLocalizations.appLang == 'en'?
                (approval?.headers?[0].headerNameEn??'') : (approval?.headers?[0].headerNameAr??''),
                style:FontUtilities.getTextStyle(TextType.medium),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                AppLocalizations.appLang == 'en'?
                (approval?.headers?[1].headerNameEn??'') : (approval?.headers?[1].headerNameAr??''),
                style:FontUtilities.getTextStyle(TextType.medium),
              ),
            ),
          ],
        ),
         if (approvalTemplates != null)
          ...?approvalTemplates?.map((template) {
            return buildTableRow(
              AppLocalizations.appLang == 'en'
                  ? (template.groupNameEn ?? '')
                  : (template.groupNameAr ?? ''),
              template.sla ?? '',
            );
          }),
      ],
    );
  }

  TableRow buildTableRow(String groupName, String sla) {
    Radius radius = Radius.circular(8);
    return TableRow(
      decoration:BoxDecoration(color:Color(0xffB9C8FC).withOpacity(0.1),
          borderRadius: BorderRadius.only(bottomRight:radius,bottomLeft:radius)) ,
      children: [
        Padding(
          padding: EdgeInsets.all(8.0),
          child: Text(
            groupName,
            style:FontUtilities.getTextStyle(TextType.regular, size: 12.sp),),
        ),
        Padding(
          padding: EdgeInsets.all(8.0),
          child: Text(
            sla,
            style: FontUtilities.getTextStyle(TextType.regular, size: 12.sp),
          ),
        ),
      ],
    );
  }
}
