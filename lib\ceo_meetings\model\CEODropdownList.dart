class CEODropdownList {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  List<PurposeMeeting>? purposeMeeting;
  List<PurposeMeeting>? requiredMeetin;
  List<PurposeMeeting>? ceoMeetingTyp;
  List<PurposeMeeting>? ceoPriority;
  List<PurposeMeeting>? meetingCategor;

  CEODropdownList(
      {this.createdBy,
        this.createdById,
        this.createdDate,
        this.recId,
        this.purposeMeeting,
        this.requiredMeetin,
        this.ceoMeetingTyp,
        this.ceoPriority,
        this.meetingCategor});

  CEODropdownList.fromJson(Map<String, dynamic> json) {
    createdBy = json['createdBy'];
    createdById = json['createdById'];
    createdDate = json['createdDate'];
    recId = json['recId'];
    if (json['purpose_meeting'] != null) {
      purposeMeeting = <PurposeMeeting>[];
      json['purpose_meeting'].forEach((v) {
        purposeMeeting!.add(new PurposeMeeting.fromJson(v));
      });
    }
    if (json['required_meetin'] != null) {
      requiredMeetin = <PurposeMeeting>[];
      json['required_meetin'].forEach((v) {
        requiredMeetin!.add(new PurposeMeeting.fromJson(v));
      });
    }
    if (json['ceo_meeting_typ'] != null) {
      ceoMeetingTyp = <PurposeMeeting>[];
      json['ceo_meeting_typ'].forEach((v) {
        ceoMeetingTyp!.add(new PurposeMeeting.fromJson(v));
      });
    }
    if (json['ceo_priority'] != null) {
      ceoPriority = <PurposeMeeting>[];
      json['ceo_priority'].forEach((v) {
        ceoPriority!.add(new PurposeMeeting.fromJson(v));
      });
    }
    if (json['meeting_categor'] != null) {
      meetingCategor = <PurposeMeeting>[];
      json['meeting_categor'].forEach((v) {
        meetingCategor!.add(new PurposeMeeting.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdBy'] = this.createdBy;
    data['createdById'] = this.createdById;
    data['createdDate'] = this.createdDate;
    data['recId'] = this.recId;
    if (this.purposeMeeting != null) {
      data['purpose_meeting'] =
          this.purposeMeeting!.map((v) => v.toJson()).toList();
    }
    if (this.requiredMeetin != null) {
      data['required_meetin'] =
          this.requiredMeetin!.map((v) => v.toJson()).toList();
    }
    if (this.ceoMeetingTyp != null) {
      data['ceo_meeting_typ'] =
          this.ceoMeetingTyp!.map((v) => v.toJson()).toList();
    }
    if (this.ceoPriority != null) {
      data['ceo_priority'] = this.ceoPriority!.map((v) => v.toJson()).toList();
    }
    if (this.meetingCategor != null) {
      data['meeting_categor'] =
          this.meetingCategor!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PurposeMeeting {
  String? namear;
  String? id;
  String? nameen;
  int? recid;

  PurposeMeeting({this.namear, this.id, this.nameen, this.recid});

  PurposeMeeting.fromJson(Map<String, dynamic> json) {
    namear = json['namear'];
    id = json['id'];
    nameen = json['nameen'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['namear'] = this.namear;
    data['id'] = this.id;
    data['nameen'] = this.nameen;
    data['recid'] = this.recid;
    return data;
  }
}
