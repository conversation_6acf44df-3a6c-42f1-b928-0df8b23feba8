class SubmitResponse {
  String createdBy;
  String createdById;
  String createdDate;
  int recId;
  MeetingParameters meetingParameters;
  dynamic groupsEmails;
  dynamic tendervalue;
  List<dynamic> nextapprovallis;
  dynamic guarantTypeId;
  dynamic projectNameAr;
  dynamic resultmessagear;
  String serviceKey;
  String requesternamear;
  dynamic periodDaysEn;
  String employeeid;
  String requeststatus;
  String requesternameen;
  dynamic costCenterId;
  String tableName;
  dynamic notes;
  dynamic resultmessageen;
  dynamic budgetTypeEn;
  dynamic guaranteevalue;
  dynamic requestDetails;
  String elmWebUrl;
  String authkey;
  dynamic withdrawflag;
  dynamic nxtAprNameAr;
  String userid;
  String requestTypeAr;
  String requestType;
  String mytypeid;
  dynamic projectNameId;
  dynamic nxtAprNameEn;
  String displayRecid;
  dynamic periodDaysId;
  dynamic periodDaysAr;
  List<Approvalproce> approvalproce;
  dynamic startdate;
  dynamic reqStatusAr;
  dynamic exception;
  String requesteremail;
  dynamic guaranteePerce;
  dynamic guarantTypeEn;
  dynamic rejectionreason;
  dynamic comments;
  dynamic customerId;
  dynamic rejectedbyar;
  List<dynamic> attachments;
  dynamic projectNameEn;
  dynamic taskWebUrl;
  dynamic costCenterAr;
  dynamic approvedByEn;
  String createddate;
  dynamic customerNameA;
  dynamic withdrawreason;
  dynamic approvedByAr;
  dynamic customerNameE;
  dynamic code;
  dynamic budgetTypeAr;
  dynamic reqCardData;
  dynamic rejectedbyen;
  dynamic guarantTypeAr;
  String pendingRequest;
  dynamic resultmessage;
  dynamic costCenterEn;

  SubmitResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.meetingParameters,
    required this.groupsEmails,
    required this.tendervalue,
    required this.nextapprovallis,
    required this.guarantTypeId,
    required this.projectNameAr,
    required this.resultmessagear,
    required this.serviceKey,
    required this.requesternamear,
    required this.periodDaysEn,
    required this.employeeid,
    required this.requeststatus,
    required this.requesternameen,
    required this.costCenterId,
    required this.tableName,
    required this.notes,
    required this.resultmessageen,
    required this.budgetTypeEn,
    required this.guaranteevalue,
    required this.requestDetails,
    required this.elmWebUrl,
    required this.authkey,
    required this.withdrawflag,
    required this.nxtAprNameAr,
    required this.userid,
    required this.requestTypeAr,
    required this.requestType,
    required this.mytypeid,
    required this.projectNameId,
    required this.nxtAprNameEn,
    required this.displayRecid,
    required this.periodDaysId,
    required this.periodDaysAr,
    required this.approvalproce,
    required this.startdate,
    required this.reqStatusAr,
    required this.exception,
    required this.requesteremail,
    required this.guaranteePerce,
    required this.guarantTypeEn,
    required this.rejectionreason,
    required this.comments,
    required this.customerId,
    required this.rejectedbyar,
    required this.attachments,
    required this.projectNameEn,
    required this.taskWebUrl,
    required this.costCenterAr,
    required this.approvedByEn,
    required this.createddate,
    required this.customerNameA,
    required this.withdrawreason,
    required this.approvedByAr,
    required this.customerNameE,
    required this.code,
    required this.budgetTypeAr,
    required this.reqCardData,
    required this.rejectedbyen,
    required this.guarantTypeAr,
    required this.pendingRequest,
    required this.resultmessage,
    required this.costCenterEn,
  });

  factory SubmitResponse.fromJson(Map<String, dynamic> json) => SubmitResponse(
    createdBy: json["createdBy"],
    createdById: json["createdById"],
    createdDate: json["createdDate"],
    recId: json["recId"],
    meetingParameters: MeetingParameters.fromJson(json["meetingParameters"]),
    groupsEmails: json["groups_emails"],
    tendervalue: json["tendervalue"],
    nextapprovallis: List<dynamic>.from(json["nextapprovallis"].map((x) => x)),
    guarantTypeId: json["guarant_type_id"],
    projectNameAr: json["project_name_ar"],
    resultmessagear: json["resultmessagear"],
    serviceKey: json["service_key"],
    requesternamear: json["requesternamear"],
    periodDaysEn: json["period_days_en"],
    employeeid: json["employeeid"],
    requeststatus: json["requeststatus"],
    requesternameen: json["requesternameen"],
    costCenterId: json["cost_center_id"],
    tableName: json["table_name"],
    notes: json["notes"],
    resultmessageen: json["resultmessageen"],
    budgetTypeEn: json["budget_type_en"],
    guaranteevalue: json["guaranteevalue"],
    requestDetails: json["request_details"],
    elmWebUrl: json["elm_web_url"],
    authkey: json["authkey"],
    withdrawflag: json["withdrawflag"],
    nxtAprNameAr: json["nxt_apr_name_ar"],
    userid: json["userid"],
    requestTypeAr: json["request_type_ar"],
    requestType: json["request_type"],
    mytypeid: json["mytypeid"],
    projectNameId: json["project_name_id"],
    nxtAprNameEn: json["nxt_apr_name_en"],
    displayRecid: json["display_recid"],
    periodDaysId: json["period_days_id"],
    periodDaysAr: json["period_days_ar"],
    approvalproce: List<Approvalproce>.from(json["approvalproce"].map((x) => Approvalproce.fromJson(x))),
    startdate: json["startdate"],
    reqStatusAr: json["req_status_ar"],
    exception: json["exception"],
    requesteremail: json["requesteremail"],
    guaranteePerce: json["guarantee_perce"],
    guarantTypeEn: json["guarant_type_en"],
    rejectionreason: json["rejectionreason"],
    comments: json["comments"],
    customerId: json["customer_id"],
    rejectedbyar: json["rejectedbyar"],
    attachments: List<dynamic>.from(json["attachments"].map((x) => x)),
    projectNameEn: json["project_name_en"],
    taskWebUrl: json["task_web_url"],
    costCenterAr: json["cost_center_ar"],
    approvedByEn: json["approved_by_en"],
    createddate: json["createddate"],
    customerNameA: json["customer_name_a"],
    withdrawreason: json["withdrawreason"],
    approvedByAr: json["approved_by_ar"],
    customerNameE: json["customer_name_e"],
    code: json["code"],
    budgetTypeAr: json["budget_type_ar"],
    reqCardData: json["req_card_data"],
    rejectedbyen: json["rejectedbyen"],
    guarantTypeAr: json["guarant_type_ar"],
    pendingRequest: json["pending_request"],
    resultmessage: json["resultmessage"],
    costCenterEn: json["cost_center_en"],
  );

  Map<String, dynamic> toJson() => {
    "createdBy": createdBy,
    "createdById": createdById,
    "createdDate": createdDate,
    "recId": recId,
    "meetingParameters": meetingParameters.toJson(),
    "groups_emails": groupsEmails,
    "tendervalue": tendervalue,
    "nextapprovallis": List<dynamic>.from(nextapprovallis.map((x) => x)),
    "guarant_type_id": guarantTypeId,
    "project_name_ar": projectNameAr,
    "resultmessagear": resultmessagear,
    "service_key": serviceKey,
    "requesternamear": requesternamear,
    "period_days_en": periodDaysEn,
    "employeeid": employeeid,
    "requeststatus": requeststatus,
    "requesternameen": requesternameen,
    "cost_center_id": costCenterId,
    "table_name": tableName,
    "notes": notes,
    "resultmessageen": resultmessageen,
    "budget_type_en": budgetTypeEn,
    "guaranteevalue": guaranteevalue,
    "request_details": requestDetails,
    "elm_web_url": elmWebUrl,
    "authkey": authkey,
    "withdrawflag": withdrawflag,
    "nxt_apr_name_ar": nxtAprNameAr,
    "userid": userid,
    "request_type_ar": requestTypeAr,
    "request_type": requestType,
    "mytypeid": mytypeid,
    "project_name_id": projectNameId,
    "nxt_apr_name_en": nxtAprNameEn,
    "display_recid": displayRecid,
    "period_days_id": periodDaysId,
    "period_days_ar": periodDaysAr,
    "approvalproce": List<dynamic>.from(approvalproce.map((x) => x.toJson())),
    "startdate": startdate,
    "req_status_ar": reqStatusAr,
    "exception": exception,
    "requesteremail": requesteremail,
    "guarantee_perce": guaranteePerce,
    "guarant_type_en": guarantTypeEn,
    "rejectionreason": rejectionreason,
    "comments": comments,
    "customer_id": customerId,
    "rejectedbyar": rejectedbyar,
    "attachments": List<dynamic>.from(attachments.map((x) => x)),
    "project_name_en": projectNameEn,
    "task_web_url": taskWebUrl,
    "cost_center_ar": costCenterAr,
    "approved_by_en": approvedByEn,
    "createddate": createddate,
    "customer_name_a": customerNameA,
    "withdrawreason": withdrawreason,
    "approved_by_ar": approvedByAr,
    "customer_name_e": customerNameE,
    "code": code,
    "budget_type_ar": budgetTypeAr,
    "req_card_data": reqCardData,
    "rejectedbyen": rejectedbyen,
    "guarant_type_ar": guarantTypeAr,
    "pending_request": pendingRequest,
    "resultmessage": resultmessage,
    "cost_center_en": costCenterEn,
  };
}

class Approvalproce {
  Template template;
  dynamic predecessors;
  int taskSeq;
  dynamic newPlannedStartDate;
  dynamic description;
  String taskTypeName;
  String plannedEndDate;
  int duration;
  int taskTypeId;
  dynamic parentSequence;
  int urgency;
  int processId;
  String processName;
  dynamic ccList;
  dynamic dueDate;
  dynamic actualCost;
  int recid;
  dynamic msPriorityValue;
  bool noWait;
  dynamic minStartDate;
  dynamic ccListId;
  String plannedStartDate;
  int plannedEfforts;
  List<dynamic> assignedEmpsGrpsToTask;
  int primeId;
  String taskName;
  int typeId;
  dynamic primeTypeId;
  dynamic assignee;
  int recId;
  int objectid;

  Approvalproce({
    required this.template,
    required this.predecessors,
    required this.taskSeq,
    required this.newPlannedStartDate,
    required this.description,
    required this.taskTypeName,
    required this.plannedEndDate,
    required this.duration,
    required this.taskTypeId,
    required this.parentSequence,
    required this.urgency,
    required this.processId,
    required this.processName,
    required this.ccList,
    required this.dueDate,
    required this.actualCost,
    required this.recid,
    required this.msPriorityValue,
    required this.noWait,
    required this.minStartDate,
    required this.ccListId,
    required this.plannedStartDate,
    required this.plannedEfforts,
    required this.assignedEmpsGrpsToTask,
    required this.primeId,
    required this.taskName,
    required this.typeId,
    required this.primeTypeId,
    required this.assignee,
    required this.recId,
    required this.objectid,
  });

  factory Approvalproce.fromJson(Map<String, dynamic> json) => Approvalproce(
    template: Template.fromJson(json["template"]),
    predecessors: json["predecessors"],
    taskSeq: json["taskSeq"],
    newPlannedStartDate: json["new_planned_start_date"],
    description: json["description"],
    taskTypeName: json["taskTypeName"],
    plannedEndDate: json["plannedEndDate"],
    duration: json["duration"],
    taskTypeId: json["taskTypeId"],
    parentSequence: json["parentSequence"],
    urgency: json["urgency"],
    processId: json["processId"],
    processName: json["processName"],
    ccList: json["ccList"],
    dueDate: json["due_Date"],
    actualCost: json["actual_Cost"],
    recid: json["RECID"],
    msPriorityValue: json["msPriorityValue"],
    noWait: json["noWait"],
    minStartDate: json["min_Start_Date"],
    ccListId: json["ccListID"],
    plannedStartDate: json["plannedStartDate"],
    plannedEfforts: json["plannedEfforts"],
    assignedEmpsGrpsToTask: List<dynamic>.from(json["assignedEmpsGrpsToTask"].map((x) => x)),
    primeId: json["PRIME_ID"],
    taskName: json["taskName"],
    typeId: json["typeId"],
    primeTypeId: json["PRIME_TYPE_ID"],
    assignee: json["assignee"],
    recId: json["recId"],
    objectid: json["objectid"],
  );

  Map<String, dynamic> toJson() => {
    "template": template.toJson(),
    "predecessors": predecessors,
    "taskSeq": taskSeq,
    "new_planned_start_date": newPlannedStartDate,
    "description": description,
    "taskTypeName": taskTypeName,
    "plannedEndDate": plannedEndDate,
    "duration": duration,
    "taskTypeId": taskTypeId,
    "parentSequence": parentSequence,
    "urgency": urgency,
    "processId": processId,
    "processName": processName,
    "ccList": ccList,
    "due_Date": dueDate,
    "actual_Cost": actualCost,
    "RECID": recid,
    "msPriorityValue": msPriorityValue,
    "noWait": noWait,
    "min_Start_Date": minStartDate,
    "ccListID": ccListId,
    "plannedStartDate": plannedStartDate,
    "plannedEfforts": plannedEfforts,
    "assignedEmpsGrpsToTask": List<dynamic>.from(assignedEmpsGrpsToTask.map((x) => x)),
    "PRIME_ID": primeId,
    "taskName": taskName,
    "typeId": typeId,
    "PRIME_TYPE_ID": primeTypeId,
    "assignee": assignee,
    "recId": recId,
    "objectid": objectid,
  };
}

class Template {
  int createdById;
  String createdBy;
  int createdDate;
  String companyName;
  String uuid;
  int recId;
  String templateName;
  int typeId;

  Template({
    required this.createdById,
    required this.createdBy,
    required this.createdDate,
    required this.companyName,
    required this.uuid,
    required this.recId,
    required this.templateName,
    required this.typeId,
  });

  factory Template.fromJson(Map<String, dynamic> json) => Template(
    createdById: json["createdById"],
    createdBy: json["createdBy"],
    createdDate: json["createdDate"],
    companyName: json["companyName"],
    uuid: json["uuid"],
    recId: json["recId"],
    templateName: json["templateName"],
    typeId: json["typeId"],
  );

  Map<String, dynamic> toJson() => {
    "createdById": createdById,
    "createdBy": createdBy,
    "createdDate": createdDate,
    "companyName": companyName,
    "uuid": uuid,
    "recId": recId,
    "templateName": templateName,
    "typeId": typeId,
  };
}

class MeetingParameters {
  MeetingParameters();

  factory MeetingParameters.fromJson(Map<String, dynamic> json) => MeetingParameters(
  );

  Map<String, dynamic> toJson() => {
  };
}




class LetterOfGuaranteeBody{
  String? budgetType;
  String? tenderValue;
  String? startDate;
  String? guaranteeValue;
  String? comments;
  String? projectNameId;
  String? periodDaysId;
  String? guaranteePercent;
  String? guarantTypeId;
  String? costCenterId;
  String? customerId;

  LetterOfGuaranteeBody({
    this.budgetType,
    this.tenderValue,
    this.startDate,
    this.guaranteeValue,
    this.comments,
    this.projectNameId,
    this.periodDaysId,
    this.guaranteePercent,
    this.guarantTypeId,
    this.costCenterId,
    this.customerId,
  });
  Map<String,dynamic> toJson()=>{
    "budget_type_en": budgetType,
    "tendervalue": tenderValue,
    "startdate": startDate,
    "guaranteevalue": guaranteeValue,
    "comments": comments,
    "project_name_id": projectNameId,
    "period_days_id": periodDaysId,
    "guarantee_perce": guaranteePercent,
    "guarant_type_id": guarantTypeId,
    "cost_center_id":costCenterId,
    "customer_id":customerId
  };
}