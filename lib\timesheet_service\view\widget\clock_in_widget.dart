import 'package:eeh/timesheet_service/models/retrieve_emp_weekly_records_response.dart';
import 'package:eeh/timesheet_service/view/widget/day_time_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import 'close_button_widget.dart';
import 'missing_hours/total_missing_hours.dart';

class ClockInWidget extends StatelessWidget {
  final List<BiometricEntry> biometricEntries;
  const ClockInWidget({
    super.key,
    required this.biometricEntries,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.sp, vertical: 8.sp),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          TotalMissingHours(
            body: AppLocalizations.of(context).translate(
                "time_sheet_total_details_desc"),
            bodyColor: secondTextColor,
            image: "fa-solid fa-right-to-bracket",
            imageBackgroundColor: Color.fromRGBO(255, 244, 238, 1),
            title: AppLocalizations.of(context).translate(
                "${AppLocalizations.of(context).translate('Clock in')} - (${biometricEntries.isEmpty ? '' : DateFormat('EEE, d MMM').format(DateTime.tryParse(biometricEntries[0].dayDate ?? '') ?? DateTime.now())})"),
            titleColor: secondTextColor,
            iconColor: primaryColor,
            hasDivider: true,
          ),
          Expanded(
            child: ListView.separated(
              separatorBuilder: (context, index) => Divider(
                thickness: 1.5.sp,
                color: dividerColor,
              ),
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return IntrinsicHeight(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: DayAndTimeWidget(
                          hasIcon: false,
                          title: AppLocalizations.of(context)
                              .translate('start_time'),
                          value: biometricEntries[index].startTime ?? "00:00",
                          isDemmed: true,
                          isTitleDemmed: true,
                          hasDivider: true,
                          dividerEndIndent: 25.w,
                        ),
                      ),
                      Expanded(
                        child: DayAndTimeWidget(
                          hasIcon: false,
                          title: AppLocalizations.of(context)
                              .translate('end_time'),
                          value: biometricEntries[index].endTime ?? "00:00",
                          isDemmed: true,
                          isTitleDemmed: true,
                          hasDivider: true,
                          dividerEndIndent: 25.w,
                        ),
                      ),
                      Expanded(
                        child: DayAndTimeWidget(
                          hasIcon: false,
                          title: AppLocalizations.of(context)
                              .translate('total_time'),
                          value: biometricEntries[index].totalTime ?? "00:00",
                          isDemmed: true,
                          isTitleDemmed: true,
                          hasDivider: true,
                          dividerEndIndent: 25.w,
                        ),
                      ),
                    ],
                  ),
                );
              },
              itemCount: biometricEntries.length,
            ),
          ),
          CloseButtonWidget()
        ],
      ),
    );
  }
}
