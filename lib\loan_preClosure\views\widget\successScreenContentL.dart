
import 'package:eeh/loan_preClosure/model/submit_request_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/widgets/sar_component.dart';
import '../../../success_view/success_screen_view.dart';

class SuccessScreenContentWidgetL extends StatelessWidget {
  final SubmitRequestResponse? submitResponse;
  const SuccessScreenContentWidgetL(
      {super.key, required this.submitResponse});
  String formatDate(String rawDate) {
    try {
      final DateTime parsedDate = DateTime.parse(rawDate);
      return DateFormat('dd MMM, yyyy').format(parsedDate);
    } catch (e) {
      return rawDate; // fallback if parse fails
    }
  }
  @override
  Widget build(BuildContext context) {
    return submitResponse == null
        ? SizedBox.shrink()
        : SingleChildScrollView(
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderText(AppLocalizations.of(context).translate('Request Type')),
            buildDetailsText(submitResponse?.requestType ?? ' '),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate('Request ID')),
            buildDetailsText(submitResponse?.display_recId.toString() ?? ''),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate("Loan Pre-Closure Amount")),
            Row(children: [  buildDetailsText(submitResponse?.remainingAmount.toString() ?? ''), Padding(
              padding:  EdgeInsets.only(right: 6.w , left: 6.w),
              child: SARComponent(
                color: secondTextColor,
              ),
            ),],),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate("Start Date")),
            buildDetailsText(formatDate(submitResponse?.startDate ?? '')),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate("End Date")),
            buildDetailsText(formatDate(submitResponse?.endDate ?? '')),
            buildDivider(),

          ],
        ),
      ),
    );
  }
}
