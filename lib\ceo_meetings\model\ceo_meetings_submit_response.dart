class CEOMeetingsSubmitResponse {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final Map<String, dynamic>? meetingParameters;
  final String? authkey;
  final String? purposeMeetEn;
  final String? purposeMeetAr;
  final String? purposeMeetId;
  final String? mRequesterAr;
  final String? mRequesterEn;
  final String? mRequesterId;
  final String? noteTakerEn;
  final String? noteTakerAr;
  final String? noteTakerId;
  final String? endTime;
  final String? startTime;
  final String? agenda;
  final String? meetingSubject;
  final String? meetingDate;
  final String? meetingTypeId;
  final String? meetingTypeEn;
  final String? meetingTypeAr;
  final String? requiredMeetId;
  final String? requiredMeetEn;
  final String? requiredMeetAr;
  final String? requestType;
  final String? requestTypeAr;
  final String? requestStatus;
  final String? resultMessage;
  final String? resultMessageEn;
  final String? resultMessageAr;
  final String? displayRecid;
  final String? serviceKey;
  final String? tableName;
  final String? requesterEmail;
  final String? requesterNameEn;
  final String? requesterNameAr;
  final String? code;
  final String? rejectionReason;
  final String? withdrawReason;
  final bool? withdrawFlag;
  final String? exception;
  final String? notes;
  final String? approvedByAr;
  final String? approvedByEn;
  final String? rejectedByAr;
  final String? rejectedByEn;
  final String? priorityId;
  final String? priorityEn;
  final String? priorityAr;
  final String? meetCategoryId;
  final String? meetCategoryEn;
  final String? meetCategoryAr;
  final String? reqStatusAr;
  final String? groupsEmails;
  final String? groupId;
  final String? ceoGroupId;
  final String? ceoGroupAr;
  final String? ceoGroupEn;
  final String? taskWebUrl;
  final String? elmWebUrl;
  final String? createdDateIso;
  final String? myTypeId;
  final String? employeeId;
  final String? pendingRequest;
  final String? whatDoWeExpect;
  final String? nextApprovalNameEn;
  final String? nextApprovalNameAr;
  final bool? relatedBoardD;

  final List<Attachment>? attachments;
  final List<EmployeeRef>? meetingAttendees;
  final List<EmployeeRef>? ceoGroups;
  final List<ApprovalProcess>? approvalProcess;
  final List<dynamic>? meetingRequest;

  CEOMeetingsSubmitResponse({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.authkey,
    this.meetingParameters,
    this.relatedBoardD,
    this.attachments,
    this.meetingAttendees,
    this.ceoGroups,
    this.approvalProcess,
    this.meetingRequest,
    this.mRequesterAr,
    this.mRequesterEn,
    this.mRequesterId,
    this.noteTakerEn,
    this.noteTakerAr,
    this.noteTakerId,
    this.endTime,
    this.startTime,
    this.agenda,
    this.meetingSubject,
    this.meetingDate,
    this.meetingTypeId,
    this.meetingTypeEn,
    this.meetingTypeAr,
    this.requiredMeetId,
    this.requiredMeetEn,
    this.requiredMeetAr,
    this.requestType,
    this.requestTypeAr,
    this.requestStatus,
    this.resultMessage,
    this.resultMessageEn,
    this.resultMessageAr,
    this.displayRecid,
    this.serviceKey,
    this.tableName,
    this.requesterEmail,
    this.requesterNameEn,
    this.requesterNameAr,
    this.code,
    this.rejectionReason,
    this.withdrawReason,
    this.withdrawFlag,
    this.exception,
    this.notes,
    this.approvedByAr,
    this.approvedByEn,
    this.rejectedByAr,
    this.rejectedByEn,
    this.priorityId,
    this.priorityEn,
    this.priorityAr,
    this.meetCategoryId,
    this.meetCategoryEn,
    this.meetCategoryAr,
    this.reqStatusAr,
    this.groupsEmails,
    this.groupId,
    this.ceoGroupId,
    this.ceoGroupAr,
    this.ceoGroupEn,
    this.taskWebUrl,
    this.elmWebUrl,
    this.createdDateIso,
    this.myTypeId,
    this.employeeId,
    this.pendingRequest,
    this.whatDoWeExpect,
    this.nextApprovalNameEn,
    this.nextApprovalNameAr,
    this.purposeMeetAr,
    this.purposeMeetEn,
    this.purposeMeetId,
  });

  factory CEOMeetingsSubmitResponse.fromJson(Map<String, dynamic> json) {
    return CEOMeetingsSubmitResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      authkey: json['authkey'],
      meetingParameters: json['meetingParameters'] ?? {},
      relatedBoardD: json['related_board_d'].toString() == 'true',
      mRequesterAr: json['m_requester_ar'],
      mRequesterEn: json['m_requester_en'],
      mRequesterId: json['m_requester_id'],
      noteTakerEn: json['note_taker_en'],
      noteTakerAr: json['note_taker_ar'],
      noteTakerId: json['note_taker_id'],
      endTime: json['end_time'],
      startTime: json['start_time'],
      agenda: json['agenda'],
      meetingSubject: json['meeting_subject'],
      meetingDate: json['meeting_date'],
      meetingTypeId: json['meeting_type_id'],
      meetingTypeEn: json['meeting_type_en'],
      meetingTypeAr: json['meeting_type_ar'],
      requiredMeetId: json['required_meetid'],
      requiredMeetEn: json['required_meeten'],
      requiredMeetAr: json['required_meetar'],
      requestType: json['request_type'],
      requestTypeAr: json['request_type_ar'],
      requestStatus: json['requeststatus'],
      resultMessage: json['resultmessage'],
      resultMessageEn: json['resultmessageen'],
      resultMessageAr: json['resultmessagear'],
      displayRecid: json['display_recid'],
      serviceKey: json['service_key'],
      tableName: json['table_name'],
      requesterEmail: json['requesteremail'],
      requesterNameEn: json['requesternameen'],
      requesterNameAr: json['requesternamear'],
      code: json['code'],
      rejectionReason: json['rejectionreason'],
      withdrawReason: json['withdrawreason'],
      withdrawFlag: json['withdrawflag'],
      exception: json['exception'],
      notes: json['notes'],
      approvedByAr: json['approved_by_ar'],
      approvedByEn: json['approved_by_en'],
      rejectedByAr: json['rejectedbyar'],
      rejectedByEn: json['rejectedbyen'],
      priorityId: json['priority_id'],
      priorityEn: json['priority_en'],
      priorityAr: json['priority_ar'],
      meetCategoryId: json['meet_categoryid'],
      meetCategoryEn: json['meet_categoryen'],
      meetCategoryAr: json['meet_categoryar'],
      reqStatusAr: json['req_status_ar'],
      groupsEmails: json['groups_emails'],
      groupId: json['group_id'],
      ceoGroupId: json['ceo_group_id'],
      ceoGroupAr: json['ceo_group_ar'],
      ceoGroupEn: json['ceo_group_en'],
      taskWebUrl: json['task_web_url'],
      elmWebUrl: json['elm_web_url'],
      createdDateIso: json['createddate'],
      myTypeId: json['mytypeid'],
      employeeId: json['employeeid'],
      pendingRequest: json['pending_request'],
      whatDoWeExpect: json['what_do_we_expe'],
      nextApprovalNameEn: json['nxt_apr_name_en'],
      nextApprovalNameAr: json['nxt_apr_name_ar'],
      purposeMeetAr: json['purpose_meet_ar'],
      purposeMeetEn: json['purpose_meet_en'],
      purposeMeetId: json['purpose_meet_id'],
      attachments: List<Attachment>.from(
        (json['attachments'] ?? []).map((e) => Attachment.fromJson(e)),
      ),
      meetingAttendees: List<EmployeeRef>.from(
        (json['meeting_attende'] ?? []).map((e) => EmployeeRef.fromJson(e)),
      ),
      ceoGroups: List<EmployeeRef>.from(
        (json['ceo_groups'] ?? []).map((e) => EmployeeRef.fromJson(e)),
      ),
      approvalProcess: List<ApprovalProcess>.from(
        (json['approvalproce'] ?? []).map((e) => ApprovalProcess.fromJson(e)),
      ),
      meetingRequest: json['meeting_request'] ?? [],
    );
  }
}

class EmployeeRef {
  final String? employeeId;
  final int? recid;

  EmployeeRef({this.employeeId, this.recid});

  factory EmployeeRef.fromJson(Map<String, dynamic> json) {
    return EmployeeRef(
      employeeId: json['employeeid'],
      recid: json['recid'],
    );
  }
}

class Attachment {
  Attachment(); // Placeholder — expand if attachments have content

  factory Attachment.fromJson(Map<String, dynamic> json) {
    return Attachment(); // Adapt if structure known
  }
}

class ApprovalProcess {
  final Template? template;
  final int? taskSeq;
  final String? taskTypeName;
  final String? plannedStartDate;
  final String? plannedEndDate;
  final int? duration;

  ApprovalProcess({
    this.template,
    this.taskSeq,
    this.taskTypeName,
    this.plannedStartDate,
    this.plannedEndDate,
    this.duration,
  });

  factory ApprovalProcess.fromJson(Map<String, dynamic> json) {
    return ApprovalProcess(
      template: Template.fromJson(json['template']),
      taskSeq: json['taskSeq'],
      taskTypeName: json['taskTypeName'],
      plannedStartDate: json['plannedStartDate'],
      plannedEndDate: json['plannedEndDate'],
      duration: json['duration'],
    );
  }
}

class Template {
  final int? recId;
  final String? templateName;
  final String? createdBy;
  final int? createdById;
  final String? companyName;
  final int? createdDate;

  Template({
    this.recId,
    this.templateName,
    this.createdBy,
    this.createdById,
    this.companyName,
    this.createdDate,
  });

  factory Template.fromJson(Map<String, dynamic> json) {
    return Template(
      recId: json['recId'],
      templateName: json['templateName'],
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      companyName: json['companyName'],
      createdDate: json['createdDate'],
    );
  }
}
