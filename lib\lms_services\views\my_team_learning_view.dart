import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/lms_services/utils/lms_utils.dart';
import 'package:eeh/lms_services/widgets/course_card.dart';
import 'package:eeh/lms_services/widgets/info_card.dart';
import 'package:eeh/lms_services/widgets/year_filter_field.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../../shared/favorite_component/bloc/favorite_event.dart';

class MyTeamLearningView extends StatefulWidget {
  const MyTeamLearningView({super.key});
  @override
  State<MyTeamLearningView> createState() => _MyTeamLearningViewState();
}

class _MyTeamLearningViewState extends State<MyTeamLearningView> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
      child: Scaffold(
        appBar: getAppbarWidget(),
        body: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomScrollView(
                slivers: [
                  getMyDashboardCardInfo(context),
                  getYearFilterField(context),
                  getSpace(),
                  // getStatisticsCards(context),
                  getSpace(),
                  getEmployeeCardInfo(context),
                  getSpace(),
                  // getCoursesList(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // SliverList getCoursesList() {
  //   return SliverList(delegate: SliverChildBuilderDelegate(
  //                 (context, index) => CourseCard(progress: 0.0),
  //                 childCount: 10,
  //               ));
  // }

  SliverToBoxAdapter getEmployeeCardInfo(BuildContext context) {
    return SliverToBoxAdapter(
                  child: InfoCard(
                    title: AppLocalizations.of(context).translate("Courses & Certificates"),
                    icon: getIconFromCss("fa-solid fa-graduation-cap"),
                  ),
                );
  }

  SliverToBoxAdapter getSpace() {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 16.h,
      ),
    );
  }

  // SliverGrid getStatisticsCards(BuildContext context) {
  //   return SliverGrid(
  //     delegate: SliverChildBuilderDelegate(
  //       (context, index) {
  //         return LmsUtils.getStatisticsCards(context)[index];
  //       },
  //       childCount: LmsUtils.getStatisticsCards(context).length,
  //     ),
  //     gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //       crossAxisCount: 2,
  //       mainAxisSpacing: 16,
  //       crossAxisSpacing: 16,
  //       childAspectRatio: 1.9,
  //     ),
  //   );
  // }

  SliverToBoxAdapter getYearFilterField(BuildContext context) {
    return SliverToBoxAdapter(
      child: YearFilterField(
        label: AppLocalizations.of(context).translate("Filter by year"),
        initialYear: 2005,
        onYearSelected: (year) {
          print(year);
        },
      ),
    );
  }

  SliverToBoxAdapter getMyDashboardCardInfo(BuildContext context) {
    return SliverToBoxAdapter(
      child: InfoCard(
        title: AppLocalizations.of(context).translate("My Dashboard"),
        icon: getIconFromCss("fa-regular fa-chart-pie-simple"),
      ),
    );
  }

  PreferredSize getAppbarWidget() {
    return PreferredSize(
        preferredSize: Size.fromHeight(56.h),
        child: PreferredSize(
          preferredSize: Size.fromHeight(56.h),
          child: FavoriteComponent(
            id: "30",
            title: AppLocalizations.of(context).translate("My Learning"),
            hasSearchIcon: true,
          ),
        ));
  }
}
