import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/lms_services/utils/lms_utils.dart';
import 'package:eeh/lms_services/widgets/course_card.dart';
import 'package:eeh/lms_services/widgets/info_card.dart';
import 'package:eeh/lms_services/widgets/year_filter_field.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:eeh/shared/widgets/custom_tab_bar.dart';
import 'package:eeh/shared/widgets/custom_topBar_tab.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

import '../../shared/favorite_component/bloc/favorite_event.dart';

class MyTeamLearningView extends StatefulWidget {
  const MyTeamLearningView({super.key});
  @override
  State<MyTeamLearningView> createState() => _MyTeamLearningViewState();
}

class _MyTeamLearningViewState extends State<MyTeamLearningView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  @override
  void initState() {
    _tabController = TabController(length: 2, initialIndex: 0, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> tabs = [
      CustomTopBarTab(
        title: AppLocalizations.of(context).translate('Direct Reporting'),
      ),
      CustomTopBarTab(
        title: AppLocalizations.of(context).translate('All'),
      ),
    ];
    return BlocProvider(
      create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
      child: Scaffold(
        appBar: getAppbarWidget(),
        body: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: CustomScrollView(
                slivers: [
                  getMyTeamDashboardCardInfo(context),
                  // getYearFilterField(context),
                  getSpace(),
                  SliverToBoxAdapter(
                    child: CustomTabBar(
                      controller: _tabController,
                      tabs: tabs,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          getEmployeeCardInfo(context),
                          getEmployeeCardInfo(context),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // SliverList getCoursesList() {
  //   return SliverList(delegate: SliverChildBuilderDelegate(
  //                 (context, index) => CourseCard(progress: 0.0),
  //                 childCount: 10,
  //               ));
  // }

  getEmployeeCardInfo(BuildContext context) {
    return InfoCard(
      title: AppLocalizations.of(context).translate("Courses & Certificates"),
      icon: getIconFromCss("fa-solid fa-graduation-cap"),
    );
  }

  SliverToBoxAdapter getSpace() {
    return SliverToBoxAdapter(
      child: SizedBox(
        height: 16.h,
      ),
    );
  }

  // SliverGrid getStatisticsCards(BuildContext context) {
  //   return SliverGrid(
  //     delegate: SliverChildBuilderDelegate(
  //       (context, index) {
  //         return LmsUtils.getStatisticsCards(context)[index];
  //       },
  //       childCount: LmsUtils.getStatisticsCards(context).length,
  //     ),
  //     gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //       crossAxisCount: 2,
  //       mainAxisSpacing: 16,
  //       crossAxisSpacing: 16,
  //       childAspectRatio: 1.9,
  //     ),
  //   );
  // }

SliverToBoxAdapter getYearFilterField(BuildContext context) {
    return SliverToBoxAdapter(
      child: YearFilterField(
        label: AppLocalizations.of(context).translate("Filter by year"),
        // initialYear: null,
        onYearSelected: (year) {
          print(year);
        },
      ),
    );
  }

  SliverToBoxAdapter getMyTeamDashboardCardInfo(BuildContext context) {
    return SliverToBoxAdapter(
      child: InfoCard(
        title: AppLocalizations.of(context).translate("My Team Dashboard"),
        icon: getIconFromCss("fa-regular fa-chart-pie-simple"),
      ),
    );
  }

  PreferredSize getAppbarWidget() {
    return PreferredSize(
        preferredSize: Size.fromHeight(56.h),
        child: PreferredSize(
          preferredSize: Size.fromHeight(56.h),
          child: FavoriteComponent(
            id: "30",
            title: AppLocalizations.of(context).translate("My Team Learning"),
            hasSearchIcon: true,
          ),
        ));
  }
}
