import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../l10n/app_localizations.dart';
import '../style.dart';
import '../styles/colors.dart';
import '../styles/styles.dart';
import 'new_textformfield_component.dart';

class CustomCommentsWidget extends StatelessWidget {
  final TextEditingController? commentController;
  final bool isMandatory;
  final bool isDemmed;
  final Function(String)? onChange;
  const CustomCommentsWidget({
    super.key,
    this.commentController,
    this.onChange,
    this.isDemmed = false,
    this.isMandatory = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(context),
        _buildCommentTextField(context),
      ],
    );
  }

  Widget _buildLabel(BuildContext context) {
    return Text.rich(
      TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context).translate('Comments'),
            style:isDemmed
                ? labelLarge!.copyWith(
                color: disableTextColor,
                fontSize: 16.sp
            )
                : labelLarge!.copyWith(

                fontSize: 16.sp
            ),
          ),
          if (isMandatory)
            TextSpan(
              text: ' *',
              style: mandatoryStyle,
            ),
        ],
      ),
    );
  }

  Widget _buildCommentTextField(BuildContext context) {
    return NewCustomTextFieldComponent(
      type: TextFieldType.note,
      onChange: onChange,
      validationText: isMandatory
          ? AppLocalizations.of(context).translate("Please enter Comments")
          : null,
      hintText: AppLocalizations.of(context).translate("Write a Comments"),

      controller: commentController,
    );
  }
}
