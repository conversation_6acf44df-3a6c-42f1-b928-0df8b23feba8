import 'dart:convert';
import 'package:eeh/shared/app_constants.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../service_details_revamp/details_layout_revamp.dart';
import '../work_widget/work_item_layout.dart';

class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _localNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future initialize() async {
    const InitializationSettings initializationSettings =
        InitializationSettings(
            android: AndroidInitializationSettings('@mipmap/ic_launcher'),
            iOS: DarwinInitializationSettings());

    await _localNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (response) async {
        await onNotificationClick(response);
      },
    );
  }

  static Future cancelAll() async {
    await _localNotificationsPlugin.cancelAll();
  }

  static Future showNotification(RemoteMessage event) async {
    AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      event.data['recId'] ?? '',
      event.data['requesttype'] ?? 'Business Card',
      importance: Importance.max,
      priority: Priority.high,
    );
    var iOSPlatformChannelSpecifics = const DarwinNotificationDetails();
    NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );
    if (event.notification?.body != null && event.notification?.title != null) {
      await _localNotificationsPlugin.show(
          int.parse(event.data['recId']),
          event.notification?.title,
          event.notification?.body,
          platformChannelSpecifics,
          payload: jsonEncode(event.data));
    }
  }

  static Future onNotificationClick(NotificationResponse event) async {
    Map<String, dynamic> notificationData = jsonDecode(event.payload!);
    int? taskId = int.tryParse(notificationData['taskId']);
    String? taskTableName = notificationData['taskTableName'];

    int? requestId = int.tryParse(notificationData['recId']);
    String? requestType = notificationData['requesttype'];
    if (requestType == null) {
      requestType = notificationData['udaTableName'];
    }
    String? notificationreason = notificationData['notificationreason'];

    bool isRequest = false;

    if (requestId != null && taskId != null && taskId != 0) {
      isRequest = false;
    } else if (requestId != null && taskId == 0) {
      isRequest = true;
    }
    if (navigatorKey.currentWidget != null &&
        navigatorKey.currentContext != null) {
      Navigation.navigateToScreen(
          navigatorKey.currentContext!,
          DetailsLayoutRevamp(
            isNotification: true,
            taskTableName:taskTableName,
            notificationReason: notificationreason,
            workType:
                isRequest ? WorkType.requestDetails : WorkType.taskDetails,
            requestId: requestId.toString(),
            taskId: taskId.toString(),
            requestType: requestType ?? '',
          ));
    }
  }
}
