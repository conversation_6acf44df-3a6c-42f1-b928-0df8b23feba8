import 'package:bloc/bloc.dart';
import 'package:eeh/lms_services/models/get_lms_sheet_id_response_model.dart';
import 'package:eeh/lms_services/models/my_learning/get_my_learning_response_model.dart';
import 'package:eeh/lms_services/repo/lms_repo.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:equatable/equatable.dart';

part 'lms_event.dart';
part 'lms_state.dart';

class LmsBloc extends Bloc<LmsEvent, LmsState> {
  final ILMSRepo _repo;
  GetLmsSheetIdResponseModel? getLmsSheetIdResponseModel;
  MyLearningResponseModel? myLearningResponseModel;

  String? sheetId;
  LmsBloc({ILMSRepo? repo})
      : _repo = repo ?? LMSRepo.instance,
        super(LmsInitial()) {
    on<LmsGetSheetIdEvent>(_getLmsSheetId);
    on <LmsGetMyLearningEvent>(_getLmsMyLearning);
  }

Future<void> _getLmsMyLearning(
    LmsGetMyLearningEvent event, Emitter<LmsState> emit) async {
  emit(GetLmsMyLearningLoadingState());
  final response = await _repo.getMyLearning(sheetId: event.sheetId);
  response.maybeWhen(
    ok: (data) {
      try {
        final model = MyLearningResponseModel.fromJson(data);
        myLearningResponseModel = model;
        
        print("Total learning items: ${model.learningData.dashboardKpi.totalCount}");
        print("Completed items: ${model.learningData.dashboardKpi.completedCount}");
        print("Number of items: ${model.learningData.itemDetails.length}");
        
        for (final item in model.learningData.itemDetails) {
          print("${item.type}: ${item.title ?? 'No title'} - Status: ${item.status}");
        }
        
        emit(GetLmsMyLearningSuccessState(model));
      } catch (e) {
        print('Error parsing learning data: $e');
        emit(GetLmsMyLearningErrorState('Failed to parse learning data: $e'));
      }
    },
    onError: (error) {
      showMessage(error.toString(), MessageType.error);
      print('in onError in LMS GetLmsMyLearningErrorState: $error');
      emit(GetLmsMyLearningErrorState(error.toString()));
    },
  );
}

  Future<void> _getLmsSheetId(
      LmsGetSheetIdEvent event, Emitter<LmsState> emit) async {
    emit(GetLmsSheetIdLoadingState());
    final response = await _repo.getLmsSheetId(payload: const {
      "employee_id": "hr2013",
      "v_year": "2025",
    });
    response.maybeWhen(
      ok: (data) {
        print('in response.maybeWhen in LMS GetLmsSheetIdResponseModel Sheet ID: ${data['sheet_id']}');
        final model = GetLmsSheetIdResponseModel.fromJson(data);
        getLmsSheetIdResponseModel = model;
        sheetId = model.sheetId;
        print('GetLmsSheetIdResponseModel Sheet ID: ${model.sheetId}');
        emit(GetLmsSheetIdSuccessState(model));
      },
      onError: (error) {
        print('in onError in LMS GetLmsSheetIdResponseModel Sheet ID: $error');
        showMessage(error.toString(), MessageType.error);
        emit(GetLmsSheetIdErrorState(error.toString()));
      },
    );
  }
}
