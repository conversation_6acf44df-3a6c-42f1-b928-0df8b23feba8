import 'package:eeh/shared/work_widget/work_item_layout.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../l10n/app_localizations.dart';
import '../styles/colors.dart';
import '../utility/font_utility.dart';
import 'sar_component.dart';

class CurrencyDetailsItem extends StatelessWidget {
  CurrencyDetailsItem({
    required this.messageAR,
    required this.messageEN,
    required this.titleAR,
    required this.titleEN,
    this.workType,
  });

  final String messageAR;
  final String messageEN;
  final String titleAR;
  final String titleEN;
  WorkType? workType;

  double getDetailsTitleSize(WorkType? workType) {
    switch (workType) {
      case WorkType.requestDetailsDialog:
      case WorkType.taskDetailsDialog:
        return 12;
      default:
        return 12;
    }
  }

  double getDetailsMessageSize(WorkType? workType) {
    switch (workType) {
      case WorkType.requestDetailsDialog:
      case WorkType.taskDetailsDialog:
        return 12;
      default:
        return 12;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(6.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            AppLocalizations.appLang == 'en' ? titleEN : titleAR,
            style: FontUtilities.getTextStyle(TextType.regular,
                size: getDetailsMessageSize(workType),
                textColor: secondTextColor),
          ),
          Spacer(),
          Row(
            children: [
              Text(
                overflow: TextOverflow.ellipsis,
                AppLocalizations.appLang == 'en' ? messageEN : messageAR,
                style: FontUtilities.getTextStyle(TextType.regular,
                    size: getDetailsMessageSize(workType),
                    textColor: primaryDarkBlue,
                    fontWeight: FontWeight.w700),
              ),
              SizedBox(
                width: 2.w,
              ),
              SARComponent(
                size: getDetailsMessageSize(workType),
                color: primaryDarkBlue,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
