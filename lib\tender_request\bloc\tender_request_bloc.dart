
import 'package:eeh/tender_request/bloc/tender_request_event.dart';
import 'package:eeh/tender_request/bloc/tender_request_state.dart';
import 'package:eeh/tender_request/model/bank_model.dart';
import 'package:eeh/tender_request/model/submit_body_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../l10n/app_localizations.dart';
import '../../letter_of_guarantee/models/Lists.dart';
import '../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../shared/attachment_uda/bloc/attach_event.dart';
import '../../shared/attachment_uda/model/attachment_request_model.dart';
import '../../shared/network/api_helper.dart';
import '../../shared/widgets/customer_info_component/model/customer_Info_model.dart';
import '../../shared/widgets/toast.dart';

import '../model/projectWps_model.dart';
import '../model/static_data_model.dart';
import '../model/submit_tender_request_model.dart';
import '../model/tender_payment_model.dart';
//import '../model/tender_request_model.dart';
import '../repo/tender_request_repo.dart';
import '../../letter_of_guarantee/models/Lists.dart' as consts;


class TenderRequestBloc extends Bloc<TenderRequestEvent,TenderRequestState>{
  late TenderRequestRepo repo;
  TenderPaymentResponse? tenderPaymentResponse;
  List<TenderPayment> tenderPaymentList = [];
  TenderPayment? listPyment ;
  ProjectWpsResponse? projectWpsResponse;
  List<ProjectWps> filterListProject =[];
  ProjectWps? selectedProjectWps;
  BankListResponse? bankListResponse;
  List<BankModel> bankList =[];
  BankModel? listbank;
  CostCenter? costCenter;
  CustomerInfoResponse? customerInfoResponse;
  CustomerInfo? customerInfo;
  List<CustomerInfo> customerInfoList=[];
  AttachmentRequestBody attachmentRequestBody =
  AttachmentRequestBody(attachmentUdaId: '', objectId: '');
  SubmitTenderRequestModel? submitTenderRequestModel;
  List<String> paymentRequiredFields = [];
  BudgetTypeModel? selectedBudgetTypeModel = departmentList.first;
  TenderPayment? selectedTenderPaymentModel;
  bool isProject = false;
  String selectPayment = '';
  late BuildContext ctx;
  final TextEditingController budgetTypeController = TextEditingController();
  final TextEditingController costCenterController = TextEditingController();
  final TextEditingController projectController = TextEditingController();
  final TextEditingController tenderNameController = TextEditingController();
  final TextEditingController tenderNumberController = TextEditingController();
  final TextEditingController  tenderValueController = TextEditingController();
  final TextEditingController  customerNameController = TextEditingController();
  final TextEditingController  paymentTypeController = TextEditingController(text:AppLocalizations.appLang == 'en' ? "SADAD Number" : "رقم سداد");
  final TextEditingController  sADADNumberController = TextEditingController();
  final TextEditingController  beneficiaryNameController = TextEditingController();
  final TextEditingController  bankController = TextEditingController();
  final TextEditingController  iBANController = TextEditingController();
  final TextEditingController  commentController = TextEditingController();

  TenderRequestBloc() : super(TenderRequestInitialState()){
    repo = TenderRequestRepo();
    on<TenderRequestInitialEvent>((event,emit) async{
      emit(TenderRequestInitialState());
    });

    on<PaymentTypeListEvent>((event,emit) async{
      emit(PaymentTypeListLoadingState());
      await fetchPaymentTypeList(emit);
    });

    on<ProjectWpsListEvent>((event,emit) async{
      emit(ProjectWpsListLoadingState());
      await fetchProjectWpsList(emit);
    });

    on<BankListEvent>((event,emit) async{
      emit(BankListLoadingState());
      await fetchBankList(emit);
    });

    on<CustomerListEvent>((event,emit) async{
      emit(CustomerListLoadingState());
      await fetchCustomerList(emit);
    });

    on<UploadAttachmentFilesEvent>((event, emit) async {
      emit(UploadAttachmentLoadingState());
      await uploadAttachmentFiles(emit);
    });


    on<FilesUpdatedEvent>((event, emit) async {
      emit(FilesUpdatedState());
    });

    on<SubmitEvent>((event, emit) async {
      await submitTenderRequest(emit);
    });

    on<ChangeBudgetTypeEvent>(
            (ChangeBudgetTypeEvent event,Emitter<TenderRequestState> emit) async {
        selectedBudgetTypeModel = event.budgetTypeModel;
        isProject = selectedBudgetTypeModel?.id == consts.project;
        if(isProject){
          costCenterController.clear();
         add(ProjectWpsListEvent());

        }else{
          costCenterController.text =  tenderPaymentResponse?.costCenter.first.costCenterCode ?? '';
          projectController.clear();
        }
        emit(ChangeBudgetTypeState());
    });

    on<ChangePaymentTypeEvent>(
        (ChangePaymentTypeEvent event,Emitter<TenderRequestState> emit) async {
          selectedTenderPaymentModel = event.tenderPayment;
          selectPayment= selectedTenderPaymentModel?.id ?? '';
          paymentRequiredFields = getRequiredFieldsByTenderPaymentId(selectPayment);
          if(selectPayment == "Bank Transfer" ){
            sADADNumberController.clear();
            beneficiaryNameController.clear();
          }if(selectPayment == "SADAD Number"){
            beneficiaryNameController.clear();
            bankController.clear();
            iBANController.clear();
          }if(selectPayment == "Bank Cheque"){
            sADADNumberController.clear();
            bankController.clear();
            iBANController.clear();
            beneficiaryNameController.clear();
          }

        });


    on<ValidateFieldsEvent>(
            (ValidateFieldsEvent event, Emitter<TenderRequestState> emit) async {
          emit(ValidateFieldsState(isButtonEnable()));
        });

  }




  fetchPaymentTypeList(emitter)async{
    await repo.getPaymentTypeList(optimizedUdasValueMap: '').then((response) => onfetchPaymentTypeList(response, emitter));
  }

  void onfetchPaymentTypeList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onfetchPaymentTypeListSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(PaymentTypeListErrorState());
    });
  }

 void onfetchPaymentTypeListSuccess(data,emitter){
   tenderPaymentResponse = TenderPaymentResponse.fromJson(data);
   tenderPaymentList = tenderPaymentResponse?.tenderPayment??[] ;
    emitter(PaymentTypeListSuccessState());
  }
/////////////////////////////////


    fetchProjectWpsList(emitter)async{
    await repo.getProjectWpsList(employeeid: ' ').then((response) => onfetchProjectWpsList(response, emitter));
  }

  void onfetchProjectWpsList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onfetchProjectWpsListSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(ProjectWpsListErrorState(message:error.toString() ));
      print(error.toString());
    });
  }
  void onfetchProjectWpsListSuccess(data,emitter){
    projectWpsResponse = ProjectWpsResponse.fromJson(data);
    filterListProject = projectWpsResponse?.projectWps??[];
    emitter(ProjectWpsListSuccessState());
  }
////////////

  fetchBankList(emitter)async{
      await repo.getBankList(employeeid: ' ').then((response)=>onFetchBankList(response, emitter));
  }

  void onFetchBankList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchBankSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(BankListErrorState());

    });
  }

  void onFetchBankSuccess(data,emitter){
      bankListResponse = BankListResponse.fromJson(data);
      bankList = bankListResponse?.bankList??[];
      emitter(BankListSuccessState());
  }
  //////////

  fetchCustomerList(emitter)async{
    await repo.getCustomerList(searchTerm: ' ', skip: ' ', top: '').then((response)=>onFetchCustomerList(response, emitter));
  }

  void onFetchCustomerList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchCustomerSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(CustomerListErrorState());

    });
  }

  void onFetchCustomerSuccess(data,emitter){
    customerInfoResponse = CustomerInfoResponse.fromJson(data);
    customerInfoList = customerInfoResponse?.customerInforma??[];
    emitter(CustomerListSuccessState());
  }

///////////////

  bool isValidAttach() => AttachBloc.instance(ctx).files.isNotEmpty &&
      AttachBloc.instance(ctx).files.any((file) => file.isNetworkFile == false);

  Future uploadAttachmentFiles(emitter) async {
    if (AttachBloc.instance(ctx).files.isNotEmpty) {
      {
        AttachBloc.instance(ctx).add(UploadFilesEvent(
            false,
            submitTenderRequestModel?.recId,
            submitTenderRequestModel?.recId,
            AttachBloc.instance(ctx).metaDataResponse?.attachmentId ?? ''));
      }
    }

    emitter(UploadAttachmentFilesSuccessState());

  }
  List<String> getRequiredFieldsByTenderPaymentId(String id) {
    switch (id) {
      case "Bank Transfer":
        return ["beneficiaryName", "bank", "iban"];
      case "Bank Cheque":
        return ["beneficiaryName"];
      case "SADAD Number":
        return ["sadadNumber"];
      default:
        return [];
    }
  }

  bool isButtonEnable(){

    paymentRequiredFields = getRequiredFieldsByTenderPaymentId(selectedTenderPaymentModel?.id ?? '');

      return (budgetTypeController.text.isNotEmpty) &&
         ( ((costCenterController.text.isNotEmpty))||
          ((projectController.text.isNotEmpty)))
        &&(tenderNameController.text.isNotEmpty)  &&
        (tenderNumberController.text.isNotEmpty) &&
        (tenderValueController.text.isNotEmpty) &&
        (customerNameController.text.isNotEmpty) &&
        (paymentTypeController.text.isNotEmpty) &&
     (((selectedTenderPaymentModel?.id == "Bank Transfer" )&&(beneficiaryNameController.text.isNotEmpty)
         &&(bankController.text.isNotEmpty)&&(iBANController.text.isNotEmpty))||
         ((selectedTenderPaymentModel?.id == "Bank Cheque" )&&(beneficiaryNameController.text.isNotEmpty))||
         ((selectedTenderPaymentModel?.id == "SADAD Number" )&&(sADADNumberController.text.isNotEmpty))
     )
        &&(commentController.text.isNotEmpty);
  }






  //  &&(AttachBloc.instance(ctx).files.isNotEmpty )
  /*bool isButtonEnable() {
    final requiredFields = selectedBudgetTypeModel?.requiredFields ?? [];
    final costCenterValid =  requiredFields.contains("costCenter") ? (costCenterController.text.isNotEmpty): true;
    final projectValid =  requiredFields.contains("project") ? (projectController.text.isNotEmpty):true;

    paymentRequiredFields = getRequiredFieldsByTenderPaymentId(paymentTypeController.text ?? '');

    final beneficiaryValid = !paymentRequiredFields.contains("beneficiaryName") || (beneficiaryNameController.text.isNotEmpty);
    final bankValid = !paymentRequiredFields.contains("bank") || (bankController.text.isNotEmpty);
    final ibanValid = !paymentRequiredFields.contains("iban") || (iBANController.text.isNotEmpty);
    final sadadValid = !paymentRequiredFields.contains("sadadNumber") || (sADADNumberController.text.isNotEmpty);


    return (budgetTypeController.text.isNotEmpty) &&
        (costCenterValid||
            projectValid)
        &&(tenderNameController.text.isNotEmpty)  &&
        (tenderNumberController.text.isNotEmpty) &&
        (tenderValueController.text.isNotEmpty) &&
        (customerNameController.text.isNotEmpty) &&
        (paymentTypeController.text.isNotEmpty) &&
        beneficiaryValid && bankValid && ibanValid && sadadValid
        &&(AttachBloc.instance(ctx).files.isNotEmpty )
        &&(commentController.text.isNotEmpty);
  }*/

  Future<void> submitTenderRequest(Emitter emit) async{
    emit(SubmitLoadingState());

    final TenderSubmitBody submitBody = TenderSubmitBody(
        sadadNumber: sADADNumberController.text ?? '',
        budgetTypeEn: selectedBudgetTypeModel?.id ?? '',
        costCenterId: isProject?null:tenderPaymentResponse?.costCenter.first.costCenterCode ?? '',
        tenderName: tenderNameController.text ?? '',
        tenderNumber: tenderNumberController.text ?? '',
        tenderValue: tenderValueController.text ?? '',
        customerId: customerInfo?.id ?? '' ,
        paymentTypeId: selectPayment ?? '',
        beneficiaryName: beneficiaryNameController.text ?? '',
  //     bankNameEn: listbank?.bankNameEn ?? '',
        bankNameId:listbank?.bankKey ?? '',
        ibanNumber: iBANController.text?? '',
        comments: commentController.text?? '',
        projectNameId: selectedProjectWps?.wbscode ?? ''
    );

    await repo
        .getSubmit(submitBodyModel:submitBody).then((response) => onSubmit(response, emit));
   }

  void onSubmit(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitErrorState());
    });
  }

void onSubmitSuccess(data, Emitter emitter){
  submitTenderRequestModel =SubmitTenderRequestModel.fromJson(data);
  attachmentRequestBody.objectId = submitTenderRequestModel!.recId.toString();
  emitter(SubmitSuccessState());

}




}

//int.tryParse(submitTenderRequestModel?.displayRecId ?? '') ?? 0,