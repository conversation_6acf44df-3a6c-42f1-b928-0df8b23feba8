
class MyTeamDashboardKpi {
  final int total;
  final int courses;
  final int overdue;
  final int planned;
  final int quizzes;
  final int completed;
  final int inProgress;
  final String? employeeId;
  final int certificates;

  MyTeamDashboardKpi({
    required this.total,
    required this.courses,
    required this.overdue,
    required this.planned,
    required this.quizzes,
    required this.completed,
    required this.inProgress,
    this.employeeId,
    required this.certificates,
  });

  factory MyTeamDashboardKpi.fromJson(Map<String, dynamic> json) {
    return MyTeamDashboardKpi(
      total: json['total']?.toInt() ?? 0,
      courses: json['courses']?.toInt() ?? 0,
      overdue: json['overdue']?.toInt() ?? 0,
      planned: json['planned']?.toInt() ?? 0,
      quizzes: json['quizzes']?.toInt() ?? 0,
      completed: json['completed']?.toInt() ?? 0,
      inProgress: json['inprogress']?.toInt() ?? 0,
      employeeId: json['employee_id']?.toString(),
      certificates: json['certificates']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'courses': courses,
      'overdue': overdue,
      'planned': planned,
      'quizzes': quizzes,
      'completed': completed,
      'inprogress': inProgress,
      'employee_id': employeeId,
      'certificates': certificates,
    };
  }

  // Helper method to get completion percentage
  double get completionPercentage {
    if (total == 0) return 0.0;
    return (completed / total) * 100;
  }

  // Helper method to get progress percentage
  double get progressPercentage {
    if (total == 0) return 0.0;
    return ((completed + inProgress) / total) * 100;
  }
}
