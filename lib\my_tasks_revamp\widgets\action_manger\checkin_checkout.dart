import 'package:eeh/my_tasks_revamp/bloc/my_task_bloc_revamp.dart';
import 'package:eeh/my_tasks_revamp/bloc/tasks_events.dart';
import 'package:eeh/my_tasks_revamp/widgets/action_manger/actions.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';

class CheckInCheckOut extends IAdminAction {
  CheckInCheckOut(super.actionData, super.context, super.workModel);
  String _actionType = '';

  @override
  onActionTapped() {
   if (actionData.actionRoute?.toLowerCase().contains('check in') ?? false) {
      _actionType = 'Check In';
    } else {
      _actionType = 'Check Out';
    }
    TasksBlocRevamp.instance(context).add(CheckInCheckOutEvent(
      actionType: _actionType,
      mutableContext: workModel.mutableContext ?? '',
      tableName: workModel.tableName??'',
      actionPopup: actionData.actionPopup??''
    ));
    Navigation.popScreen(context);
  }
  
}