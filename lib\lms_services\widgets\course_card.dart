import 'package:cached_network_image/cached_network_image.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/lms_services/models/my_learning/item_detail.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/lms_services/utils/lms_utils.dart';
import 'package:eeh/lms_services//widgets/detail_row.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class CourseCard extends StatelessWidget {
  final double progress = 0.0;
  final DateTime? dueDate;
  final VoidCallback? onCourseLaunch;
  final ItemDetail? itemDetail;
  const CourseCard({
    Key? key,
    // this.progress,
    required this.itemDetail,
    this.dueDate,
    this.onCourseLaunch,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // final title = course.course?.title ?? 'Untitzled Course';
    // final description =
    //     course.course?.description ?? 'No description available';
    final imageUrl =
        "https://campustechnology.com/-/media/edu/campustechnology/2019-images/20191209online.jpg";
    final status = itemDetail?.status.toLowerCase() ?? "";

    // bool isOverdue = false;
    // if (dueDate != null && status != LmsUtils.STATUS_COMPLETED) {
    //   try {
    //     final courseDueDate = DateTime.parse(course.dueDate!);
    //     isOverdue = courseDueDate.isBefore(DateTime.now());
    //   } catch (e) {
    //     isOverdue = false;
    //   }
    // }

    // final displayStatus = isOverdue ? LmsUtils.STATUS_OVERDUE : status;
    // double progressPercentage = progress / 100;

    return Container(
      padding: EdgeInsets.all(16).h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: borderColor, width: 1.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildImageSection(imageUrl),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 12.h),
              _buildDetailRowSection(context, [
                DetailRowItem(
                  label: itemDetail?.tag ?? "",
                  iconCss: "fa-solid fa-airplay",
                ),
                if (itemDetail?.dueDate != null)
                  DetailRowItem(
                    label:
                        "Due Date ${LmsUtils.formatDate(itemDetail?.dueDate ?? "")}",
                    iconCss: "fa-regular fa-calendar",
                  ),
              ]),
              SizedBox(height: 8.h),
              _buildTitle(itemDetail?.title ?? ""),
              itemDetail?.title != null
                  ? SizedBox(height: 8.h)
                  : SizedBox.shrink(),
              _buildDescription(itemDetail?.description ?? ""),
              itemDetail?.description != null
                  ? SizedBox(height: 16.h)
                  : SizedBox.shrink(),
              _buildStatusCard(context, status),
              status != LmsUtils.STATUS_COMPLETED
                  ? SizedBox(height: 16.h)
                  : SizedBox.shrink(),
              _buildProgressBar(context,
                  progressPercentage: itemDetail?.progressPar ?? "0",
                  displayStatus: status),
              itemDetail?.progressPar != null
                  ? SizedBox(height: 16.h)
                  : SizedBox.shrink(),
              _buildActionButton(context, "displayStatus"),
              SizedBox(height: 16.h),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(String? imageUrl) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: CachedNetworkImage(
            imageUrl: imageUrl ?? "",
            height: 150.h,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 150.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: CircularProgressIndicator(
                  color: primaryColor,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 150.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                getIconFromCss('fa-solid fa-book'),
                color: Colors.grey,
                size: 50.sp,
              ),
            ),
          ),
        ),
        Positioned(
          right: AppLocalizations.appLang == 'en' ? 0 : null,
          left: AppLocalizations.appLang == 'en' ? null : 0,
          child: Padding(
            padding: EdgeInsets.all(16),
            child: DetailRow(
                label: "Course",
                icon: getIconFromCss("fa-solid fa-airplay"),
                onPressed: () {}),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRowSection(
    BuildContext context,
    List<DetailRowItem> items,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          for (int i = 0; i < items.length; i++) ...[
            DetailRow(
              label: items[i].label,
              icon: getIconFromCss(items[i].iconCss),
              onPressed: items[i].onPressed ?? () {},
              borderColor: borderColor,
              textColor: textMain,
              iconColor: textMain,
            ),
            if (i != items.length - 1) SizedBox(width: 8.w),
          ],
        ],
      ),
    );
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: FontUtilities.getTextStyle(
        TextType.medium,
        size: 16.sp,
        fontWeight: FontWeight.bold,
        textColor: textMain,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildDescription(String description) {
    return Text(
      description,
      style: FontUtilities.getTextStyle(
        TextType.medium,
        size: 14.sp,
        textColor: textSecondary,
      ),
      maxLines: 5,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildStatusCard(BuildContext context, String displayStatus) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: LmsUtils.getStatusColor(displayStatus).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(
          color: LmsUtils.getStatusColor(displayStatus),
          width: 1,
        ),
      ),
      child: Text(
        LmsUtils.getStatusText(context, displayStatus),
        style: FontUtilities.getTextStyle(
          TextType.medium,
          size: 12.sp,
          fontWeight: FontWeight.w500,
          textColor: LmsUtils.getStatusColor(displayStatus),
        ),
      ),
    );
  }

  Widget _buildProgressBar(
    BuildContext context, {
    required String progressPercentage,
    required String displayStatus,
  }) {
    double progress = double.tryParse(progressPercentage) ?? 0.0;
    return Row(
      children: [
        Expanded(
          child: LinearPercentIndicator(
            lineHeight: 8.h,
            percent: progress,
            backgroundColor: Colors.grey[200],
            progressColor: LmsUtils.getStatusColor(displayStatus),
            barRadius: Radius.circular(4.r),
            padding: EdgeInsets.zero,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          AppLocalizations.of(context).translate('Completed '),
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 12.sp,
            textColor: textSecondary,
          ),
        ),
        Text(
          '${(progress * 100).round()}%',
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 12.sp,
            textColor: LmsUtils.getStatusColor(displayStatus),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context, String displayStatus) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        onPressed: () {
          _launchCourse(context);
        },
        child: Text(
          LmsUtils.getActionButtonText(context, displayStatus),
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 14.sp,
            fontWeight: FontWeight.w500,
            textColor: Colors.white,
          ),
        ),
      ),
    );
  }

  void _launchCourse(BuildContext context) {
    if (onCourseLaunch != null) {
      onCourseLaunch!();
    } else {
      // Default implementation
      // final url = course.course?.url;
      // if (url != null && url.isNotEmpty) {
      //   Navigation.navigateToScreen(
      //     context,
      //     SimpleWebView(
      //       url: url,
      //       title: course.course?.title ?? 'Course',
      //     ),
      //   );
    }
  }
}

class DetailRowItem {
  final String label;
  final String iconCss;
  final VoidCallback? onPressed;

  DetailRowItem({
    required this.label,
    required this.iconCss,
    this.onPressed,
  });
}
