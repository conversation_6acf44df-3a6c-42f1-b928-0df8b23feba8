
import '../model/employee_loaninfo_model.dart';

abstract class LoanPreClousrState{}

class LoanPreClousrInitialState extends LoanPreClousrState{}

class FetchEmployeeInfoLoadingState extends LoanPreClousrState {}

class FetchEmployeeInfoSuccessState extends LoanPreClousrState {
  final LoanStatusModel loanModel;
  FetchEmployeeInfoSuccessState(this.loanModel);

}

class FetchEmployeeInfoErrorState extends LoanPreClousrState {}

class UploadAttachmentLoadingState extends LoanPreClousrState{}

class UploadAttachmentFilesSuccessState extends LoanPreClousrState{}

class UploadAttachmentFilesErrorState extends LoanPreClousrState{}

class FilesUpdatedState extends LoanPreClousrState{}

//class CheckFileState extends LoanPreClousrState{}

class SubmitRequestLoadingState extends LoanPreClousrState {}

class SubmitRequestSuccessState extends LoanPreClousrState {}

class SubmitRequestErrorState extends LoanPreClousrState {

}

