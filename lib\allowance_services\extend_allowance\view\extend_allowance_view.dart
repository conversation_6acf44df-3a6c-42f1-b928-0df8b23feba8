import 'package:eeh/allowance_services/extend_allowance/bloc/extend_allowance_bloc.dart';
import 'package:eeh/allowance_services/extend_allowance/bloc/extend_allowance_event.dart';
import 'package:eeh/allowance_services/extend_allowance/bloc/extend_allowance_state.dart';
import 'package:eeh/allowance_services/utils.dart';
import 'package:eeh/allowance_services/widget/allowance_card.dart';
import 'package:eeh/allowance_services/widget/success_screen_content.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_event.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/cancel_button_component.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

class ExtendAllowanceView extends StatefulWidget {
  final bool isGas;
  const ExtendAllowanceView({super.key, this.isGas = false});

  @override
  State<ExtendAllowanceView> createState() => _ExtendAllowanceViewState();
}

class _ExtendAllowanceViewState extends State<ExtendAllowanceView> {
  late ExtendAllowanceBloc bloc;
  AllowanceUtils utils = AllowanceUtils();

  @override
  void dispose() {
    bloc.commentController.dispose();
    bloc.numberOfMonthsController.dispose();
    bloc.searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ExtendAllowanceBloc>(
          create: (context) {
            return ExtendAllowanceBloc()
              ..add(ExtendAllowanceInitialEvent(widget.isGas));
          },
        ),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
      ],
      child: Scaffold(
        appBar: getAppBarWidget(),
        body: BlocConsumer<ExtendAllowanceBloc, ExtendAllowanceState>(
          listenWhen: (previous, current) =>
              current is ExtendAllowanceInitialState,
          listener: (context, state) {
            bloc.add(EmployeeAllowanceEvent());
          },
          buildWhen: (previous, current) =>
              current is ExtendAllowanceInitialState ||
              current is EmployeeAllowanceSuccessState ||
              current is SearchEmployeeState,
          builder: (context, state) {
            bloc = context.read<ExtendAllowanceBloc>();
            if (state is ExtendAllowanceInitialState ||
                state is EmployeeAllowanceLoadingState) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return getBody();
            }
          },
        ),
      ),
    );
  }

  PreferredSize getAppBarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: widget.isGas
          ? FavoriteComponent(
              title: AppLocalizations.of(context)
                  .translate('Extend Gas Allowance'),
              id: '42',
            )
          : FavoriteComponent(
              title: AppLocalizations.of(context)
                  .translate('Extend Communication Allowance'),
              id: '41',
            ),
    );
  }

  getBody() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height - 220.h,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    utils.searchTextField(bloc.searchController, context,
                        (v) => bloc.add(SearchEmployeeEvent(v))),
                    utils.getImportantNotes(context),
                    utils.getEmplyeeAllowanceNumber(
                        context, bloc.allowanceListResponse?.employees?.length),
                    utils.emptyView(bloc.filterList.isEmpty, context,bloc.searchController.text.trim().isNotEmpty),    
                    getAllowanceCardWidget(),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: getSubmitButton(),
          ),
        ],
      ),
    );
  }

  getAllowanceCardWidget() {
    return BlocBuilder<ExtendAllowanceBloc, ExtendAllowanceState>(
        buildWhen: (previous, current) =>
            current is NumberOfMonthsValueChangelState ||
            current is SavedButtonActionState ||
            current is SearchEmployeeState,
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(15.0),
            child: SizedBox(
              width: double.infinity,
              height: 400.h,
              child: ListView.builder(
                  itemBuilder: (context, index) {
                    final item = bloc.filterList[index];
                    return AllowanceCard(
                      actionType: ActionType.edit,
                      employeeEn: item.employeenameen ?? '',
                      employeeAr: item.employeenamear ?? "",
                      numOfMonths: item.numberofmonths.toString(),
                      targetAmount: item.eligibleamount.toString(),
                      startDate: item.startdate ?? '',
                      endDate: item.enddate ?? '',
                      extendedMonths: item.extendedMonths ?? '',
                      comment: item.comment ?? '',
                      onActionTap: () {
                        bloc.clearForm();
                        getEditBottomSheet(index);
                      },
                    );
                  },
                  itemCount: bloc.filterList.length),
            ),
          );
        });
  }

  getEditBottomSheet(int index) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        showDragHandle: true,
        builder: (context) {
          return DraggableScrollableSheet(
            initialChildSize: 0.65,
            maxChildSize: 1,
            minChildSize: 0.3,
            expand: false,
            builder: (context, scrollController) => getBottomSheetBody(index),
          );
        });
  }

  getBottomSheetBody(int index) {
    return Form(
      key: bloc.formKey,
      child: Column(children: [
        Center(
            child: Column(
          children: [
            Icon(
              getIconFromCss("fa-light fa-pen-to-square"),
              size: 30,
            ),
            Text(AppLocalizations.of(context).translate('Edit Employee'),
                style:
                    FontUtilities.getTextStyle(TextType.medium, size: 16.sp)),
            Text(
                AppLocalizations.of(context)
                    .translate('Please fill in the data below'),
                style:
                    FontUtilities.getTextStyle(TextType.regular, size: 14.sp)),
          ],
        )),
        utils.getNumberOfMonthsWidget(
          context: context,
          onSelect: (value) {
            bloc.add(NumberOfMonthsValueChangelEvent(value));
          },
          controller: bloc.numberOfMonthsController,
          label: AppLocalizations.of(context).translate('Extended Months'),
          title:
              AppLocalizations.of(context).translate('Select Extended Months'),
        ),
        utils.getCommentsWidget(
          context,
          bloc.commentController,
        ),
        Spacer(),
        getSaveAndCancelButtons(index),
      ]),
    );
  }

  Widget getSaveAndCancelButtons(int index) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          CancelButtonComponent(
            onPressed: () {
              Navigator.pop(context);
              bloc.clearForm();
            },
          ),
          SubmitButtonComponent(
            text: AppLocalizations.of(context).translate('save'),
            onPressed: () {
              bloc.add(SavedButtonActionEvent(context, index));
            },
            backGroung: primaryColor,
            //  bloc.isButtonEnable()
            //     ? primaryColor
            //     : primaryColor.withOpacity(0.2),
            isLoading: false,
          ),
        ]);
  }

  Widget getSubmitButton() {
    return BlocConsumer<ExtendAllowanceBloc, ExtendAllowanceState>(
        listenWhen: (previous, current) =>
            current is SubmitAllowanceSuccessState,
        listener: (context, state) {
          if (state is SubmitAllowanceSuccessState) {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => SuccessScreen(
                          title: AppLocalizations.of(context)
                              .translate("Request Submitted Successfully"),
                          contentWidget: SuccessScreenContentWidget(
                            submitResponse: bloc.submitResponse,
                          ),
                        )));
          }
        },
        buildWhen: (previous, current) =>
            current is SubmitAllowanceLoadingState ||
            current is SubmitAllowanceErrorState ||
            current is SubmitAllowanceSuccessState ||
            current is SavedButtonActionState,
        builder: (context, state) {
          return Column(
            children: [
              CancelButtonComponent(),
              SubmitButtonComponent(
                text: AppLocalizations.of(context).translate('Submit'),
                onPressed: () {
                  if (bloc.extendedList.isNotEmpty) {
                    bloc.add(SubmitAllowanceEvent());
                  }
                },
                backGroung: (bloc.extendedList.isNotEmpty)
                    ? primaryColor
                    : primaryColor.withOpacity(0.2),
                isLoading: state is SubmitAllowanceLoadingState,
              ),
            ],
          );
        });
  }
}
