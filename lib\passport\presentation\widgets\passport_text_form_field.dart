import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/passport/data/models/get_countries_response_model/country.dart';
import 'package:eeh/shared/app_bloc/app_event.dart';
import 'package:eeh/shared/form_validation.dart';
import 'package:eeh/shared/style.dart';
import 'package:eeh/travel_requset/presentation/widgets/travel_bottom_sheet.dart';
import 'package:eeh/travel_requset/travel_request_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../../../shared/styles/colors.dart';
import '../../../shared/date_util.dart';
import '../../../shared/utility/font_utility.dart';
import '../../../shared/utility/navigation_utility.dart';
import '../../../shared/widgets/calendar.dart';
import '../bloc/passport_bloc.dart';
import 'passport_bottom_sheet.dart';

class PassportCustomTextFormField extends StatelessWidget {
  PassportCustomTextFormField({
    super.key,
    this.onTap,
    required this.labelText,
    this.controller,
    this.isRequired = true,
    this.hasBottomSheet = false,
    this.hasDatePicker = false,
    this.startOfDatePicker,
    this.hasDatePickerRange = false,
    this.countries,
    this.onSelect,
    this.isReadOnly = false,
    this.dateRangePickerController,
    this.onChange,
    this.keyboardType = TextInputType.text,
    this.onDateChange()?,
    this.maxDate,
    this.minDate,
  });
  final DateRangePickerController? dateRangePickerController;
  final Function()? onTap;
  ValueChanged<dynamic>? onSelect;
  final String labelText;
  final bool isRequired;
  final bool hasBottomSheet;
  final bool isReadOnly;
  final bool hasDatePicker;
  DateTime? startOfDatePicker;
  final TextInputType keyboardType;
  final bool hasDatePickerRange;
  final TextEditingController? controller;
  final List<Country>? countries;
  final Function(String)? onChange;
  Function()? onDateChange;
  final RegExp passportNumRegExp = RegExp(r'^[a-zA-Z0-9]+$');
  final RegExp iqamaNumRegExp = RegExp(r'^\d{10}$');
  final DateTime? maxDate;
  final DateTime? minDate;
  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: TextFormField(
          onChanged: onChange,
          keyboardType: keyboardType,
          onTap: hasDatePicker
              ? () async {
                  hasDatePickerRange
                      ? await getDateRangePicker(context).then((onValue) {
                          controller?.text = DateUtil.formatDate(
                                  onValue!.start!, DateUtil.serverDateFormat) +
                              " : " +
                              DateUtil.formatDate(
                                  onValue.end, DateUtil.serverDateFormat);
                          onSelect?.call(onValue);
                        })
                      : await onTapDatePickerFunction(context: context);
                }
              : () {},
          readOnly: isReadOnly,
          controller: controller,
          // onChanged: ,
          validator: (value) {
            switch (labelText) {
              case 'Passport Number':
                if (value!.isEmpty || !passportNumRegExp.hasMatch(value)) {
                  return AppLocalizations.of(context)
                      .translate('please enter valid passport number');
                }
                break;
              case 'Iqama Number':
                if (value!.isEmpty) {
                  return '${AppLocalizations.of(context).translate('please enter')} ${AppLocalizations.of(context).translate('Iqama Number')}';
                } else if (value.length != 10) {
                  return AppLocalizations.of(context)
                      .translate('border number msg'); // "Please enter 10 digits"
                } else if (!iqamaNumRegExp.hasMatch(value)) {
                  return AppLocalizations.of(context)
                      .translate('please enter valid Iqama number');
                }
                break;
              default:
                return FormValidation.checkEmptyText(value,
                    "${AppLocalizations.of(context).translate('please enter')} $labelText");
            }
          },
          style:FontUtilities.getTextStyle(TextType.regular,
                    size: 12.sp,fontWeight:FontWeight.w400),
          decoration: InputDecoration(
            label: Text.rich(TextSpan(children: <TextSpan>[
              TextSpan(
                style:FontUtilities.getTextStyle(TextType.medium,
                    size: 12.sp,fontWeight:FontWeight.w500),
                text: labelText,
              ),
              TextSpan(
                  text: isRequired ? ' *' : "",
                  style:mandatoryStyle),
            ])),
            suffixIcon: hasDatePicker
                ? const Icon(Icons.calendar_today)
                : hasBottomSheet
                    ? const Icon(Icons.keyboard_arrow_down_sharp)
                    : SizedBox(),
          ),
        ));
  }

  Future<DateTime?> getDatePicker(BuildContext context) {
    return showDatePicker(
      context: context,
      currentDate: startOfDatePicker ??= DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2200),
    );
  }

  Future<DateTimeRange?> getDateRangePicker(BuildContext context) {
    return showDateRangePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime(200),
    );
  }

  Future<void> getBottomSheet({
    required BuildContext context,
    required String title,
  }) async {
    FocusScope.of(context).unfocus();
    await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        showDragHandle: true,
        builder: (context) {
          return PassportBottomSheet(
            contentList: countries,
            title: title,
            onSelection: (value) {
              controller?.text = value;

              Navigation.popScreen(context);
              onSelect?.call(value);
            },
          );
        });
  }

  onTapDatePickerFunction({required BuildContext context}) async {
    showModalBottomSheet(
            isScrollControlled: true,
            context: context,
            backgroundColor: Colors.white,
            builder: (BuildContext context) {
              return DraggableScrollableSheet(
                // controller: ,
                expand: false,
                builder: (context, scrollController) {
                  return Container(
                    // height: 751.h,
                    // width: 377.w,
                    padding: const EdgeInsets.all(16.0),
                    child: CustomCalendar(
                      maxDate: maxDate,
                      minDate: minDate,
                      dateRangePickerController: dateRangePickerController!, isWeekendEnable: true,
                    ),
                  );
                },
                initialChildSize: 0.7,
                maxChildSize: 1,
                minChildSize: 0.3,
              );
            },
            showDragHandle: true)
        .then((value) {
      onDateChange?.call();
    });
  }
}
