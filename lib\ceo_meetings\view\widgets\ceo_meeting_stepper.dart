import 'dart:ui';

import 'package:eeh/ceo_meetings/bloc/ceo_meetings_bloc.dart';
import 'package:eeh/ceo_meetings/bloc/ceo_meetings_events.dart';
import 'package:eeh/ceo_meetings/bloc/ceo_meetings_states.dart';
import 'package:eeh/ceo_meetings/view/widgets/success_screen_content_widget.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import 'ceo_stepper_header.dart';

class CEOMeetingStepper extends StatelessWidget {
  final int currentStep;
  final VoidCallback onStepContinue;
  final VoidCallback onStepCancel;
  final List<Step> steps;
  bool withCancel = true;

  CEOMeetingStepper({
    super.key,
    required this.currentStep,
    required this.onStepContinue,
    required this.onStepCancel,
    required this.steps,
    required this.withCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CEOStepperHeader(steps: steps, currentStep: currentStep),
        SizedBox(
          height: 10.h,
        ),
        getCEOMeetingsBody(),
        getButtons(context),
      ],
    );
  }

  Column getButtons(BuildContext context) {
    return Column(
      children: [
        getCancelButton(context),
        BlocConsumer<CEOMeetingsBloc, CEOMeetingsState>(
            listener: (context, state) =>
                state is UploadAttachmentFilesSuccessState
                    ? Navigation.navigateToScreen(
                        context,
                        SuccessScreen(
                          contentWidget: CEOMeetingsSuccessScreenContentWidget(
                            requestData: context
                                .read<CEOMeetingsBloc>()
                                .submitRequestResponse,
                          ),
                          title: AppLocalizations.of(context)
                              .translate("Request Submitted Successfully"),
                        ))
                    : context
                        .read<CEOMeetingsBloc>()
                        .add(UploadAttachmentFilesEvent(context)),
            listenWhen: (previous, current) =>
                current is UploadAttachmentFilesSuccessState ||
                current is SubmitRequestSuccessState,
            buildWhen: (previous, current) =>
                current is ChangeFieldValueState ||
                current is UploadAttachmentLoadingState ||
                current is SubmitRequestLoadingState,
            builder: (context, state) => SubmitButtonComponent(
                text: currentStep == steps.length - 1
                    ? AppLocalizations.of(context).translate('Submit')
                    : AppLocalizations.of(context).translate('Next'),
                onPressed: () => _onButtonPressed(currentStep, context, state),
                backGroung: _getButtonColor(currentStep, context),
                isLoading: state is SubmitRequestLoadingState ||
                    state is UploadAttachmentLoadingState)),
      ],
    );
  }

  SizedBox getCancelButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: onStepCancel,
        child: Text(
          currentStep == 0
              ? AppLocalizations.of(context).translate('Cancel')
              : AppLocalizations.of(context).translate('back'),
          style: TextStyle(
            height: 2.5.h,
            color: rejectColor,
            fontSize: 18.sp,
          ),
        ),
      ),
    );
  }

  Expanded getCEOMeetingsBody() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: SingleChildScrollView(child: steps[currentStep].content),
      ),
    );
  }

  Color _getButtonColor(int page, BuildContext context) {
    if (page == 0) {
      return context.read<CEOMeetingsBloc>().isNextButtonEnable()
          ? primaryColor
          : primaryColor.withOpacity(0.2);
    } else {
      return context.read<CEOMeetingsBloc>().isSubmitButtonEnable()
          ? primaryColor
          : primaryColor.withOpacity(0.2);
    }
  }

  void _onButtonPressed(
      int page, BuildContext context, CEOMeetingsState state) {
    if (page == 0) {
      context.read<CEOMeetingsBloc>().isNextButtonEnable()
          ? onStepContinue()
          : null;
    } else {
      if (context.read<CEOMeetingsBloc>().isSubmitButtonEnable()) {
        if (state is! UploadAttachmentLoadingState &&
            state is! SubmitRequestLoadingState) {
          context.read<CEOMeetingsBloc>().add(SubmitRequestEvent());
        } else {
          return;
        }
      }
    }
  }
}
