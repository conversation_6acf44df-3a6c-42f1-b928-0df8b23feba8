import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/app_constants.dart';
import 'package:eeh/shared/flavors_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../shared/widgets/app_bar.dart';
import '../../../../shared/widgets/toast.dart';
import '../../../services_screen/models/services_response.dart';
import '../../../shared/network/api_helper.dart';
import '../../../shared/network/api_utilities.dart';
import '../../../shared/network/endpoints.dart';
import '../../../shared/utility/secure_storage.dart';



@pragma('vm:entry-point') // Required for FlutterDownloader background execution
void downloadCallback(String id, int status, int progress) {
  // IMPORTANT: Do not use setState or anything that relies on BuildContext here.
  // This function runs in a separate Isolate (background thread).
  // Use `IsolateNameServer` to communicate with the main UI Isolate if needed.

  // This part is for sending messages back to the main UI isolate
  final SendPort? send = IsolateNameServer.lookupPortByName('downloader_send_port');
  if (send != null) {
    send.send([id, status, progress]);
  }
  print('Download task ($id) status raw: $status, progress: $progress');


}

class TableauWebView extends StatefulWidget {
  Elmservice? report;
  TableauWebView({super.key, this.report,});

  @override
  State createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<TableauWebView> {
  bool loading = true;
  final GlobalKey webViewKey = GlobalKey(); // GlobalKey for InAppWebView
  List<Cookie> cookies = [];
  InAppWebViewController? webViewController;

  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
      crossPlatform: InAppWebViewOptions(
        allowFileAccessFromFileURLs: true,
        javaScriptEnabled: true,
        useShouldOverrideUrlLoading: true,
        mediaPlaybackRequiresUserGesture: false,
        useOnDownloadStart: true, // ESSENTIAL for download interception
        allowUniversalAccessFromFileURLs: true, // Often needed for local files interacting with web content
      ),
      android: AndroidInAppWebViewOptions(
        useHybridComposition: true,
        allowFileAccess: true,
        allowContentAccess: true,
      ),
      ios: IOSInAppWebViewOptions(
        allowsInlineMediaPlayback: true,
      ));

  late PullToRefreshController pullToRefreshController;
  String currentUrl = ""; // To store the current URL in the WebView
  final urlController = TextEditingController(); // For displaying current URL (optional UI)
  final textController = TextEditingController(); // For displaying download URL/info
  String? tableauToken;

  String ensureTableauParams(String url) {
    final requiredParams = {
      ':embed': 'y',
      ':tabs': 'no',
      ':toolbar': 'no',
      // ':showVizHome': 'no',
    };

    final uriParts = url.split('#');
    final urlWithoutFragment = uriParts[0];
    final fragment = uriParts.length > 1 ? '#${uriParts[1]}' : '';

    final parts = urlWithoutFragment.split('?');
    final base = parts[0];
    final queryString = parts.length > 1 ? parts[1] : '';

    final existingParams = Map.fromEntries(queryString.split('&')
        .where((param) => param.contains('='))
        .map((param) {
      final split = param.split('=');
      return MapEntry(split[0], split[1]);
    }));

    final updatedParams = Map<String, String>.from(existingParams);

    requiredParams.forEach((key, value) {
      if (updatedParams[key] != value) {
        updatedParams[key] = value;
      }
    });

    final updatedQuery = updatedParams.entries.map((e) => '${e.key}=${e.value}').join('&');
    final finalUrl = '$base?$updatedQuery$fragment';

    return finalUrl;
  }

  @override
  void initState() {
    super.initState();
    _fetchTableauSession(); // Fetch Tableau token initially
    FlutterDownloader.registerCallback(downloadCallback); // Now this should work

    pullToRefreshController = PullToRefreshController(
      options: PullToRefreshOptions(
        color: Colors.blue,
      ),
      onRefresh: () async {
        if (Platform.isAndroid) {
          webViewController?.reload();
        } else if (Platform.isIOS) {
          webViewController?.loadUrl(
              urlRequest: URLRequest(url: await webViewController?.getUrl()));
        }
      },
    );
  }
String getTableauBaseURL(){
    switch(FlavorConfig.instance.flavor){
      case Flavor.staging:
        return "https://etxapipoc.elm.sa";
      case Flavor.development:
        return "https://etxapipoc.elm.sa";
      case Flavor.production:
        return prodBaseURL.replaceAll("/rest", "");
      default:  
        return "https://etxapipoc.elm.sa";
    }
}
 getSiteName()=>(widget.report?.siteName??'').isNotEmpty?"/t/${(widget.report?.siteName??'')}":"";
  Future<void> _fetchTableauSession() async {
    String reportURL='';
    try {
      final response = await ApiHelper().apiCall(
        genericObject,
        body: {"ttoken": null},
        requestType: RequestType.post,
        sessionToken: await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": 'tableauToken Form',
          "moduleName": 'tableauToken',
          "appKey": 'ACM',
        },
      );
      if (response.data != null && response.data["ttoken"] != null) {
        setState(() {
          tableauToken = response.data["ttoken"];
          reportURL="${getTableauBaseURL()}/trusted/$tableauToken${getSiteName()}/views/${(widget.report?.mobReportURL??'')}";;
          reportURL=ensureTableauParams(reportURL);

        });
        // Load the HTML content and then the Tableau viz after getting the token
        if (webViewController != null && tableauToken != null) {
          // This ensures the HTML is loaded first, then the JS API is called
          await webViewController!.evaluateJavascript(
              source: "loadTableauViz('$reportURL');");
        }
      } else {
        if (mounted) {
          showMessage("Failed to fetch Tableau token: ${response}", MessageType.error);
        }
      }
    } catch (e) {
      if (mounted) {
        showMessage("Fetch Tableau Session error: $e", MessageType.error);
      }
    } finally {
      if (mounted) {
        setState(() {
          loading = false; // Set loading to false once initial token fetch is done
        });
      }
    }
  }

  @override
  void dispose() {
    urlController.dispose();
    textController.dispose();
    webViewController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Arguments are typically accessed once in initState or directly in build
    // if they don't change widget state. Removed `isFirst` flag.
    final arguments = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>?;
    // You can use `arguments` here if needed, but it seems `widget.report` covers it.

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(56.h),
        child: CustomAppBar(
          appbar: AppbarType.present,
          title: '${AppLocalizations.appLang == 'en' ? widget.report?.serviceNameEn : widget.report?.serviceNameAr}',
          onActionIconPressed: () {},
        ),
      ),
      body: SafeArea(
        child: Column(
          children: <Widget>[
            Row(
              mainAxisAlignment:MainAxisAlignment.end,
              children: [
                IconButton(
                  icon:  Icon(FontAwesomeIcons.pdfExport,size: 25.sp,),
                  tooltip: "Export Tableau Report to PDF",
                  onPressed: () async {
                    if (webViewController != null && tableauToken != null) {
                      await webViewController!.evaluateJavascript(source: "exportToPDF();");
                    } else {
                      showMessage("Tableau is not ready for export.", MessageType.warning);
                    }
                  },
                ), IconButton(
                  icon:  Icon(FontAwesomeIcons.imageExport,size: 25.sp,),
                  tooltip: "Export Tableau Report to PDF",
                  onPressed: () async {
                    if (webViewController != null && tableauToken != null) {
                      await webViewController!.evaluateJavascript(source: "exportImage();");
                    } else {
                      showMessage("Tableau is not ready for export.", MessageType.warning);
                    }
                  },
                ),

              ],
            ),
            Expanded(
              child: Stack(
                children: [
                  InAppWebView(
                    key: webViewKey,
                    initialFile: 'assets/tableau_embed.html',
                    initialOptions: options,
                    pullToRefreshController: pullToRefreshController,
                    onWebViewCreated: (controller) async {
                      webViewController = controller;

                      // Add JavaScript handlers for communication from JS to Flutter
                      controller.addJavaScriptHandler(
                        handlerName: "exportError",
                        callback: (args) {
                          print("JS Export Error: ${args[0]}");
                          if (mounted) {
                            showMessage("Tableau Export Error: ${args[0]}", MessageType.error);
                          }
                        },
                      );
                      // This handler is for if you implement direct blob download in JS
                      // It is currently NOT used by the `exportToPDF()` which triggers `onDownloadStartRequest`

                      // Initial call to load Tableau Viz if token is already available
                      if (tableauToken != null) {
                        await webViewController!.evaluateJavascript(
                            source: "loadTableauViz('$tableauToken');");
                      }
                    },
                    onDownloadStartRequest: (controller, request) async {
                      textController.text = "Attempting download: ${request.url}";
                      try {
                        // Get cookies for the request's domain
                        List<Cookie> currentCookies = await CookieManager.instance().getCookies(url: request.url);
                        String cookieHeader = currentCookies.map((c) => "${c.name}=${c.value}").join("; ");

                        // Extract XSRF token if available in cookies
                        String xsrfToken = currentCookies.firstWhere(
                              (c) => c.name == "XSRF-TOKEN",
                          orElse: () => Cookie(value: '', name: 'XSRF-TOKEN'),
                        ).value.toString();

                        // Determine the save directory based on platform
                        final Directory? dir = Platform.isAndroid
                            ? await getExternalStorageDirectory() // For Android, often better for user-visible files
                            : await getApplicationDocumentsDirectory(); // For iOS, app-private documents directory

                        if (dir == null) {
                          if (mounted) {
                            showMessage("Could not determine download directory.", MessageType.error);
                          }
                          return;
                        }

                        // Extract a filename from the URL, or default
                        String filename = request.url.pathSegments.isNotEmpty
                            ? request.url.pathSegments.last.split('?').first // Remove query parameters
                            : "downloaded_file";

                        // --- Start of Image and PDF Handling Logic ---
                        bool isImageDownload = false;
                        bool isPdfDownload = false;
                        String suggestedExtension = '';

                        // Check MIME type first
                        if (request.mimeType?.contains("application/pdf") == true) {
                          isPdfDownload = true;
                          suggestedExtension = '.pdf';
                        } else if (request.mimeType?.startsWith('image/') == true) {
                          isImageDownload = true;
                          // Extract common image extensions
                          if (request.mimeType!.contains('jpeg') || request.mimeType!.contains('jpg')) {
                            suggestedExtension = '.jpeg'; // Use jpeg as primary for broader compatibility
                          } else if (request.mimeType!.contains('png')) {
                            suggestedExtension = '.png';
                          } else if (request.mimeType!.contains('gif')) {
                            suggestedExtension = '.gif';
                          } else if (request.mimeType!.contains('webp')) {
                            suggestedExtension = '.webp';
                          } else {
                            suggestedExtension = '.dat'; // Fallback if image type is unknown
                          }
                        }

                        if (isImageDownload) {
                          // Ensure the filename has a proper image extension
                          final String lowerCaseFilename = filename.toLowerCase();
                          final List<String> imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];

                          bool hasKnownImageExtension = imageExtensions.any((ext) => lowerCaseFilename.endsWith(ext));

                          if (filename.isEmpty || filename == "F" || !hasKnownImageExtension) {
                            filename = "TableauImage_${DateTime.now().millisecondsSinceEpoch}${suggestedExtension}";
                          } else if (!lowerCaseFilename.endsWith(suggestedExtension)) {
                            // If it has an image extension but not the one suggested by MIME, correct it.
                            // E.g., if MIME is image/png but URL ends in .jpg, change to .png
                            final int dotIndex = filename.lastIndexOf('.');
                            if (dotIndex != -1) {
                              filename = "${filename.substring(0, dotIndex)}$suggestedExtension";
                            } else {
                              filename = "$filename$suggestedExtension";
                            }
                          }
                        } else if (isPdfDownload) {
                          // PDF specific logic
                          filename = "${(widget.report?.serviceNameEn??'')}${DateTime.now().millisecondsSinceEpoch}.pdf";

                        } else {
                          // Generic fallback for other file types if neither image nor PDF
                          if (filename.isEmpty || filename == "F" || !filename.contains('.')) {
                            filename = "downloaded_file_${DateTime.now().millisecondsSinceEpoch}.dat";
                          }
                        }
                        // --- End of Image and PDF Handling Logic ---
                        Map<String, String> downloadHeaders = {
                          if (cookieHeader.isNotEmpty) 'Cookie': cookieHeader,
                          if (xsrfToken.isNotEmpty) 'X-XSRF-TOKEN': xsrfToken,
                          'User-Agent': request.userAgent ?? "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", // Mimic a browser User-Agent

                        };
                        final taskId = await FlutterDownloader.enqueue(
                          url: request.url.toString(),
                          headers: downloadHeaders,
                          savedDir: dir.path,
                          fileName: filename,
                          showNotification:false ,

                          saveInPublicStorage: true,
                        );

                        if (taskId != null) {
                          if (mounted) {
                            showMessage("File Downloaded, Please check your files", MessageType.success);
                            await FlutterDownloader.open(taskId: taskId);
                          }
                        } else {
                          if (mounted) {
                            showMessage("Failed to queue download.", MessageType.error);
                          }
                        }

                      } catch (e) {
                        if (mounted) {
                          showMessage("Error during download: $e", MessageType.error);
                        }
                        print("Error during download: $e");
                      }
                    },

                    onLoadStart: (controller, url) {
                      print("onLoadStart: $url");
                      if (mounted) {
                        setState(() {
                          currentUrl = url.toString();
                          urlController.text = currentUrl;
                          loading = true;
                        });
                      }
                    },
                    onLoadStop: (controller, url) async {
                      pullToRefreshController.endRefreshing();
                      if (mounted) {
                        setState(() {
                          currentUrl = url.toString();
                          urlController.text = currentUrl;
                          loading = false;
                        });
                      }
                      // After the page loads, call loadTableauViz if token is available
                      if (tableauToken != null) {
                        await webViewController!.evaluateJavascript(
                            source: "loadTableauViz('$tableauToken');");
                      }

                      // Hide Tableau banner if it appears
                      await webViewController!.evaluateJavascript(source: '''
                               (function hideBanner() {
                                 const interval = setInterval(() => {
                                   var banner = document.querySelector('.tab-app-banner');
                                   if (banner) {
                                     banner.style.display = "none";
                                     console.log("Banner hidden");
                                     clearInterval(interval);
                                   }
                                 }, 500);
                               })();
                               ''');
                    },
                    onLoadError: (controller, url, code, message) {
                      pullToRefreshController.endRefreshing();
                      if (mounted) {
                        showMessage("Load Error ($code): $message", MessageType.error);
                      }
                    },
                    onReceivedHttpError: (controller, request, response) {
                      String errorBody = response.data != null ? utf8.decode(response.data!.toList()) : "No response body";
                      if (mounted) {
                        // showMessage("HTTP Error ${response.statusCode}:\n$errorBody", MessageType.error);
                      }
                    },
                    onConsoleMessage: (controller, consoleMessage) {
                    },
                    androidOnPermissionRequest: (controller, origin, resources) async {
                      return PermissionRequestResponse(
                          resources: resources,
                          action: PermissionRequestResponseAction.GRANT);
                    },
                    shouldOverrideUrlLoading: (controller, navigationAction) async {
                      var uri = navigationAction.request.url!;

                      // Allow specific schemes within WebView, otherwise try to launch externally
                      if (![
                        "http",
                        "https",
                        "file",
                        "chrome",
                        "data",
                        "javascript",
                        "about"
                      ].contains(uri.scheme)) {
                        if (await canLaunchUrl(uri)) {
                          await launchUrl(uri, mode: LaunchMode.externalApplication);
                          return NavigationActionPolicy.CANCEL;
                        }
                      }
                      return NavigationActionPolicy.ALLOW;
                    },
                  ),
                  if (loading)
                    Positioned.fill(
                      child: Container(
                        color: Colors.white.withOpacity(0.8), // Semi-transparent overlay
                        alignment: Alignment.center,
                        child: const CircularProgressIndicator(),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

