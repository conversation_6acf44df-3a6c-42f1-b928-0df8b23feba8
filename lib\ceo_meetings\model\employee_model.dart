class EmployeeResponse {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final String? top;
  final List<Employee>? allElmEmploye;
  final String? authKey;
  final String? searchTerm;
  final String? exception;
  final String? skip;

  EmployeeResponse({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.top,
    this.allElmEmploye,
    this.authKey,
    this.searchTerm,
    this.exception,
    this.skip,
  });

  factory EmployeeResponse.fromJson(Map<String, dynamic> json) {
    return EmployeeResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      top: json['top'],
      allElmEmploye: (json['all_elm_employe'] as List)
          .map((e) => Employee.fromJson(e))
          .toList(),
      authKey: json['auth_key'],
      searchTerm: json['search_term'],
      exception: json['exception'],
      skip: json['skip'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'top': top,
      'all_elm_employe': allElmEmploye?.map((e) => e.toJson()).toList(),
      'auth_key': authKey,
      'search_term': searchTerm,
      'exception': exception,
      'skip': skip,
    };
  }
}

class Employee {
  final String? nameAr;
  final String? employeeId;
  final int? recId;
  final String? email;
  final String? nameEn;
  bool isSelected;

  Employee({
    this.nameAr,
    this.employeeId,
    this.recId,
    this.email,
    this.nameEn,
    this.isSelected = false,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      nameAr: json['name_ar'],
      employeeId: json['employeeid'],
      recId: json['recid'],
      email: json['email'],
      nameEn: json['name_en'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name_ar': nameAr,
      'employeeid': employeeId,
      'recid': recId,
      'email': email,
      'name_en': nameEn,
    };
  }
}
extension EmployeeCopy on Employee {
  Employee copyWith({
    String? nameAr,
    String? employeeId,
    int? recId,
    String? email,
    String? nameEn,
    bool? isSelected,
  }) {
    return Employee(
      nameAr: nameAr ?? this.nameAr,
      employeeId: employeeId ?? this.employeeId,
      recId: recId ?? this.recId,
      email: email ?? this.email,
      nameEn: nameEn ?? this.nameEn,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
