import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/secure_storage.dart';

abstract class IPettyCashRepo {
  Future<NetworkResponse> getCostCenterData(Map<String, dynamic> requestPayload);
  Future<NetworkResponse> getProjectWbsList();
  Future<NetworkResponse> submitRequest(Map<String, dynamic> requestPayload);
}

class PettyCashRequestRepo implements IPettyCashRepo {
  PettyCashRequestRepo._internal();

  static final PettyCashRequestRepo _instance =
      PettyCashRequestRepo._internal();

  static PettyCashRequestRepo get instance => _instance;

  @override
  Future<NetworkResponse> getCostCenterData(
      Map<String, dynamic> requestPayload) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await <PERSON><PERSON><PERSON><PERSON><PERSON>().apiCall(genericObject,
        body: requestPayload,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'formName': 'Cost Center Form',
          'moduleName': 'Cost Center',
          'appKey': 'PPC',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> getProjectWbsList() async{
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {'employeeid': null},
        requestType: RequestType.post,
        sessionToken:
        await SecureStorageService().readSecureData(key: "user_session") ?? '',
        headers: {
          "formName": "Project WPS Form",
          "moduleName": "Project WPS",
          "appKey": "NOT",
          "Content-Type": "application/json",
        });

    return response;

  }

  @override
  Future<NetworkResponse> submitRequest(
      Map<String, dynamic> requestPayload) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: requestPayload,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          'appKey': 'PPC',
          'Content-Type': 'application/json'
        });
    return response;
  }
}
