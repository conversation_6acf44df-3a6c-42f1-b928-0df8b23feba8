import 'package:eeh/letter_of_guarantee/view/widget/successScreenContent.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../../l10n/app_localizations.dart';
import '../../shared/app_constants.dart';
import '../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../shared/attachment_uda/bloc/attach_event.dart';
import '../../shared/attachment_uda/widget/attachment_component.dart';
import '../../shared/favorite_component/bloc/favorite_bloc.dart';
import '../../shared/favorite_component/bloc/favorite_event.dart';
import '../../shared/favorite_component/view/favorite_component.dart';
import '../../shared/network/endpoints.dart';
import '../../shared/styles/colors.dart';
import '../../shared/utility/methods.dart';
import '../../shared/utility/navigation_utility.dart';
import '../../shared/widgets/cancel_button_component.dart';
import '../../shared/widgets/currency_field_component.dart';
import '../../shared/widgets/customer_info_component/view/customer_info_component.dart';
import '../../shared/widgets/important_note.dart';
import '../../shared/widgets/new_custom_note_widget.dart';
import '../../shared/widgets/new_textformfield_component.dart';
import '../../shared/widgets/submit_button_component.dart';
import '../../shared/widgets/toast.dart';
import '../../success_view/success_screen_view.dart';
import '../../tender_request/views/widget/tender_utils.dart';
import '../bloc/letter_bloc.dart';
import '../bloc/letter_event.dart';
import '../bloc/letter_state.dart';
import '../models/Lists.dart';
import '../models/guaranteeDataResponse.dart';
import '../models/projectWbsModel.dart';
import '../repo/repo.dart';

class LetterOfGuaranteeScreen extends StatefulWidget {
  const LetterOfGuaranteeScreen({super.key});

  @override
  State<LetterOfGuaranteeScreen> createState() => _LetterOfGuaranteeScreenState();
}

class _LetterOfGuaranteeScreenState extends State<LetterOfGuaranteeScreen> {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  GlobalKey<FormFieldState> budgetTypeKey = GlobalKey<FormFieldState>(debugLabel: "budgetTypeKey");
  GlobalKey<FormFieldState> projectWBSKey = GlobalKey<FormFieldState>(debugLabel: "projectWBSKey");
  GlobalKey<FormFieldState> periodKey = GlobalKey<FormFieldState>(debugLabel: "periodKey");
  GlobalKey<FormFieldState> startDateKey = GlobalKey<FormFieldState>(debugLabel: "startDateKey");
  GlobalKey<FormFieldState> guaranteeTypeKey = GlobalKey<FormFieldState>(debugLabel: "guaranteeTypeKey");
  GlobalKey<FormFieldState> tenderValueKey = GlobalKey<FormFieldState>(debugLabel: "tenderValueKey");
  GlobalKey<FormFieldState> guaranteePercentKey = GlobalKey<FormFieldState>(debugLabel: "guaranteePercentKey");
  GlobalKey<FormFieldState> guaranteeValueKey = GlobalKey<FormFieldState>(debugLabel: "guaranteeValueKey");
  LetterBloc? bloc;
  GuaranteeDataResponse? response;
   ProjectWpsResponse? projectWpsResponse;
  TextEditingController dateTextController=TextEditingController();
  Color submitButtonColor = primaryColor.withOpacity(0.4);


  @override
  void initState() {
    super.initState();

  }  @override
  void dispose() {
    super.dispose();
    bloc?.budgetTypeController.dispose();
    bloc?.costCenterController.dispose();
    bloc?.customerController.dispose();
    bloc?.periodController.dispose();
    bloc?.dateController.dispose();
    dateTextController.dispose();
    bloc?.guaranteeTypeController.dispose();
    bloc?.tenderValueController.dispose();
    bloc?.guaranteePercentageController.dispose();
    bloc?.guaranteeValueController.dispose();
    bloc?.commentController.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<LetterBloc>(
          create: (context) => LetterBloc( GuaranteeRepo())
            ..add(LoadGuaranteeDataEvent()) ..add(LetterOfGuaranteeInitialEvent()),
        ),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
        BlocProvider<AttachBloc>(
          create: (BuildContext context) {
            return AttachBloc()..add(AttachInitialEvent());
          },
        )
      ],
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),

            child: Scaffold(
                resizeToAvoidBottomInset: false,
                appBar: getAppBarWidget(),
                body: BlocConsumer<LetterBloc, LetterState>(
            listenWhen: (previous, current) =>
                current is LetterInitialState ||
                current is ProjectsSuccessState ||
                current is ValidateFieldsState ||
                current is SubmitSuccessState ||
                current is UploadAttachmentFilesSuccessState ||
                current is GuaranteeDataSuccessState,
                    listener: (context, state) {
                      if (state is LetterInitialState) {

                        context.read<LetterBloc>().add(LoadGuaranteeDataEvent());
                        context.read<LetterBloc>().add(LoadProjectsEvent());
                      }
                      if(state is GuaranteeDataSuccessState) {
                        response =state.response;
                      }if(state is ProjectsSuccessState) {
                        projectWpsResponse=state.projectWpsResponse;
                      }
              if (state is SubmitSuccessState) {
                bloc?.add(UploadAttachmentFilesEvent());
              }
              if (state is UploadAttachmentFilesSuccessState) {
                Navigation.navigateToScreen(
                    context,
                    SuccessScreen(
                        title: AppLocalizations.of(context)
                            .translate("Request Submitted Successfully"),
                        contentWidget: SuccessScreencontentList(
                          submitResponse: bloc?.submitResponse,
                        ) //SuccessScreenContentWidgetL(submitResponse: bloc?.submitRequestResponse),
                        ));
              }
            },
                    builder: (context, state) {
                      bloc = context.read<LetterBloc>();
                      bloc?.ctx = context;
                      if (state is LetterInitialState || state is GuaranteeDataLoadingState || state is ProjectsLoadingState) {
                        return const Center(child: CircularProgressIndicator());
                      }

              return
                Form(
                  onChanged: () {
                   bloc?.add(ValidateFieldsEvent());

                  },
                key: formKey,
                child: Container(
                  padding: EdgeInsets.only(top: 2.h),
                  height: screenHeight(context),
                  child: Stack(
                    children: [
                      SizedBox(
                        height: screenHeight(context) - 190.h,
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 18.0),
                            child: Column(
                              children: <Widget>[
                                getBudgetType(),
                                _getTypeField(),
                              //  getCustomerName(),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                  child: CustomerInfoComponent(controller: bloc!.customerController,appKey: 'NOT', onSelect:
                                      (value) => bloc?.customerInfo = value,),
                                ),
                                getPeriod(),
                                getStartDate(),
                                getGuaranteeType(),
                                getTenderValues(),
                                getGuaranteePercentage(),
                                getGuaranteeValue(),
                                getCommentsWidget(),
                                getAttachment(),
                                getImportantNotes(),
                                const SafeArea(child: SizedBox(height: 65)),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Positioned(bottom: 0, left: 0, right: 0, child: getButtons()),
                    ],
                  ),
                ),
                );


                    },
                ),
            ),
      ),
    );
  }


  PreferredSize getAppBarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: FavoriteComponent(
        title: AppLocalizations.of(context).translate('Letter of Guarantee'),
        id: guaranteeletterID,
      ),
    );
  }

  Widget getBudgetType() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: NewCustomTextFieldComponent(
        validationText: AppLocalizations.of(context).translate("must select Budget Type"),
        key: budgetTypeKey,
        isReadOnly: true,
        isMandatory: true,
        labelText: AppLocalizations.of(context).translate('Budget Type'),
        type: TextFieldType.bottomSheet,
        controller:bloc?.budgetTypeController,
        bottomSheetText: AppLocalizations.of(context).translate('Budget Type'),
        bottomSheetContentList:departmentList,
        bottomSheetHeight: .25,
        onSelect: (value){
          bloc?.add(ChangeBudgetTypeEvent(value));
        },
      ),
    );
  }


  Widget _getTypeField() {
    return    BlocBuilder<LetterBloc, LetterState>(
      buildWhen: (current, previous) =>
          current is ChangeBudgetTypeState ||
          current is GuaranteeDataSuccessState ||
          current is ProjectsSuccessState,
      builder: (context, state) {
    bloc?.costCenterController.text=response?.costCenter?.first.costCenterCode??'';
      return( bloc?.isProject??false) ? getProject() : getCostCenter();
    },
    );

  }


  Widget getCostCenter()
  {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      child: BlocBuilder<LetterBloc, LetterState>(
        builder: (context, state) {
          bloc?.costCenterController.text=response?.costCenter?.first.costCenterCode??'';
          return    NewCustomTextFieldComponent(
            labelText: AppLocalizations.of(context).translate('Cost Center'),
            isDemmed: true,
            isReadOnly: true,
            controller: bloc?.costCenterController,
          );
        },
      ),
    );

  }


  Widget getProject() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: BlocConsumer<LetterBloc, LetterState>(
        listener: (context, state) {
        },
        buildWhen: (current, previous) =>
        current is ProjectsSuccessState ||
        current is ChangeBudgetTypeState ||
            current is ProjectsLoadingState ||
            current is ProjectsErrorState,
        builder: (context, state) {
          if (state is ProjectsLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }   return NewCustomTextFieldComponent(
            validationText: AppLocalizations.of(context).translate("must select Project"),
            key: projectWBSKey,
            bottomSheetHasSearch: true,
            labelText: AppLocalizations.of(context).translate('Project WBS'),
            type: TextFieldType.bottomSheet,
            isMandatory: true,
            isReadOnly: true,
            controller: bloc?.projectController,
            onSelect: (value){
              bloc?.selectedProjectID= (value as ProjectWps ).wbscode??'';
            },
            bottomSheetContentList: projectWpsResponse?.projectWps??[],
            bottomSheetText: AppLocalizations.of(context).translate('Project WBS'),
          );
        },
      ),
    );
  }


  getCustomerName() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: NewCustomTextFieldComponent(
        labelText: AppLocalizations.of(context).translate('Customer Name'),
        isMandatory: true,type: TextFieldType.bottomSheet,
        bottomSheetText: AppLocalizations.of(context).translate('Customer Name'),bottomSheetContentList: ['Customer Name1','Customer Name2'],
      ),
    );
  }

  Widget getPeriod() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: BlocBuilder<LetterBloc, LetterState>(
        builder: (context, state) {
          if (state is GuaranteeDataLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }

            return NewCustomTextFieldComponent(
              validationText: AppLocalizations.of(context).translate("please select period"),
              key: periodKey,
              isDemmed: false,
              isMandatory: true,
              isReadOnly: true,
                bottomSheetHeight:.4,
              labelText: AppLocalizations.of(context).translate('Period (days)'),
              bottomSheetText: AppLocalizations.of(context).translate('Period (days)'),
              type: TextFieldType.bottomSheet,
              controller: bloc?.periodController,
              bottomSheetContentList: (response?.perioddays),
              onSelect: ( value){
                bloc?.selectedPeriodID=(value as Perioddays).id??'';
              },

            );
          }
        ,
      ),
    );
  }

  Widget getStartDate() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: NewCustomTextFieldComponent(
        validationText: AppLocalizations.of(context).translate("must select date"),
        key: startDateKey,
        type: TextFieldType.calendar,
        labelText: AppLocalizations.of(context).translate('Start Date'),
        isMandatory: true,
        controller: dateTextController,
        onDateChange:(){
          dateTextController.text=  DateFormat('yyyy-MM-dd')
              .format(bloc?.dateController.selectedDate??DateTime.now());
        } ,
        dateRangePickerController: bloc?.dateController,
      ),
    );
  }

  Widget getGuaranteeType() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: BlocBuilder<LetterBloc, LetterState>(
        builder: (context, state) {
            return NewCustomTextFieldComponent(
              validationText: AppLocalizations.of(context).translate("must select guarantee type"),
              key: guaranteeTypeKey,
              type: TextFieldType.bottomSheet,
              labelText: AppLocalizations.of(context).translate('Guarantee Type'),
              isMandatory: true,
              isReadOnly: true,
              bottomSheetHeight: .4,
              controller:bloc?.guaranteeTypeController,
              bottomSheetText: AppLocalizations.of(context).translate('Guarantee Type'),
              bottomSheetContentList: response?.guaranteetype??[],
              onSelect: (value) {
                bloc?.add(ChangeGuaranteeTypeEvent(value));
              },
            );

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget getTenderValues() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: CurrencyTextFieldComponent(
        validationText: AppLocalizations.of(context).translate("must select Tender Value"),
        key: tenderValueKey,
        onChange: (_) {
          bloc?.add(ChangeGuaranteeValueEvent());
        },
        labelText: AppLocalizations.of(context).translate('Tender Value'),
        controller: bloc?.tenderValueController,
        isMandatory: true,
      ),
    );
  }
  //
  // Widget getPeriod(LetterBloc bloc) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 8.0),
  //     child: NewCustomTextFieldComponent(
  //       isDemmed: false,
  //       isMandatory: true,
  //       isReadOnly: true,
  //       labelText: AppLocalizations.of(context).translate('Period (days)'),
  //       bottomSheetText: AppLocalizations.of(context).translate('Period (days)'),
  //       type: TextFieldType.bottomSheet,
  //       controller: periodController,
  //       bottomSheetContentList: bloc.periodDays,
  //     ),
  //   );
  // }



  Widget getGuaranteePercentage() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: NewCustomTextFieldComponent(
        key: guaranteePercentKey,
        type: (bloc?.guaranteeTypeID??'').isNotEmpty?TextFieldType.bottomSheet:TextFieldType.normal,
        controller: bloc?.guaranteePercentageController,
        labelText: AppLocalizations.of(context).translate('Guarantee Percentage (%)'),
        isMandatory: true,
        isReadOnly: true,
        validationText: AppLocalizations.of(context).translate("must select guarantee percentage"),
        bottomSheetHeight: 0.3,
        bottomSheetText: AppLocalizations.of(context).translate('Guarantee Percentage (%)'),
        bottomSheetContentList:bloc?.getFilteredPercentages(response?.guarantPercent??[]),
        onSelect: (value) {
          bloc?.add(ChangeGuaranteePercentEvent(value));
        },
      ),
    );
  }



  Widget getGuaranteeValue() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: CurrencyTextFieldComponent(
        key: guaranteeValueKey,
        isSARComponentHasBrackets: true,
        controller: bloc?.guaranteeValueController,
        labelText: AppLocalizations.of(context).translate('Guarantee Value'),
        isDemmed: true,
        isReadOnly: true,
      ),
    );
  }

  Widget getCommentsWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8, top: 10),
      child: CustomNoteWidget(
        hintText: AppLocalizations.of(context).translate("Write a Comments"),
        labelText: AppLocalizations.of(context).translate("Comments:"),
      ),
    );
  }

  Widget getAttachment() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: AttachUI(
        label: AppLocalizations.of(context).translate("File:"),
        isRequired: true,
        color: primaryColor,
        endPoint: deleteLetterOfGuarantee,
        title: AppLocalizations.of(context).translate('Attachment'),
        serviceType: 'Letterofguarantee',
        subTitle: AppLocalizations.of(context).translate('Click to upload'),
        onFilesUpdated: (){
          bloc?.add(FilesUpdatedEvent());
        },
      ),
    );
  }

  Widget getImportantNotes() {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, right: 8, bottom: 35),
      child: ImportantNote(
        title: AppLocalizations.of(context).translate("Important Note:"),
        notes: [
          AppLocalizations.of(context).translate("Please attach the tender document"),
          AppLocalizations.of(context).translate('5 Days (Starting after complete all approval steps)'),
        ],
      ),
    );
  }

  Widget getButtons() {
    return BlocConsumer<LetterBloc, LetterState>(
      listenWhen: (current, previous) =>
          current is ValidateFieldsState,
      listener: (context, state) {
        if(state is ValidateFieldsState) {
          submitButtonColor = (state).isValid
              ? primaryColor
              : primaryColor.withOpacity(.4);
        }

      },
      // listenWhen: (current, previous) => current is SubmitSuccessState||  current is ValidateFieldsState  ,
      buildWhen: (current, previous) =>
      current is FilesUpdatedState ||
      current is UploadAttachmentFilesSuccessState ||
      current is UploadAttachmentFilesErrorState ||
      current is UploadAttachmentLoadingState ||
      current is ValidateFieldsState ||
      current is SubmitSuccessState ||
      current is SubmitLoadingState ||
        current is LetterInitialState,
      builder: (context, state) {
        final bool hasFiles = AttachBloc.instance(context).files.isNotEmpty;
        final tenderValueText = bloc!.tenderValueController.text;
        final tenderValue = double.tryParse(tenderValueText) ?? 0;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            const CancelButtonComponent(),
            SubmitButtonComponent(
              text: AppLocalizations.of(context).translate("Submit"),
              isLoading: state is SubmitLoadingState || state is UploadAttachmentLoadingState,
              onPressed: () {
                if(tenderValue <= 0){
                  showMessage(
                    AppLocalizations.of(context).translate("Value Should be more than or equal 1"),
                    MessageType.warning,
                  );
                  return;
                }
                if(formKey.currentState!.validate() && hasFiles) {
                  bloc?.add(SubmitLetterOfGuaranteeEvent());
                }else if (!hasFiles) {
                  showMessage(
                    AppLocalizations.of(context).translate("Must add at least one attachment"),
                    MessageType.warning,
                  );
                }
              },
              backGroung:(bloc!.isAllFieldsValid() && hasFiles) ? primaryColor :  primaryColor.withOpacity(0.2),
            ),
          ],
        );
      },
    );;
  }
}