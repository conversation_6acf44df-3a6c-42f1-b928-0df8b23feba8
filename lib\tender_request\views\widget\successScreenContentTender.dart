
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/widgets/sar_component.dart';
import '../../../success_view/success_screen_view.dart';
import '../../model/submit_tender_request_model.dart';

class SuccessScreenContentWidgetTender extends StatelessWidget {
  final SubmitTenderRequestModel? submitResponse;
  const SuccessScreenContentWidgetTender(
      {super.key, required this.submitResponse});


  @override
  Widget build(BuildContext context) {
    return submitResponse == null
        ? SizedBox.shrink()
        : SingleChildScrollView(
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderText(AppLocalizations.of(context).translate('Request Type')),
            buildDetailsText((AppLocalizations.appLang == 'en' ? submitResponse?.requestType : submitResponse?.requestTypeAr) ?? '' ),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate('Request ID')),
            buildDetailsText(submitResponse?.displayRecId?.toString()??''),
            buildDivider(),

           buildHeaderText(AppLocalizations.of(context).translate('Budget Type')),
           buildDetailsText((AppLocalizations.appLang == 'en' ? submitResponse?.budgetTypeEn : submitResponse?.budgetTypeAr) ?? '' ),
           buildDivider(),

            ((submitResponse?.costCenterId != null ) && (submitResponse?.budgetTypeEn == "Department") ) ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(AppLocalizations.of(context).translate('Cost Center')),
                buildDetailsText(submitResponse?.costCenterId ?? ''),
                buildDivider(),
              ],
            ): SizedBox.shrink(),

             ( submitResponse?.projectNameEn !=null ) ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(AppLocalizations.of(context).translate("Project Name")),
                buildDetailsText(submitResponse?.projectNameEn  ?? ''),
                buildDivider(),
              ],
            ): SizedBox.shrink(),


            buildHeaderText(AppLocalizations.of(context).translate('Tender Name')),
            buildDetailsText(submitResponse?.tenderName ?? ''),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate('Tender Number')),
            buildDetailsText(submitResponse?.tenderNumber ?? ''),
            buildDivider(),


            buildHeaderText(AppLocalizations.of(context).translate("Tender Value")),
            buildCurrencyDetailsText(submitResponse?.tenderValue ?? ''),
            buildDivider(),

              buildHeaderText(AppLocalizations.of(context).translate('Customer Name')),
              buildDetailsText(AppLocalizations.appLang == 'en' ? submitResponse?.customerNameE ?? '' :submitResponse?.customerNameA ?? '' ),
              buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate("Customer ID")),
            buildDetailsText(submitResponse?.customerId ?? '' ),
            buildDivider(),

             buildHeaderText(AppLocalizations.of(context).translate('Payment Type')),
             buildDetailsText(AppLocalizations.appLang == 'en' ? submitResponse?.paymentTypeEn ?? '' :submitResponse?.paymentTypeAr ?? '' ),
             buildDivider(),




           submitResponse?.sadadNumber != null ? Column(
            // mainAxisAlignment: MainAxisAlignment.start,
             crossAxisAlignment: CrossAxisAlignment.start,
             children: [
               buildHeaderText(AppLocalizations.of(context).translate('SADAD Number')),
               buildDetailsText(submitResponse?.sadadNumber ?? ''),
               buildDivider(),
             ],
           ): SizedBox.shrink(),

            submitResponse?.beneficiaryName != null ? Column(
              // mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(AppLocalizations.of(context).translate("Beneficiary Name")),
                buildDetailsText(submitResponse?.beneficiaryName ?? ''),
                buildDivider(),
              ],
            ): SizedBox.shrink(),

            (( submitResponse?.bankNameEn != null) || ( submitResponse?.bankNameAr != null) ) ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(AppLocalizations.of(context).translate("Bank")),
                buildDetailsText((AppLocalizations.appLang == 'en' ? submitResponse?.bankNameEn : submitResponse?.bankNameAr) ?? '' ),

                buildDivider(),
              ],
            ): SizedBox.shrink(),

            submitResponse?.ibanNumber != null ? Column(
              // mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(AppLocalizations.of(context).translate("IBAN")),
                buildDetailsText(submitResponse?.ibanNumber ?? ''),
                buildDivider(),
              ],
            ): SizedBox.shrink(),

          ],
        ),
      ),
    );
  }
}
