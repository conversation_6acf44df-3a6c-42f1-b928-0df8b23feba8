import 'package:eeh/allowance_services/model/allawance_list_response_model.dart';
import 'package:eeh/allowance_services/model/submit_allowance_request_body.dart';
import 'package:eeh/allowance_services/model/submit_allowance_response_model.dart';
import 'package:eeh/allowance_services/new_allowance/bloc/new_allowance_event.dart';
import 'package:eeh/allowance_services/new_allowance/bloc/new_allowance_state.dart';
import 'package:eeh/allowance_services/repo/allowance_repo.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class NewAllowanceBloc extends Bloc<NewAllowanceEvent, NewAllowanceState> {
  late AllowanceRepo repo;
  final TextEditingController beneficialEmployeeController =
      TextEditingController();
  final TextEditingController numberOfMonthsController =
      TextEditingController();
  final TextEditingController startDateTextController = TextEditingController();
  final TextEditingController endDateTextController = TextEditingController();
  final DateRangePickerController startDateController =
      DateRangePickerController();
  final DateRangePickerController endDateController =
      DateRangePickerController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController commentController = TextEditingController();
  final TextEditingController eligableController = TextEditingController();
  AllowanceListResponse? allowanceListResponse;
  List<Employee> allowanceCardList = [];
  Employee? selectedEmployee;
  NumOfMonths? selectedMonths;
  bool isGas = false;
  late BuildContext ctx;
  SubmitAllowanceResponse? submitResponse;

  NewAllowanceBloc() : super(NewAllowanceInitialState()) {
    on<NewAllowanceInitialEvent>((event, emit) {
      repo = AllowanceRepo(isGas: event.isGas);
      isGas = event.isGas;
      numberOfMonthsController.text =
          AppLocalizations.appLang == 'en' ? '6Months' : '6 شهور';
      emit(NewAllowanceInitialState());
    });

    on<BeneficialEmployeeValueChangelEvent>((event, emit) {
      beneficialEmployeeValueChangel(event.value);
      emit(BeneficialEmployeeValueChangelState());
    });

    on<NumberOfMonthsValueChangelEvent>((event, emit) {
      selectMonthsValueChange(event.value);
      emit(NumberOfMonthsValueChangelState());
    });

    on<StartDateValueChangelEvent>((event, emit) {
      startDateValueChange(emit);
    });

    on<CalculateEndDateEvent>((event, emit) {
      calculateEndDate();
      emit(CalculateEndDateState());
    });

    on<EndDateValueChangelEvent>((event, emit) {
      endDateValueChange();
      emit(EndDateValueChangelState());
    });

    on<AddEmployeeEvent>((event, emit) {
      onAddEmployeeButtonPressed();
      emit(AddEmployeeState());
    });

    on<ActionTappedEvent>((event, emit) {
      deleteEmployee(event.index);
      emit(ActionTappedState());
    });

    on<EmployeeAllowanceEvent>((event, emit) async {
      emit(EmployeeAllowanceLoadingState());
      await fetchEmployeeAllowance(emit);
    });

    on<SubmitAllowanceEvent>((event, emit) async {
      emit(SubmitAllowanceLoadingState());
      await submitAllowance(emit);
    });
  }

  beneficialEmployeeValueChangel(value) {
    if (value != null) {
      if (!allowanceCardList
          .any((card) => card.employeeid == value?.employeeid)) {
        selectedEmployee = value;
        eligableController.text =
            selectedEmployee?.eligibleamount.toString() ?? '';
      } else {
        clearForm();
        showMessage(
            AppLocalizations.of(ctx)
                .translate('This employee is already added'),
            MessageType.error);
      }
    }
  }

  bool isAddButtonEnabled() {
    return beneficialEmployeeController.text.isNotEmpty &&
        numberOfMonthsController.text.isNotEmpty &&
        startDateTextController.text.isNotEmpty &&
        endDateTextController.text.isNotEmpty;
  }

  calculateEndDate() {
    DateTime startDate = DateTime.parse(startDateTextController.text);
    int numberOfMonths = int.tryParse((RegExp(r'\d+')
                .firstMatch(numberOfMonthsController.text)
                ?.group(0)) ??
            '') ??
        0;
    DateTime endDate = DateTime(
        startDate.year, startDate.month + numberOfMonths, startDate.day);
    endDateTextController.text = DateFormat('yyyy-MM-dd').format(endDate);
  }

  startDateValueChange(emitter) {
    if (startDateController.selectedDate != null) {
      DateTime selectedDate = DateTime(
        startDateController.selectedDate!.year,
        startDateController.selectedDate!.month,
        startDateController.selectedDate!.day,
      );

      DateTime currentDate = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
      );
      if (selectedDate.isAtSameMomentAs(currentDate) ||
          selectedDate.isAfter(currentDate)) {
        startDateTextController.text =
            DateFormat('yyyy-MM-dd').format(startDateController.selectedDate!);
        emitter(StartDateValueChangelState());
        add(CalculateEndDateEvent());
      } else {
        startDateTextController.clear();
        endDateTextController.clear();
        showMessage(
            AppLocalizations.of(ctx).translate(
                "Past dates are invalid. Please choose a valid date"),
            MessageType.error);
        emitter(StartDateValueChangelState());
      }
    }
  }

  endDateValueChange() {
    endDateTextController.text = DateFormat('yyyy-MM-dd')
        .format(endDateController.selectedDate ?? DateTime.now());
  }

  selectMonthsValueChange(value) {
    if (value != null) {
      selectedMonths = value;
    }
  }

  onAddEmployeeButtonPressed() {
    String comment = (commentController.text.trim().isNotEmpty)
        ? commentController.text
        : "_";
    Employee item = Employee(
        employeenameen: selectedEmployee?.employeenameen ?? '',
        employeenamear: selectedEmployee?.employeenamear ?? '',
        numberofmonths: numberOfMonthsController.text,
        startdate: startDateTextController.text,
        enddate: endDateTextController.text,
        employeeid: selectedEmployee?.employeeid,
        comment: comment,
        eligibleamount: selectedEmployee?.eligibleamount);
    if (!allowanceCardList.any((card) => card.employeeid == selectedEmployee?.employeeid)) {
      allowanceCardList.add(item);
    } 
    clearForm();
  }

  clearForm() {
    beneficialEmployeeController.clear();
    startDateTextController.clear();
    endDateTextController.clear();
    commentController.clear();
    eligableController.clear();
    startDateController.selectedDate = null;
    numberOfMonthsController.text =
        AppLocalizations.appLang == 'en' ? '6 Months' : '6 شهور';
  }

  deleteEmployee(int index) {
    allowanceCardList.removeAt(index);
  }

  fetchEmployeeAllowance(emitter) async {
    await repo
        .fetchAllownceList(requestType: 'new')
        .then((response) => onFetchEmployeeAllowance(response, emitter));
  }

  onFetchEmployeeAllowance(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchEmployeeAllowanceSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(EmployeeAllowanceErrorState());
    });
  }

  onFetchEmployeeAllowanceSuccess(data, emitter) {
    allowanceListResponse = AllowanceListResponse.fromJson(data);
    emitter(EmployeeAllowanceSuccessState());
  }

  submitAllowance(emitter) async {
    await repo
        .supmitAllowance(
            employeesAllowa: getRequestBody(), appKey: isGas ? 'GAS' : 'CAS')
        .then((response) => onSubmitAllowance(response, emitter));
  }

  onSubmitAllowance(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onSubmitAllowanceSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(SubmitAllowanceErrorState());
    });
  }

  onSubmitAllowanceSuccess(data, emitter) {
    submitResponse = SubmitAllowanceResponse.fromJson(data);
    emitter(SubmitAllowanceSuccessState());
  }

  getRequestBody() {
    List<Employees> body = allowanceCardList.map((emp) {
      return Employees(
        employeeId: getNumofMon(emp.employeeid ?? ''),
        employeeNameAr: emp.employeenamear,
        employeeNameEn: emp.employeenameen,
        startDate: emp.startdate,
        endDate: emp.enddate,
        eligibleAmount: emp.eligibleamount,
        numberOfMonths: getNumofMon(emp.numberofmonths ?? ''),
        comments: emp.comment,
      );
    }).toList();
    return SupmitAllowanceBody(
        employeesallowa: body, numOfEmps: allowanceCardList.length.toString());
  }

  getNumofMon(String num) {
    final match = RegExp(r'\d+').firstMatch(num);
    int? numofMon = match != null ? int.tryParse(match.group(0)!) : null;
    return numofMon;
  }
}
