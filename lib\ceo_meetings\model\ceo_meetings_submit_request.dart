class CEOMeetingRequest {
  final bool relatedBoardD;
  final String meetCategoryId;
  final String requiredMeetId;
  final String purposeMeetId;
  final String meetingTypeId;
  final String priorityId;
  final String meetingSubject;
  final String meetingDate;
  final String startTime;
  final String endTime;
  final String agenda;
  final String whatDoWeExpect;
  final List<CeoEmployee> ceoGroups;
  final List<CeoEmployee> meetingAttendees;
  final String noteTakerId;
  final String requesterId;
  final String notes;

  CEOMeetingRequest({
    required this.relatedBoardD,
    required this.meetCategoryId,
    required this.requiredMeetId,
    required this.purposeMeetId,
    required this.meetingTypeId,
    required this.priorityId,
    required this.meetingSubject,
    required this.meetingDate,
    required this.startTime,
    required this.endTime,
    required this.agenda,
    required this.whatDoWeExpect,
    required this.ceoGroups,
    required this.meetingAttendees,
    required this.noteTakerId,
    required this.requesterId,
    required this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'related_board_d': relatedBoardD,
      'meet_categoryid': meetCategoryId,
      'required_meetid': requiredMeetId,
      'purpose_meet_id': purposeMeetId,
      'meeting_type_id': meetingTypeId,
      'priority_id': priorityId,
      'meeting_subject': meetingSubject,
      'meeting_date': meetingDate,
      'start_time': startTime,
      'end_time': endTime,
      'agenda': agenda,
      'what_do_we_expe': whatDoWeExpect,
      'ceo_groups': ceoGroups.map((e) => e.toJson()).toList(),
      'meeting_attende': meetingAttendees.map((e) => e.toJson()).toList(),
      'note_taker_id': noteTakerId,
      'm_requester_id': requesterId,
      'notes': notes,
    };
  }
}

class CeoEmployee {
  final String employeeId;

  CeoEmployee({
    required this.employeeId,
  });

  factory CeoEmployee.fromJson(Map<String, dynamic> json) {
    return CeoEmployee(
      employeeId: json['employeeid'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employeeid': employeeId,
    };
  }
}
