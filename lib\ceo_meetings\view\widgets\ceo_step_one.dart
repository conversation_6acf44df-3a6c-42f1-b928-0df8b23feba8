import 'package:eeh/ceo_meetings/view/widgets/CEO_BottomSheet.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/validator_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/widgets/new_textformfield_component.dart';
import '../../bloc/ceo_meetings_bloc.dart';
import '../../bloc/ceo_meetings_events.dart';
import '../../bloc/ceo_meetings_states.dart';

class CEOStepOneView extends StatefulWidget {
  const CEOStepOneView({super.key});

  @override
  State<CEOStepOneView> createState() => _CEOStepOneViewState();
}

class _CEOStepOneViewState extends State<CEOStepOneView> {
  late CEOMeetingsBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CEOMeetingsBloc, CEOMeetingsState>(
        listenWhen: (previous, current) =>
            current is CEOMeetingsInitialFinishState ||
            current is GetElmCEOMembersRequestSuccessState ||
            current is GetDropdownListDataRequestSuccessState,
        listener: (context, state) {
          if (state is CEOMeetingsInitialFinishState) {
            _bloc.add(GetElmCEOMembersRequestEvent());
          }
        },
        buildWhen: (previous, current) =>
            current is CEOMeetingsInitialFinishState ||
            current is GetElmCEOMembersRequestSuccessState ||
            current is GetElmCEOMembersRequestErrorState ||
            current is GetElmCEOMembersRequestLoadingState ||
            current is GetDropdownListDataRequestLoadingState ||
            current is GetDropdownListDataRequestSuccessState ||
            current is GetDropdownListDataRequestErrorState,
        builder: (context, state) {
          _bloc = context.read<CEOMeetingsBloc>();
          return state is CEOMeetingsInitialFinishState ||
                  state is GetElmCEOMembersRequestSuccessState ||
                  state is GetDropdownListDataRequestSuccessState ||
                  state is MoveToScreenState
              ? SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Padding(
                            padding: EdgeInsets.symmetric(horizontal: 0.0.w),
                            child: Column(children: [
                              SizedBox(
                                height: 10.h,
                              ),
                              getCEOGroupsTextFieldWidget(),
                              SizedBox(
                                height: 15.h,
                              ),
                              getMeetingCategoryTextFieldWidget(),
                              SizedBox(
                                height: 15.h,
                              ),
                              getRequiredFromTheMeetingTextFieldWidget(),
                              SizedBox(
                                height: 15.h,
                              ),
                              getPurposeOfTheMeetingTextFieldWidget(),
                              SizedBox(
                                height: 15.h,
                              ),
                              getMeetingTypeTextFieldWidget(),
                              SizedBox(
                                height: 15.h,
                              ),
                              getPriorityTextFieldWidget(),
                              SizedBox(
                                height: 15.h,
                              ),
                              getCheckBoxWidget(),
                              SizedBox(
                                height: 10.h,
                              )
                            ])),
                      ],
                    ),
                  ))
              : Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 50.0),
                    child: const CircularProgressIndicator(),
                  ),
                );
        });
  }

  Widget getCEOGroupsTextFieldWidget() {
    return NewCustomTextFieldComponent(
      isMandatory: true,
      isReadOnly: true,
      textLabelStyle: TextStyle(
          fontWeight: FontWeight.normal, fontSize: 15, color: secondTextColor),
      textStyle:
          TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      labelText: AppLocalizations.of(context).translate('CEO / CEO Groups'),
      type: TextFieldType.bottomSheet,
      onTap: () async => getCEOMembersBottomSheet(context),
      controller: _bloc.ceoGroupsController,
      bottomSheetContentList: _bloc.ceoDropdownList?.meetingCategor,
    );
  }

  Widget getMeetingCategoryTextFieldWidget() {
    return NewCustomTextFieldComponent(
      isMandatory: true,
      isReadOnly: false,
      textLabelStyle: TextStyle(
          fontWeight: FontWeight.normal, fontSize: 15, color: secondTextColor),
      textStyle:
          TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      labelText: AppLocalizations.of(context).translate('Meeting category'),
      bottomSheetText:
          AppLocalizations.of(context).translate('Meeting category'),
      type: TextFieldType.bottomSheet,
      controller: _bloc.meetingCategoryController,
      bottomSheetContentList: _bloc.ceoDropdownList?.meetingCategor,
      onSelect: (value) {
        _bloc.meetingCategoryController.text = value.id;
        _bloc.selectedMeetCategoryId = value;
        context.read<CEOMeetingsBloc>().add(ChangeFieldValueEvent());
      },
      bottomSheetHeight: 0.32.h,
    );
  }

  Widget getRequiredFromTheMeetingTextFieldWidget() {
    return NewCustomTextFieldComponent(
      isMandatory: true,
      isReadOnly: false,
      textLabelStyle: TextStyle(
          fontWeight: FontWeight.normal, fontSize: 15, color: secondTextColor),
      textStyle:
          TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      labelText:
          AppLocalizations.of(context).translate('Required from the meeting'),
      bottomSheetText:
          AppLocalizations.of(context).translate('Required from the meeting'),
      type: TextFieldType.bottomSheet,
      controller: _bloc.requiredController,
      bottomSheetContentList: _bloc.ceoDropdownList?.requiredMeetin,
      onSelect: (value) {
        _bloc.requiredController.text = value.id;
        _bloc.selectedRequiredMeetId = value;
        context.read<CEOMeetingsBloc>().add(ChangeFieldValueEvent());
      },
      bottomSheetHeight: 0.32.h,
    );
  }

  Widget getPurposeOfTheMeetingTextFieldWidget() {
    return NewCustomTextFieldComponent(
      isDemmed: false,
      isMandatory: true,
      isReadOnly: true,
      textLabelStyle: TextStyle(
          fontWeight: FontWeight.normal, fontSize: 15, color: secondTextColor),
      textStyle:
          TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      labelText:
          AppLocalizations.of(context).translate('Purpose of the meeting'),
      bottomSheetText:
          AppLocalizations.of(context).translate('Purpose of the meeting'),
      type: TextFieldType.bottomSheet,
      controller: _bloc.purposeController,
      bottomSheetContentList: _bloc.ceoDropdownList?.purposeMeeting,
      onSelect: (value) {
        _bloc.purposeController.text = value.id;
        _bloc.selectedPurposeMeetId = value;
        context.read<CEOMeetingsBloc>().add(ChangeFieldValueEvent());
      },
      bottomSheetHeight: 0.32.h,
    );
  }

  Widget getMeetingTypeTextFieldWidget() {
    return NewCustomTextFieldComponent(
      isDemmed: false,
      isMandatory: true,
      isReadOnly: true,
      textLabelStyle: TextStyle(
          fontWeight: FontWeight.normal, fontSize: 15, color: secondTextColor),
      textStyle:
          TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      labelText: AppLocalizations.of(context).translate('Meeting type'),
      bottomSheetText: AppLocalizations.of(context).translate('Meeting type'),
      type: TextFieldType.bottomSheet,
      controller: _bloc.meetingTypeController,
      bottomSheetContentList: _bloc.ceoDropdownList?.ceoMeetingTyp,
      onSelect: (value) {
        _bloc.meetingTypeController.text = value.id;
        _bloc.selectedMeetingTypeId = value;
        context.read<CEOMeetingsBloc>().add(ChangeFieldValueEvent());
      },
      bottomSheetHeight: 0.32.h,
    );
  }

  Widget getCheckBoxWidget() {
    return BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
      buildWhen: (previous, current) => current is ChangeFieldValueState,
      builder: (context, state) => Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: Color.fromRGBO(185, 200, 252, 0.1),
            border: Border.all(
              color: borderColor,
              width: 1.5.w,
            ),
            borderRadius: BorderRadius.circular(4.sp),
          ),
          child: ValidatorComponent(
            onChange: (value) {
              context.read<CEOMeetingsBloc>().add(ChangeFieldValueEvent());
              _bloc.checkBoxValue = value;
            },
            value: _bloc.checkBoxValue,
            preText: AppLocalizations.of(context)
                .translate("related_to_board_of_directors"),
            titleStyle:
                FontUtilities.getTextStyle(TextType.regular, size: 14.sp),
          )),
    );
  }

  Widget getPriorityTextFieldWidget() {
    return NewCustomTextFieldComponent(
      isDemmed: false,
      isMandatory: true,
      isReadOnly: true,
      textLabelStyle: TextStyle(
          fontWeight: FontWeight.normal, fontSize: 15, color: secondTextColor),
      textStyle:
          TextStyle(fontWeight: FontWeight.w500, fontSize: 15, color: textMain),
      labelText: AppLocalizations.of(context).translate('Priority'),
      bottomSheetText: AppLocalizations.of(context).translate('Priority'),
      type: TextFieldType.bottomSheet,
      controller: _bloc.priorityController,
      bottomSheetContentList: _bloc.ceoDropdownList?.ceoPriority,
      onSelect: (value) {
        _bloc.priorityController.text = value.id;
        _bloc.selectedPriorityId = value;
        context.read<CEOMeetingsBloc>().add(ChangeFieldValueEvent());
      },
      bottomSheetHeight: 0.32.h,
    );
  }

  Future<void> getCEOMembersBottomSheet(BuildContext context) async {
    FocusScope.of(context).unfocus();
    await showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        showDragHandle: true,
        backgroundColor: Colors.white,
        builder: (context) {
          return CEOBottomSheetWidget(bloc: _bloc, onItemSelect: (item) {});
        });
  }
}
