import 'package:eeh/letter_of_guarantee/models/guaranteeDataResponse.dart';
import 'package:equatable/equatable.dart';

import '../models/Lists.dart';


abstract class LetterEvent extends Equatable {
  const LetterEvent();
  @override
  List<Object> get props => [];
}

class LetterOfGuaranteeInitialEvent extends LetterEvent {}

class LoadGuaranteeDataEvent extends LetterEvent {}

class ChangeGuaranteeTypeEvent extends LetterEvent {
  final Guaranteetype guaranteeType;
  const ChangeGuaranteeTypeEvent(this.guaranteeType);
}

class ChangeGuaranteePercentEvent extends LetterEvent {
  final GuarantPercent guaranteePercent;
  const ChangeGuaranteePercentEvent(this.guaranteePercent);
}
class ChangeGuaranteeValueEvent extends LetterEvent {
}

class ChangeBudgetTypeEvent extends LetterEvent {
  final BudgetTypeModel budgetType;
  const ChangeBudgetTypeEvent(this.budgetType);
}

class SubmitLetterOfGuaranteeEvent extends LetterEvent{}

class GetGuaranteeTypeListEvent extends LetterEvent {}

class GetGuaranteePercentageListEvent extends LetterEvent {}

class GetPeriodDaysListEvent extends LetterEvent {}
class ValidateFieldsEvent extends LetterEvent {}


class LoadProjectsEvent extends LetterEvent {}


class CustomerListEvent extends LetterEvent {}

class SelectPeriodEvent extends LetterEvent {
  SelectPeriodEvent(GuarantPercent selectedPeriod);
}

class UploadAttachmentFilesEvent extends LetterEvent{}

class FilesUpdatedEvent extends LetterEvent{}



