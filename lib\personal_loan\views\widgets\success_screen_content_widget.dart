import 'package:eeh/iqama_services/model/submit_response.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/personal_loan/models/submit_presonal_loan_response_model/submit_presonal_loan_response_model.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';

class SuccessScreenContentWidget extends StatelessWidget {
  final SubmitPersonalLoanResponseModel? submitResponse;
  final String? monthlyAmount;
  const SuccessScreenContentWidget({
    super.key,
    required this.submitResponse,
    required this.monthlyAmount,
  });

  @override
  Widget build(BuildContext context) {
    return submitResponse == null
        ? SizedBox.shrink()
        : SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(
                    AppLocalizations.of(context).translate("Request Type")),
                buildDetailsText((AppLocalizations.appLang == 'en')
                    ? submitResponse?.requestType.toString() ?? ''
                    : submitResponse?.requestTypeAr.toString() ?? ''),
                buildDivider(),
                buildHeaderText(
                    AppLocalizations.of(context).translate("Request ID")),
                buildDetailsText(
                    submitResponse?.display_recid.toString() ?? ''),
                buildDivider(),
                buildHeaderText(
                    AppLocalizations.of(context).translate('Loan Amount')),
                buildCurrencyDetailsText(
                    submitResponse?.loanamount.toString() ?? ''),
                buildDivider(),
                buildHeaderText(AppLocalizations.of(context)
                    .translate('monthly_installments_amount')),
                buildCurrencyDetailsText(monthlyAmount ?? ""),
              ],
            ),
          );
  }
}
