import 'package:eeh/petty_cash/model/cost_center_model.dart';
import 'package:eeh/shared/utility/methods.dart';
import 'package:eeh/tender_request/model/projectWps_model.dart';

abstract class PettyCashRequestState {}

class PettyCashInitialState extends PettyCashRequestState {}

class PettyCashInitialSuccessState extends PettyCashRequestState {
  EmployeeBankDetails employeeBankDetails;
  PettyCashInitialSuccessState({required this.employeeBankDetails});
}

class PettyCashInitialErrorState extends PettyCashRequestState {}

class ChangeBudgetTypeState extends PettyCashRequestState {}

class ProjectWbsListLoadingState extends PettyCashRequestState {}

class ProjectWbsListSuccessState extends PettyCashRequestState {
  ProjectWpsResponse projectWpsResponse;
  ProjectWbsListSuccessState({required this.projectWpsResponse});
}

class ProjectWbsListErrorState extends PettyCashRequestState {}

class ChangeValidatorValueState extends PettyCashRequestState {}

class UpdatefieldValueState extends PettyCashRequestState {}

class UploadAttachmentLoadingState extends PettyCashRequestState {}

class UploadAttachmentFilesSuccessState extends PettyCashRequestState {}

class SubmitRequestLoadingState extends PettyCashRequestState {}

class SubmitRequestSuccessState extends PettyCashRequestState {}

class FilesUpdatedState extends PettyCashRequestState {}

class SubmitRequestErrorState extends PettyCashRequestState {
  SubmitRequestErrorState() {
    performFailureVibration();
  }
}
