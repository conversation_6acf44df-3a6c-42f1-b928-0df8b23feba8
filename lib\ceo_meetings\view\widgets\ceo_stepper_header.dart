import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:path/path.dart';
import '../../../admin_service/view/widget/dashed_line_widget.dart';
import '../../../l10n/app_localizations.dart';

class CEOStepperHeader extends StatelessWidget {
  const CEOStepperHeader({
    super.key,
    required this.steps,
    required this.currentStep,
  });

  final List<Step> steps;
  final int currentStep;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: dividerColor.withAlpha(30),
          borderRadius: BorderRadius.circular(9),
          border: Border.all(
            color: borderColor,
            width: 1.0,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            getSteppers(context),
          ],
        ),
      ),
    );
  }

  Widget getSteppers(context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width - 60,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: steps.map((step) {
          int stepIndex = steps.indexOf(step);
          return Expanded(
            child: Row(
              children: [
                if (stepIndex > 0)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 15.0),
                      child: DashedLine(
                        dashWidth: 5.0,
                        dashHeight: 2.0,
                        color: stepIndex <= currentStep
                            ? Colors.green
                            : Colors.grey,
                      ),
                    ),
                  ),
                Column(
                  children: [
                    Container(
                      padding: EdgeInsets.only(
                          bottom: 8, left: 20, right: 20, top: 8),
                      margin: EdgeInsets.all(8.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: Colors.white,
                        border: Border.all(
                          color: stepIndex == currentStep
                              ? primaryColor
                              : stepIndex <= currentStep
                                  ? Colors.green
                                  : borderColor,
                          width: stepIndex == currentStep ? 1.0 : 1.0,
                        ),
                        // color:,
                      ),
                      child: CircleAvatar(
                        backgroundColor: stepIndex == currentStep
                            ? primaryColor
                            : stepIndex < currentStep
                                ? Colors.green
                                : secondTextColor.withAlpha(95),
                        radius: 12.r,
                        child: Text(
                          (stepIndex + 1).toString(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                          ),
                        ),
                      ),
                    ),
                    Text(
                      getPageTitle(context)[stepIndex],
                      style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: secondTextColor),
                    ),
                  ],
                ),
                if (stepIndex < steps.length - 1)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 15.0),
                      child: DashedLine(
                        dashWidth: 5.0,
                        dashHeight: 2.0,
                        color: Colors.black.withAlpha(95),
                      ),
                    ),
                  ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  List<String> getPageTitle(BuildContext context) {
    return [AppLocalizations.of(context).translate("Step 1"),AppLocalizations.of(context).translate("Step 2")];
  }
}
