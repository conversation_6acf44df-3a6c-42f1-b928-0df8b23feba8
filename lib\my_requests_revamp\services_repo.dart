import 'package:dio/dio.dart';
import 'package:eeh/my_tasks_revamp/models/task_action_payload.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/utility/secure_storage.dart';

import '../admin_service/utils/field_type_constant.dart';
import '../my_tasks_revamp/models/update_esm_payload.dart';
import '../shared/network/endpoints.dart';
import '../shared/network/api_utilities.dart';

enum ServiceType { request, task }

abstract class ServicesRepoRevamp {
  Future<NetworkResponse> getPendingServicesRevamp(
      String serviceType, String pageNumber, String sortType, String searchKey,
      {CancelToken? cancelToken});
  Future<NetworkResponse> getHistoryServicesRevamp(
      String serviceType, String pageNumber, String sortType, String searchKey,
      {CancelToken? cancelToken});
  Future<NetworkResponse> getAllServicesRevamp(
      String serviceType, String pageNumber, String sortType, String searchKey,
      {CancelToken? cancelToken});
  Future<NetworkResponse> taskAction(TaskActionPayload doneTaskPayload);
  Future<NetworkResponse> getESMServiceList(String tableName);
  Future<NetworkResponse> getESMGroups(String esmTypeKey, String tableName);
  Future<NetworkResponse> updateEsm(
      UpdateEsmPayload updateEsm, String tableName);
  Future<NetworkResponse> getESMServiceData(int requestID, String tableName);
  Future<NetworkResponse> checkInCheckOut(
      String actionType, String mutableContext, String tableName);
  Future<NetworkResponse> approvalTemplates(int requestID);
  Future<NetworkResponse> submitTemplates(int recId, String templatekey);

  Future<NetworkResponse> withdrawRequest({
    required String mutableContext,
    required String reason,
  });
  Future<NetworkResponse> sendReminder({
    required String workId,
    required String empId,
    required String message,
  });

  Future<NetworkResponse> sendNewMessage(
      Map<String, dynamic> requestBody, Map<String, String> headers);
}

class ServicesRepoRevampImpl implements ServicesRepoRevamp {
  ServicesRepoRevampImpl._internal();

  static final ServicesRepoRevampImpl _instance =
      ServicesRepoRevampImpl._internal();

  static ServicesRepoRevampImpl get instance => _instance;

  @override
  Future<NetworkResponse> getPendingServicesRevamp(
      serviceType, pageNumber, sortType, searchKey,
      {CancelToken? cancelToken}) async {
    return _getRequestByTabName(
        "pending", serviceType, pageNumber, sortType, searchKey);
  }

  @override
  Future<NetworkResponse> getHistoryServicesRevamp(
      serviceType, pageNumber, sortType, searchKey,
      {CancelToken? cancelToken}) async {
    return _getRequestByTabName(
        "history", serviceType, pageNumber, sortType, searchKey);
  }

  @override
  Future<NetworkResponse> getAllServicesRevamp(
      serviceType, pageNumber, sortType, searchKey,
      {CancelToken? cancelToken}) async {
    return _getRequestByTabName(
        "all", serviceType, pageNumber, sortType, searchKey,
        cancelToken: cancelToken);
  }

  Future<NetworkResponse> _getRequestByTabName(String tabName,
      String serviceType, String pageNumber, String sortType, String searchKey,
      {CancelToken? cancelToken}) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {
          "details_type": serviceType,
          "tab_name": tabName,
          "page_number": pageNumber,
          "search_keyword": searchKey,
          "sort_criteria": sortType
        },
        requestType: RequestType.post,
        sessionToken: token,
        cancelToken: cancelToken,
        headers: {
          'SessionToken': token,
          'formName': 'My Pending Requests Form',
          'moduleName': 'My Pending Requests',
          'appKey': 'ACM',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> withdrawRequest(
      {required String mutableContext, required String reason}) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"mutable_context": mutableContext, "reason": reason},
        requestType: RequestType.post,
        sessionToken: token,
        headers: {
          'SessionToken': token,
          'formName': 'Withdraw Form',
          'moduleName': 'Withdraw',
          'appKey': 'ACM',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> taskAction(
      TaskActionPayload taskActionPayload) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: taskActionPayload.toJson(),
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'SessionToken': sessionToken,
          'formName': 'Tasks Actions Form',
          'moduleName': 'Tasks Actions',
          'appKey': 'ACM',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> sendReminder({
    required String workId,
    required String empId,
    required String message,
  }) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {
          "to_employeeid": empId,
          "mutable_context": workId,
          "massage": message
        },
        requestType: RequestType.post,
        sessionToken: token,
        headers: {
          'SessionToken': token,
          'formName': 'Remind me Form',
          'moduleName': 'Remind me',
          'appKey': 'ACM',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> getESMServiceList(String tableName) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"tb_service_name": tableName},
        requestType: RequestType.post,
        sessionToken: token,
        headers: {
          "formName": "ACC Services list Form",
          "moduleName": "ACC Services list",
          "appKey": "ACM",
          "Content-Type": "application/json",
        });
    return response;
  }

  @override
  Future<NetworkResponse> getESMServiceData(
      int requestID, String tableName) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"request_id": requestID},
        requestType: RequestType.post,
        sessionToken: token,
        headers: _getServiceDataHeaders(tableName));
    return response;
  }

  @override
  Future<NetworkResponse> getESMGroups(
      String esmTypeKey, String tableName) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: getGroupsBodykey(tableName, esmTypeKey),
        requestType: RequestType.post,
        sessionToken: token,
        headers: _getESMGroupsHeaders(tableName));
    return response;
  }

  @override
  Future<NetworkResponse> updateEsm(
      UpdateEsmPayload updateEsm, String tableName) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: updateEsm.toJson(),
        requestType: RequestType.post,
        sessionToken: token,
        headers: _getUpdateEsmHeaders(tableName));
    return response;
  }

  @override
  Future<NetworkResponse> checkInCheckOut(
      String actionType, String mutableContext, String tableName) async {
    String token =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"action_type": actionType, "mutable_context": mutableContext},
        requestType: RequestType.post,
        sessionToken: token,
        headers: _getCheckInCheckOutHeaders(tableName));
    return response;
  }

  @override
  Future<NetworkResponse> sendNewMessage(
      Map<String, dynamic> requestBody, Map<String, String> headers) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: requestBody,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: headers);
    return response;
  }

  getGroupsBodykey(String tableName, key) {
    switch (tableName) {
      case TableName.esmTableName:
        return {"esm_type_key": key};
      case TableName.lcrTableName:
        return {"lcr_type_key": key};
      case TableName.cSAtableName:
        return {"hss_type_key": key};
      case TableName.supTableName:
        return {"sup_type_key": key};
      case TableName.ketTableName:
        return {"ket_type_key": key};
      case TableName.cysTableName:
        return {"cys_type_key": key};  
      case TableName.earTableName:
        return {"esm_type_key": key};  
    }
  }

  Map<String, String>? _getESMGroupsHeaders(String tableName) {
    switch (tableName) {
      case TableName.esmTableName:
        return {
          'formName': 'ESM Groups and Members Form',
          'moduleName': 'ESM Groups and Members',
          'appKey': 'ESM',
          'Content-Type': 'application/json'
        };
      case TableName.lcrTableName:
        return {
          'formName': 'LCR Groups and Members Form',
          'moduleName': 'LCR Groups and Members',
          "appKey": "LCR",
          "Content-Type": "application/json",
        };
      case TableName.cSAtableName:
        return {
          'formName': 'HSS Groups and Members Form',
          'moduleName': 'HSS Groups and Members',
          "appKey": "HSS",
          "Content-Type": "application/json",
        };
      case TableName.supTableName:
        return {
          'formName': 'SUP Groups and Members Form',
          'moduleName': 'SUP Groups and Members',
          "appKey": "SUP",
          "Content-Type": "application/json",
        };
      case TableName.ketTableName:
        return {
          'formName': 'KET Groups and Members Form',
          'moduleName': 'KET Groups and Members',
          "appKey": "KET",
          "Content-Type": "application/json",
        };
      case TableName.cysTableName:
        return {
          'formName': 'CYS Groups and Members Form',
          'moduleName': 'CYS Groups and Members',
          "appKey": "CYS",
          "Content-Type": "application/json",
        };  
      case TableName.earTableName:
        return {
          'formName': 'EAR Groups and Members Form',
          'moduleName': 'EAR  Groups and Members',
          "appKey": "EAR",
          "Content-Type": "application/json",
        };  
    }
    return {};
  }

  Map<String, String>? _getServiceDataHeaders(String tableName) {
    switch (tableName) {
      case TableName.esmTableName:
        return {
          'formName': 'Get ESM service data Form',
          'moduleName': 'Get ESM service data',
          'appKey': 'ESM',
          'Content-Type': 'application/json'
        };
      case TableName.lcrTableName:
        return {
          'formName': 'Get LCR service data Form',
          'moduleName': 'Get LCR service data',
          "appKey": "LCR",
          "Content-Type": "application/json",
        };
      case TableName.cSAtableName:
        return {
          'formName': 'Get HSS service data Form',
          'moduleName': 'Get HSS service data',
          "appKey": "HSS",
          "Content-Type": "application/json",
        };
      case TableName.supTableName:
        return {
          'formName': 'Get SUP service data Form',
          'moduleName': 'Get SUP service data',
          "appKey": "SUP",
          "Content-Type": "application/json",
        };
      case TableName.ketTableName:
        return {
          'formName': 'Get KET service data Form',
          'moduleName': 'Get KET service data',
          "appKey": "KET",
          "Content-Type": "application/json",
        };
      case TableName.cysTableName:
        return {
          'formName': 'Get CYS service data Form',
          'moduleName': 'Get CYS service data',
          "appKey": "CYS",
          "Content-Type": "application/json",
        };  
      case TableName.earTableName:
        return {
          'formName': 'Get EAR service data Form',
          'moduleName': 'Get EAR service data',
          "appKey": "EAR",
          "Content-Type": "application/json",
        };  
    }
    return {};
  }

  Map<String, String>? _getUpdateEsmHeaders(String tableName) {
    switch (tableName) {
      case TableName.esmTableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          'appKey': 'ESM',
          'Content-Type': 'application/json'
        };
      case TableName.lcrTableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          "appKey": "LCR",
          "Content-Type": "application/json",
        };
      case TableName.cSAtableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          "appKey": "HSS",
          "Content-Type": "application/json",
        };
      case TableName.supTableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          "appKey": "SUP",
          "Content-Type": "application/json",
        };
      case TableName.ketTableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          "appKey": "KET",
          "Content-Type": "application/json",
        };
      case TableName.cysTableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          "appKey": "CYS",
          "Content-Type": "application/json",
        };  
      case TableName.earTableName:
        return {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          "appKey": "EAR",
          "Content-Type": "application/json",
        };  
    }
    return {};
  }

  Map<String, String>? _getCheckInCheckOutHeaders(String tableName) {
    switch (tableName) {
      case TableName.esmTableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          'appKey': 'ESM',
          'Content-Type': 'application/json'
        };
      case TableName.lcrTableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          "appKey": "LCR",
          "Content-Type": "application/json",
        };
      case TableName.cSAtableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          "appKey": "HSS",
          "Content-Type": "application/json",
        };
      case TableName.ketTableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          "appKey": "KET",
          "Content-Type": "application/json",
        };
      case TableName.supTableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          "appKey": "SUP",
          "Content-Type": "application/json",
        };
      case TableName.cysTableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          "appKey": "CYS",
          "Content-Type": "application/json",
        };
      case TableName.earTableName:
        return {
          'formName': 'Check In and Check Out Form',
          'moduleName': 'Check In and Check Out',
          "appKey": "EAR",
          "Content-Type": "application/json",
        };
    }
    return {};
  }

  @override
  Future<NetworkResponse> approvalTemplates(int requestID) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"request_id": requestID},
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'SessionToken': sessionToken,
          'formName': 'Get Templates Form',
          'moduleName': 'Get Templates',
          'appKey': 'SUP',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> submitTemplates(int recId, String templatekey) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: {"recId": recId, "template_key": templatekey},
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'SessionToken': sessionToken,
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          'appKey': 'SUP',
          'Content-Type': 'application/json'
        });
    return response;
  }
}
