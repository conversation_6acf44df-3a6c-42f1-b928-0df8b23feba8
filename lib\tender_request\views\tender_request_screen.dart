
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:eeh/tender_request/bloc/tender_request_bloc.dart';
import 'package:eeh/tender_request/bloc/tender_request_state.dart';
import 'package:eeh/tender_request/model/projectWps_model.dart';
import 'package:eeh/tender_request/views/widget/successScreenContentTender.dart';
import 'package:eeh/tender_request/views/widget/tender_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../l10n/app_localizations.dart';
import '../../letter_of_guarantee/models/Lists.dart';
import '../../shared/app_constants.dart';
import '../../shared/attachment_uda/bloc/attach_bloc.dart';
import '../../shared/attachment_uda/bloc/attach_event.dart';
import '../../shared/attachment_uda/widget/attachment_component.dart';
import '../../shared/favorite_component/bloc/favorite_bloc.dart';
import '../../shared/favorite_component/bloc/favorite_event.dart';
import '../../shared/favorite_component/view/favorite_component.dart';
import '../../shared/network/endpoints.dart';
import '../../shared/styles/colors.dart';
import '../../shared/utility/navigation_utility.dart';
import '../../shared/widgets/cancel_button_component.dart';
import '../../shared/widgets/customer_info_component/model/customer_Info_model.dart';
import '../../shared/widgets/customer_info_component/view/customer_info_component.dart';
import '../../shared/widgets/important_note.dart';
import '../../shared/widgets/new_custom_note_widget.dart';
import '../../shared/widgets/new_textformfield_component.dart';
import '../../shared/widgets/submit_button_component.dart';
import '../../shared/widgets/toast.dart';
import '../bloc/tender_request_event.dart';
import '../model/bank_model.dart';
import '../model/tender_payment_model.dart';

class TenderRequestView extends StatefulWidget {
  const TenderRequestView({super.key});

  @override
  State<TenderRequestView> createState() => _TenderRequestViewState();
}

class _TenderRequestViewState extends State<TenderRequestView> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  TenderUtils tenderUtils = TenderUtils();
  TenderPaymentResponse? tenderPaymentResponse;
  List<TenderPayment> tenderPaymentList = [];
  ProjectWpsResponse? projectWpsResponse;
  List<ProjectWps> projectWpsList =[];
  BankListResponse? bankListResponse;
  List<BankModel> bankList =[];
 // CustomerInfoResponse? customerInfoResponse;
  List<CustomerInfo> customerList=[];
 // PaymentTypeModel? selectedPaymentTypeModel;
  TenderPayment? selectedTenderPaymentModel;
  TenderRequestBloc? bloc;
  bool isloadingProject = true;
  bool isloadingBank = true;
  bool isloadingPayment = true;
/*  final TextEditingController budgetTypeController = TextEditingController();
  final TextEditingController costCenterController = TextEditingController();
  final TextEditingController projectController = TextEditingController();
  final TextEditingController tenderNameController = TextEditingController();
  final TextEditingController tenderNumberController = TextEditingController();
  final TextEditingController  tenderValueController = TextEditingController();
  final TextEditingController  customerNameController = TextEditingController();
  final TextEditingController  paymentTypeController = TextEditingController();
  final TextEditingController  sADADNumberController = TextEditingController();
  final TextEditingController  beneficiaryNameController = TextEditingController();
  final TextEditingController  bankController = TextEditingController();
  final TextEditingController  iBANController = TextEditingController();
  final TextEditingController  commentController = TextEditingController();*/
  Color submitBtnColor = primaryColor.withOpacity(0.2);
  GlobalKey<FormFieldState> budgetTypeKey =  GlobalKey<FormFieldState>(debugLabel:"budgetTypeKey");
  GlobalKey<FormFieldState> costCenterKey =  GlobalKey<FormFieldState>(debugLabel:"costCenterKey");
  GlobalKey<FormFieldState> projectKey =  GlobalKey<FormFieldState>(debugLabel:"projectKey");
  GlobalKey<FormFieldState> tenderNameKey =  GlobalKey<FormFieldState>(debugLabel:"tenderNameKey");
  GlobalKey<FormFieldState> tenderNumberKey =  GlobalKey<FormFieldState>(debugLabel:"tenderNumberKey");
  GlobalKey<FormFieldState> tenderValueKey =  GlobalKey<FormFieldState>(debugLabel:"tenderValueKey");
  GlobalKey<FormFieldState> customerNameKey =  GlobalKey<FormFieldState>(debugLabel:"customerNameKey");
  GlobalKey<FormFieldState> paymentTypeKey =  GlobalKey<FormFieldState>(debugLabel:"paymentTypeKey");
  GlobalKey<FormFieldState> sADADNumberKey =  GlobalKey<FormFieldState>(debugLabel:"sADADNumberKey");
  GlobalKey<FormFieldState> beneficiaryNameKey =  GlobalKey<FormFieldState>(debugLabel:"beneficiaryNameKey");
  GlobalKey<FormFieldState> bankKey =  GlobalKey<FormFieldState>(debugLabel:"bankKey");
  GlobalKey<FormFieldState> iBANKey =  GlobalKey<FormFieldState>(debugLabel:"iBANKey");
  GlobalKey<FormFieldState> commentKey =  GlobalKey<FormFieldState>(debugLabel:"commentKey");
  List<String> paymentRequiredFields = [];

  List<String> getRequiredFieldsByTenderPaymentId(String id) {
    switch (id) {
      case "Bank Transfer":
        return ["beneficiaryName", "bank", "iban"];
      case "Bank Cheque":
        return ["beneficiaryName"];
      case "SADAD Number":
        return ["sadadNumber"];
      default:
        return [];
    }
  }

  @override
  Widget build(BuildContext context) {

    return MultiBlocProvider(
      providers: [
        BlocProvider<TenderRequestBloc>(
          create: (context) => TenderRequestBloc()..add(TenderRequestInitialEvent()),
        ),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
        BlocProvider<AttachBloc>(
          create: (BuildContext context) {
            return AttachBloc()..add(AttachInitialEvent());
          },
        )


    ],
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Scaffold(

          appBar: getAppBarWidget(context),
          body: BlocConsumer<TenderRequestBloc,TenderRequestState>(
              listener:(context,state){
                if(state is TenderRequestInitialState){
                  bloc?.budgetTypeController.text=AppLocalizations.appLang=="en"?(bloc?.selectedBudgetTypeModel?.nameEn??''):(bloc?.selectedBudgetTypeModel?.nameAr??'');
                  bloc?.add(PaymentTypeListEvent());
                   bloc?.add(BankListEvent());

                }
                if(state is PaymentTypeListSuccessState){
                  tenderPaymentList = bloc?.tenderPaymentResponse?.tenderPayment ??[];
                  bloc?.costCenterController.text = bloc?.tenderPaymentResponse?.costCenter.first.costCenterCode ?? '';
                  isloadingPayment = false;
                  bloc?.add(ChangePaymentTypeEvent(tenderPaymentList.firstWhere((item){
                    return item.id == "SADAD Number";
                  })));

                }
                if(state is ProjectWpsListSuccessState){
                  projectWpsList = bloc?.projectWpsResponse?.projectWps ?? [];
                  isloadingProject = false;

                }
                if(state is BankListSuccessState){
                  bankList =bloc?.bankListResponse?.bankList ?? [];
                  isloadingBank = false;

                }if(state is CustomerListSuccessState){
                  customerList =bloc?.customerInfoResponse?.customerInforma ?? [];
                }
              },
              // listenWhen: (previous, current) => current is TenderRequestInitialState ,
              buildWhen: (previous, current) =>
              current is TenderRequestInitialState ||
              current is PaymentTypeListSuccessState ||
              current is ProjectWpsListSuccessState ||
              current is BankListSuccessState ||
              current is CustomerListSuccessState||
              current is ChangeBudgetTypeState||
              current is ProjectWpsListLoadingState||
              current is ValidateFieldsState||
              current is PaymentTypeListLoadingState ||
              current is BankListLoadingState
            ,
              builder: (context,state){
                bloc = context.read<TenderRequestBloc>();
                bloc?.ctx = context;


                if(state is TenderRequestInitialState || state is PaymentTypeListLoadingState || state is BankListLoadingState ){
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }
                else{
                  return Form(
                      onChanged: (){
                        if(bloc!.isButtonEnable() && (AttachBloc.instance(context).files.isNotEmpty) ){
                          submitBtnColor = primaryColor;
                        }else{
                          submitBtnColor = primaryColor.withOpacity(0.2);
                        }
                        bloc?.add(ValidateFieldsEvent());
                      },
                      key:  formKey,
                      child:Flex(
                        direction: Axis.vertical,
                        children: [
                          Expanded(
                            child: SingleChildScrollView(
                              //    physics: const BouncingScrollPhysics(),
                              child: Padding(
                                padding:    EdgeInsets.symmetric(horizontal: 16.w),
                                child: Column(
                                  children: [
                                    SizedBox(height: 10.0.h),

                                    tenderUtils.getBudgetType(context, bloc?.budgetTypeController, (selected) {
                                        bloc?.add(ChangeBudgetTypeEvent(selected));

                                    },budgetTypeKey),
                                    if (bloc?.selectedBudgetTypeModel?.id == department)
                                      tenderUtils.getCostCenter(context, bloc?.costCenterController,costCenterKey),
                                    if (bloc?.selectedBudgetTypeModel?.id == project)
                                      BlocBuilder<TenderRequestBloc,TenderRequestState>(

                                          buildWhen: (previous, current) =>
                                          current is ProjectWpsListSuccessState ||
                                              current is ProjectWpsListLoadingState,
                                          builder: (context,state){
                                          return tenderUtils.getProject(context, bloc?.projectController,projectKey,projectWpsList,state is ProjectWpsListLoadingState&&projectWpsList.isEmpty,(value) =>bloc?.selectedProjectWps=value,);
                                        }
                                      ),

                                    tenderUtils.getTenderName(context, bloc?.tenderNameController,tenderNameKey),
                                    tenderUtils.getTenderNumber(context, bloc?.tenderNumberController,tenderNumberKey),
                                    tenderUtils.getTenderValue(context, bloc?.tenderValueController,tenderValueKey),

                                   CustomerInfoComponent(controller: bloc!.customerNameController,appKey: 'NOT', onSelect:
                                   (value) => bloc?.customerInfo = value,),

                                    tenderUtils.getPaymentType(context, bloc?.paymentTypeController,paymentTypeKey ,tenderPaymentList ,state is PaymentTypeListLoadingState,
                                   (selected) {
                                        bloc?.add(ChangePaymentTypeEvent(selected));

                                    }),

                                    if (bloc?.paymentRequiredFields.contains("beneficiaryName") == true)
                                      tenderUtils.getBeneficiaryName(context, bloc?.beneficiaryNameController,beneficiaryNameKey),
                                    if (bloc?.paymentRequiredFields.contains("bank") == true)
                                      tenderUtils.getBank(context, bloc?.bankController,bankKey,bankList,isloadingBank,(value) {

                                          bloc?.listbank = value;

                                      }),
                                    if (bloc?.paymentRequiredFields.contains("iban") == true)
                                      tenderUtils.getIBAN(context, bloc?.iBANController,iBANKey),
                                    if (bloc?.paymentRequiredFields.contains("sadadNumber") == true)
                                      tenderUtils.getSADADNumber(context, bloc?.sADADNumberController,sADADNumberKey),

                                    Padding(
                                      padding:  EdgeInsets.symmetric(vertical:10.h ),
                                      child: CustomNoteWidget(
                                        onChange:(value) {} ,
                                        noteController:bloc?.commentController ,
                                        key: commentKey,
                                        isMandatory: true,
                                        hintText: AppLocalizations.of(context).translate("Write a Comments"),
                                        validationText: AppLocalizations.of(context).translate("Please enter Comments"),
                                        labelText: AppLocalizations.of(context).translate("Comments"),
                                      ),
                                    ),

                                    Padding(
                                      padding:  EdgeInsets.symmetric(vertical:5.h ),
                                      child: AttachUI(
                                        isRequired: true,
                                        serviceType: 'Tender',
                                        color: primaryColor,
                                        endPoint: deleteTenderAttach,
                                        subTitle: AppLocalizations.of(context).translate('Click to upload'),
                                        title: AppLocalizations.of(context).translate('Attachment'),
                                        label: AppLocalizations.of(context).translate("File:"),
                                        onFilesUpdated: (){
                                          bloc?.add(FilesUpdatedEvent());
                                        },

                                      ),
                                    ),

                                    Padding(
                                      padding:  EdgeInsets.symmetric(vertical:5.h ),
                                      child: ImportantNote(
                                        notes: [
                                          AppLocalizations.of(context).translate("5 Days (Starting after complete all approval steps)") ,

                                        ],
                                      ),
                                    ),

                                  ],),
                              ),
                            ),
                          ),
                          getButtons(context)
                        ],
                      )

                  );
                }
              },
              ),
        ),
      ),
    );
  }
  Widget getButtons(BuildContext context){
    return BlocConsumer<TenderRequestBloc,TenderRequestState>(
        listener: (context, state) {
          state is UploadAttachmentFilesSuccessState
              ? Navigation.navigateToScreen(
            context,
            SuccessScreen(
              title: AppLocalizations.of(context).translate(
                  "Request Submitted Successfully"),
              contentWidget: SuccessScreenContentWidgetTender(
                  submitResponse: bloc?.submitTenderRequestModel),
            ),
          ) : bloc?.add(UploadAttachmentFilesEvent());
        },

      listenWhen: (previous, current) =>
      current is UploadAttachmentFilesSuccessState || current is SubmitSuccessState,
      buildWhen: (previous, current) =>
      current is PaymentTypeListSuccessState ||
          current is ProjectWpsListSuccessState ||
          current is BankListSuccessState ||
          current is CustomerListSuccessState||
          current is FilesUpdatedState ||
          current is UploadAttachmentFilesSuccessState ||
          current is SubmitLoadingState ||
          current is SubmitSuccessState ||
          current is SubmitErrorState
      ,
      builder: (context, state){
        final bool hasFiles = AttachBloc.instance(context).files.isNotEmpty;
        final tenderValueText = bloc!.tenderValueController.text;
        final tenderValue = double.tryParse(tenderValueText) ?? 0;
     //   final Color submitBtnColor = bloc!.isButtonEnable() ? primaryColor : secondTextColor.withOpacity(0.1);
       return  Column(
         crossAxisAlignment: CrossAxisAlignment.stretch,
         mainAxisSize: MainAxisSize.min,
         children: [
           CancelButtonComponent(
               title: AppLocalizations.of(context).translate('Cancel')),
           SubmitButtonComponent(
             text: AppLocalizations.of(context).translate('Submit'),
             backGroung: (bloc!.isButtonEnable() && hasFiles) ? primaryColor :  primaryColor.withOpacity(0.2),
             isLoading: state is SubmitLoadingState || state is  UploadAttachmentLoadingState  ? true : false,
             onPressed: () {
               if(tenderValue <= 0){
                 showMessage(
                   AppLocalizations.of(context).translate("Value Should be more than or equal 1"),
                   MessageType.warning,
                 );
                 return;
               }
               if (formKey.currentState!.validate() && bloc!.isButtonEnable() && hasFiles ) {
                 if(state is! UploadAttachmentLoadingState && state is! SubmitLoadingState){
                   bloc?.add(SubmitEvent());
                 }
               }else if (!hasFiles) {
                 showMessage(
                   AppLocalizations.of(context).translate("Must add at least one attachment"),
                   MessageType.warning,
                 );
               }
             },
           )
         ],
       );
      },

    );
  }


  getAppBarWidget(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: FavoriteComponent(
        title: AppLocalizations.of(context).translate("Tender Request"),
        id: tenderRequestID,
      ),
    );
  }

}

/*

* */
