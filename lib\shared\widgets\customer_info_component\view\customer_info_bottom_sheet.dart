import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/general_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomerInfoBottomSheet extends StatefulWidget {
  final String title;
  final List contentList;
  final ValueChanged<dynamic> onSelection;
  final Function()? onSearchPressed;
  final Function()? onDownArrowPressed;
  final TextEditingController? searchController;
  final bool isIconButtonLoading;
  final bool isApiLoading;

  const CustomerInfoBottomSheet({
    super.key,
    required this.title,
    required this.contentList,
    required this.onSelection,
    required this.isIconButtonLoading,
    required this.isApiLoading,
    this.onSearchPressed,
    this.onDownArrowPressed,
    this.searchController,
  });

  @override
  State<CustomerInfoBottomSheet> createState() =>
      _CustomerInfoBottomSheetState();
}

class _CustomerInfoBottomSheetState extends State<CustomerInfoBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.85,
      maxChildSize: 1,
      minChildSize: 0.2,
      expand: false,
      builder: (context, scrollController) => Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 3,
                child: Padding(
                  padding: EdgeInsets.fromLTRB(0, 0, 12.0, 5.0),
                  child: Text(
                    textAlign: TextAlign.center,
                    widget.title,
                    style: FontUtilities.getTextStyle(TextType.medium,
                        size: 18.sp, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 10),
            child: TextFormField(
              controller: widget.searchController,
              style: FontUtilities.getTextStyle(TextType.regular,
                  size: 14.sp, fontWeight: FontWeight.w500),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.all(5),
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(9),
                    ),
                    borderSide: BorderSide(color: borderColor)),
                focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(9),
                    ),
                    borderSide: BorderSide(color: borderColor)),
                enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(9),
                    ),
                    borderSide: BorderSide(
                      color: borderColor,
                    )),
                prefixIcon: const Icon(
                  Icons.search,
                  color: primaryColor,
                ),
                suffixIcon: IconButton(
                    onPressed: widget.onSearchPressed ?? () {},
                    icon: Container(
                        height: 30.sp,
                        width: 30.sp,
                        decoration: BoxDecoration(
                          color: primaryColor,
                          borderRadius: BorderRadius.all(
                            Radius.circular(9),
                          ),
                        ),
                        child: Icon(
                          Icons.search,
                          color: Colors.white,
                        ))),
                hintText:
                    '${AppLocalizations.of(context).translate("search")} ',
                hintStyle: FontUtilities.getTextStyle(TextType.disable,
                    size: 14.sp, fontWeight: FontWeight.w400),
              ),
            ),
          ),
          widget.isApiLoading
              ? Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              : _getBody()
        ],
      ),
    );
  }

  Widget _getBody() {
    return Expanded(
      child: Column(
        children: [
          SizedBox(
            height: 1.h,
          ),
          Expanded(
            child: ListView.builder(
                itemBuilder: (context, index) {
                  final item = widget.contentList[index];
                  return InkWell(
                    onTap: () {
                      widget.onSelection(item);
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.sp),
                      child: Row(
                        children: [
                          if (item is GeneralSheetContent)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 4),
                              child: Icon(
                                item.icon,
                                color: item.iconColor ?? primaryColor,
                              ),
                            ),
                          if (item is GeneralSheetContent)
                            if ((item.extraContent ?? []).isNotEmpty)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: item.extraContent!.map((element) {
                                  return Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 2.sp),
                                    child: Row(
                                      children: [
                                        Text(
                                          "${AppLocalizations.appLang == "en" ? element.keyEn : element.keyAR} : ",
                                          style:
                                              FontUtility.getTextStyleForText(
                                                  TextType.medium,
                                                  isBold: true),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          "${AppLocalizations.appLang == "en" ? element.valueEn : element.valueAR}",
                                          overflow: TextOverflow.ellipsis,
                                        )
                                      ],
                                    ),
                                  );
                                }).toList(),
                              )
                            else
                              Expanded(
                                child: Text(
                                  (AppLocalizations.appLang == 'en'
                                          ? item.nameen
                                          : (item.namear ?? item.nameen)) ??
                                      'UnDefiend',
                                  style: FontUtilities.getTextStyle(
                                      TextType.medium,
                                      size: 14.sp,
                                      fontWeight: FontWeight.w600),
                                  maxLines: 2,
                                ),
                              ),
                        ],
                      ),
                    ),
                  );
                },
                itemCount: widget.contentList.length),
          ),
          widget.isIconButtonLoading
              ? Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: CircularProgressIndicator())
              : Visibility(
                  visible: widget.contentList.isNotEmpty,
                  child: IconButton(
                      onPressed: widget.onDownArrowPressed ?? () {},
                      icon: Container(
                          height: 30.sp,
                          width: 30.sp,
                          decoration: BoxDecoration(
                            color: primaryColor,
                            borderRadius: BorderRadius.all(
                              Radius.circular(15),
                            ),
                          ),
                          child: Icon(
                            Icons.arrow_downward,
                            color: Colors.white,
                          ))),
                ),
        ],
      ),
    );
  }
}
