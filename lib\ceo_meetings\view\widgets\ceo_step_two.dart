import 'package:eeh/ceo_meetings/bloc/ceo_meetings_states.dart';
import 'package:eeh/ceo_meetings/view/widgets/bottom_sheet_content.dart';
import 'package:eeh/shared/widgets/date_bottom_sheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../l10n/app_localizations.dart';
import '../../../shared/attachment_uda/widget/attachment_component.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/utility/font_utility.dart';
import '../../../shared/utility/methods.dart';
import '../../bloc/ceo_meetings_bloc.dart';
import '../../bloc/ceo_meetings_events.dart';
import 'ceo_meetings_text_field.dart';

class CEOStepTwoView extends StatefulWidget {
  const CEOStepTwoView({super.key});

  @override
  State<CEOStepTwoView> createState() => _CEOStepTwoViewState();
}

class _CEOStepTwoViewState extends State<CEOStepTwoView> {
  late CEOMeetingsBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      _bloc = context.read<CEOMeetingsBloc>();
      return SizedBox(
          width: MediaQuery.of(context).size.width,
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.0.w),
                    child: Column(children: [
                      SizedBox(
                        height: 10.h,
                      ),
                      getMeetingSubjectTextFieldWidget(),
                      SizedBox(
                        height: 15.h,
                      ),
                      getMeetingRequesterTextFieldWidget(),
                      SizedBox(
                        height: 15.h,
                      ),
                      getMeetingAttendeesTextFieldWidget(),
                      SizedBox(
                        height: 15.h,
                      ),
                      getNoteTakerTextFieldWidget(),
                      SizedBox(
                        height: 15.h,
                      ),
                      getSelectDateTextFieldWidget(),
                      SizedBox(
                        height: 15.h,
                      ),
                      getFromDateTextFieldWidget(),
                      SizedBox(
                        height: 15.h,
                      ),
                      getToDateTextFieldWidget(),
                      SizedBox(
                        height: 30.h,
                      ),
                      getAgendaTextFieldWidget(),
                      SizedBox(
                        height: 30.h,
                      ),
                      getExpectTextFieldWidget(),
                      SizedBox(
                        height: 30.h,
                      ),
                      getNoteTextFieldWidget(),
                      SizedBox(
                        height: 30.h,
                      ),
                      getAttachmentWidget(),
                      SizedBox(
                        height: 20.h,
                      ),
                    ])),
              ],
            ),
          ));
    });
  }

  Widget getMeetingSubjectTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.subjectController,
      isReadOnly: false,
      isRequired: true,
      title: AppLocalizations.of(context).translate("Meeting Subject"),
      haveBottomSheet: false,
      onChange: (e) => _bloc.add(ChangeFieldValueEvent()),
    );
  }

  Widget getMeetingRequesterTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.requesterController,
      isReadOnly: true,
      isRequired: true,
      title: AppLocalizations.of(context).translate('Meeting Requester'),
      haveBottomSheet: true,
      showBottomSheetOnTap: () async => showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          showDragHandle: true,
          backgroundColor: Colors.white,
          builder: (context) {
            return BlocProvider.value(
              value: _bloc,
              child: BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
                buildWhen: (previous, current) =>
                    current is GetEmployeesListSuccessState,
                builder: (context, state) => CEOBottomSheetUIWidget(
                  employees: _bloc.requestersEmployees,
                  employeesType: EmployeesType.requester,
                  textController: _bloc.requesterController,
                  title: AppLocalizations.of(context)
                      .translate('Meeting Requester'),
                  onMembersSelected: (selectedMembers) {
                    if (selectedMembers.isNotEmpty) {
                      String selectedText = selectedMembers
                          .map((member) => AppLocalizations.appLang == 'en'
                              ? member.nameEn ?? ''
                              : member.nameAr ?? '')
                          .join(', ');

                      _bloc.requesterController.text = selectedText;
                      _bloc.add(ChangeFieldValueEvent());
                    }
                  },
                ),
              ),
            );
          }),
    );
  }

  Widget getMeetingAttendeesTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.attendeesController,
      isReadOnly: true,
      isRequired: true,
      title: AppLocalizations.of(context).translate('Meeting Attendees'),
      haveBottomSheet: true,
      showBottomSheetOnTap: () async => showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          showDragHandle: true,
          backgroundColor: Colors.white,
          builder: (context) {
            return BlocProvider.value(
              value: _bloc,
              child: BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
                  buildWhen: (previous, current) =>
                      current is GetEmployeesListSuccessState,
                  builder: (context, state) => CEOBottomSheetUIWidget(
                        employees: _bloc.attendeesEmployees,
                        employeesType: EmployeesType.attendees,
                        textController: _bloc.attendeesController,
                        contentHasCheckBox: true,
                        title: AppLocalizations.of(context)
                            .translate('Meeting Attendees'),
                        onMembersSelected: (selectedMembers) {
                          if (selectedMembers.isNotEmpty) {
                            String selectedText = selectedMembers
                                .map((member) =>
                                    AppLocalizations.appLang == 'en'
                                        ? member.nameEn ?? ''
                                        : member.nameAr ?? '')
                                .join(', ');

                            _bloc.attendeesController.text = selectedText;
                            _bloc.add(ChangeFieldValueEvent());
                          }
                        },
                      )),
            );
          }),
      suffixWidget: const Icon(
        Icons.keyboard_arrow_down_sharp,
      ),
    );
  }

  Widget getNoteTakerTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.noteTakerController,
      isReadOnly: true,
      isRequired: true,
      title: AppLocalizations.of(context).translate('Note Taker'),
      haveBottomSheet: true,
      showBottomSheetOnTap: () async => showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          showDragHandle: true,
          backgroundColor: Colors.white,
          builder: (context) {
            return BlocProvider.value(
              value: _bloc,
              child: BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
                buildWhen: (previous, current) =>
                    current is GetEmployeesListSuccessState,
                builder: (context, state) => CEOBottomSheetUIWidget(
                  employees: _bloc.noteTakersEmployees,
                  employeesType: EmployeesType.noteTaker,
                  textController: _bloc.noteTakerController,
                  title: AppLocalizations.of(context).translate('Note Taker'),
                  onMembersSelected: (selectedMembers) {
                    if (selectedMembers.isNotEmpty) {
                      String selectedText = selectedMembers
                          .map((member) => AppLocalizations.appLang == 'en'
                              ? member.nameEn ?? ''
                              : member.nameAr ?? '')
                          .join(', ');

                      _bloc.noteTakerController.text = selectedText;
                      _bloc.add(ChangeFieldValueEvent());
                    }
                  },
                ),
              ),
            );
          }),
      suffixWidget: const Icon(
        Icons.keyboard_arrow_down_sharp,
      ),
    );
  }

  Widget getSelectDateTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.meetingDateController,
      isReadOnly: true,
      isRequired: true,
      title: AppLocalizations.of(context).translate('Meeting Date'),
      haveBottomSheet: true,
      showBottomSheetOnTap: () async => showModalBottomSheet(
          isScrollControlled: true,
          context: context,
          backgroundColor: Colors.white,
          builder: (BuildContext context) {
            return DateBottomSheet(
              datePickerController: _bloc.dateRangePickerController,
              minDate: DateTime.now(),
            );
          },
          showDragHandle: true)
          .then((value) {
        _bloc.onSelectDate();
        _bloc.checkDateValidations();
        _bloc.add(ChangeFieldValueEvent());
      }),
      suffixWidget: const Icon(
        Icons.calendar_today_rounded,
        size: 18,
        color: secondTextColor,
      ),
    );
  }

  Widget getFromDateTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.fromDateController,
      isReadOnly: true,
      isRequired: true,
      title: AppLocalizations.of(context).translate('From'),
      haveBottomSheet: true,
      showBottomSheetOnTap: () async => showTimePickerBottomSheet(
        context: context,
        initialTime: _bloc.fromSelectedTime,
        onTimeSelected: (selectedTime) {
          _bloc.fromDateController.text = _bloc.formatTime24Hour(selectedTime);
          _bloc.fromSelectedTime = selectedTime;
          _bloc.checkFromTimeValidations(selectedTime);
          _bloc.fromTimeSelected = selectedTime;
          _bloc.add(ChangeFieldValueEvent());
        },
      ),
      suffixWidget: const Icon(
        Icons.keyboard_arrow_down_sharp,
      ),
    );
  }

  Widget getToDateTextFieldWidget() {
    return CEOMeetingsCustomTextField(
      controller: _bloc.toDateController,
      isReadOnly: true,
      isRequired: true,
      title: AppLocalizations.of(context).translate('to'),
      haveBottomSheet: true,
      showBottomSheetOnTap: () async => showTimePickerBottomSheet(
        context: context,
        initialTime: _bloc.fromSelectedTime,
        onTimeSelected: (selectedTime) {
          if (selectedTime != _bloc.toSelectedTime) {
            _bloc.toDateController.text = _bloc.formatTime24Hour(selectedTime);
            _bloc.fromSelectedTime = selectedTime;
          }
          _bloc.checkToTimeValidations(selectedTime);
          _bloc.add(ChangeFieldValueEvent());
        },
      ),
      suffixWidget: const Icon(
        Icons.keyboard_arrow_down_sharp,
      ),
    );
  }

  Widget getAgendaTextFieldWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(TextSpan(children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context).translate('Agenda'),
            style: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
          ),
          TextSpan(
              text: ' *',
              style: FontUtility.getTextStyleForText(TextType.regular,
                  textColor: redColor, size: 13)),
        ])),
        SizedBox(
          height: 7.h,
        ),
        TextFormField(
          controller: _bloc.agendaController,
          maxLines: 4,
          onChanged: (value) {
            _bloc.add(ChangeFieldValueEvent());
          },
          decoration: InputDecoration(
            hintText:
                AppLocalizations.of(context).translate('Write Agenda here'),
            hintStyle: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
            enabledBorder: getNoteWidgetOutlineInputBorder(),
            focusedBorder: getNoteWidgetOutlineInputBorder(),
            border: getNoteWidgetOutlineInputBorder(),
            errorBorder: getNoteWidgetOutlineInputBorder(),
            focusedErrorBorder: getNoteWidgetOutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget getExpectTextFieldWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(TextSpan(children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context)
                .translate('What do we expect after the meeting?'),
            style: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
          ),
          TextSpan(
              text: ' *',
              style: FontUtility.getTextStyleForText(TextType.regular,
                  textColor: redColor, size: 13)),
        ])),
        SizedBox(
          height: 7.h,
        ),
        TextFormField(
          controller: _bloc.expectedController,
          maxLines: 4,
          onChanged: (value) {
            _bloc.add(ChangeFieldValueEvent());
          },
          decoration: InputDecoration(
            hintText: AppLocalizations.of(context)
                .translate('Please summarize the expected outcomes here.'),
            hintStyle: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
            enabledBorder: getNoteWidgetOutlineInputBorder(),
            focusedBorder: getNoteWidgetOutlineInputBorder(),
            border: getNoteWidgetOutlineInputBorder(),
            errorBorder: getNoteWidgetOutlineInputBorder(),
            focusedErrorBorder: getNoteWidgetOutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget getNoteTextFieldWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text.rich(TextSpan(children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context).translate('note'),
            style: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
          ),
        ])),
        SizedBox(
          height: 7.h,
        ),
        TextFormField(
          controller: _bloc.noteController,
          maxLines: 4,
          onChanged: (value) {},
          decoration: InputDecoration(
            hintText: AppLocalizations.of(context).translate('write_note'),
            hintStyle: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
            enabledBorder: getNoteWidgetOutlineInputBorder(),
            focusedBorder: getNoteWidgetOutlineInputBorder(),
            border: getNoteWidgetOutlineInputBorder(),
            errorBorder: getNoteWidgetOutlineInputBorder(),
            focusedErrorBorder: getNoteWidgetOutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget getAttachmentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.max,
      children: [
        Text.rich(TextSpan(children: <TextSpan>[
          TextSpan(
            text: AppLocalizations.of(context).translate('File'),
            style: TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 15,
                color: secondTextColor),
          )
        ])),
        AttachUI(
          isRequired: false,
          endPoint: '',
          serviceType: 'MeetingService',
          subTitle: AppLocalizations.of(context).translate('Attachment'),
          color: primaryColor,
        ),
      ],
    );
  }

  OutlineInputBorder getNoteWidgetOutlineInputBorder() {
    return OutlineInputBorder(
      borderSide: const BorderSide(
        color: Color.fromRGBO(185, 200, 252, 0.5),
      ),
      borderRadius: BorderRadius.circular(10),
    );
  }
}
