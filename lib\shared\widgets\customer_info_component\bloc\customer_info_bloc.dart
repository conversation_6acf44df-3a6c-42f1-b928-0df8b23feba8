import 'package:eeh/shared/widgets/customer_info_component/bloc/customer_info_event.dart';
import 'package:eeh/shared/widgets/customer_info_component/bloc/customer_info_state.dart';
import 'package:eeh/shared/widgets/customer_info_component/model/customer_Info_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../network/api_helper.dart';
import '../../../utility/text_helper.dart';
import '../../toast.dart';
import '../repo/customer_info_repo.dart';

class CustomerInfoBloc extends Bloc<CustomerInfoEvent, CustomerInfoState> {
  late CustomerInfoRepo repo;
  CustomerInfoResponse? customerInfoResponse;
  CustomerInfo? customerInfo;
  List<CustomerInfo> customerInfoList = [];
  late BuildContext ctx;
  TextEditingController searchController = TextEditingController();
  String skip = "0";
  String top = "10";

  CustomerInfoBloc() : super(CustomerListInitialState()) {
    repo = CustomerInfoRepo();
    on<CustomerListInitialEvent>((event, emit) async {
      emit(CustomerListInitialState());
    });

    on<CustomerListEvent>((event, emit) async {
      emit(CustomerListLoadingState());
      await fetchCustomerList(
          emit, event.appKey, event.isInitialState, event.searchTerm);
    });

    on<SearchEvent>((event, emit) async {
      emit(SearchLoadingState());
      await (getSearch(emit, event.searchTerm, "0", "10", event.appKey));
    });
  }

  fetchCustomerList(
      emitter, appKey, bool isInitialState, String searchTerm) async {
    await repo
        .getCustomerList(
        searchTerm: searchTerm,
        skip: isInitialState ? skip : (int.parse(skip) + 10).toString(),
        top: top,
        appKey: appKey)
        .then((response) => onFetchCustomerList(response, emitter));
  }

  void onFetchCustomerList(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchCustomerSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(CustomerListErrorState());
    });
  }

  void onFetchCustomerSuccess(data, emitter) {
    customerInfoResponse = CustomerInfoResponse.fromJson(data);
    customerInfoList.addAll(customerInfoResponse?.customerInforma ?? []);
    skip = customerInfoResponse?.skip ?? "0";
    emitter(CustomerListSuccessState());
  }

  Future<void> getSearch(
      Emitter<CustomerInfoState> emit,
      String searchTerm,
      String skip,
      String top,
      String appKey,
      ) async {
    try {
      final response = await repo.getCustomerList(
        searchTerm: searchTerm,
        skip: skip,
        top: top,
        appKey: appKey,
      );

      response.maybeWhen(
        ok: (data) {
          customerInfoList =
              CustomerInfoResponse.fromJson(data).customerInforma;

          emit(SearchSuccessState());
        },
        onError: (error) {
          showMessage(error.toString(), MessageType.error);
          emit(SearchErrorState());
        },
      );
    } catch (e) {
      emit(SearchErrorState());
    }
  }
}
