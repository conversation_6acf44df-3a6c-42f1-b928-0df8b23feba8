import 'package:eeh/ceo_meetings/bloc/ceo_meetings_bloc.dart';
import 'package:eeh/ceo_meetings/bloc/ceo_meetings_events.dart';
import 'package:eeh/ceo_meetings/bloc/ceo_meetings_states.dart';
import 'package:eeh/ceo_meetings/model/employee_model.dart';
import 'package:eeh/shared/widgets/new_textformfield_component.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../l10n/app_localizations.dart';
import '../../../shared/styles/colors.dart';
import '../../../shared/utility/font_utility.dart';
import '../../../shared/widgets/cancel_button_component.dart';
import 'selected_members_widget.dart';

class CEOBottomSheetUIWidget extends StatefulWidget {
  final Function(List<Employee> selectedMembers)? onMembersSelected;
  final TextEditingController textController;
  final String title;
  final bool contentHasCheckBox;
  final List<Employee> employees;
  final EmployeesType employeesType;

  const CEOBottomSheetUIWidget({
    super.key,
    this.onMembersSelected,
    required this.employees,
    required this.textController,
    required this.title,
    required this.employeesType,
    this.contentHasCheckBox = false,
  });

  @override
  State<CEOBottomSheetUIWidget> createState() => _CEOBottomSheetUIWidgetState();
}

class _CEOBottomSheetUIWidgetState extends State<CEOBottomSheetUIWidget> {
  List<Employee> _selectedMembers = [];
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (context.read<CEOMeetingsBloc>().employeesData == null) {
      context.read<CEOMeetingsBloc>().add(GetEmployeesListEvent(
          searchKey: null,
          isSearchMode: false,
          employeesType: widget.employeesType));
    }
    _selectedMembers.addAll(widget.employees
        .where(
          (element) => element.isSelected,
        )
        .toList());
  }

  bool _areAllMembersSelected() {
    if (widget.employees.isEmpty) return false;
    return widget.employees.every((member) => member.isSelected);
  }

  void _toggleSelectAll(bool? value) {
    setState(() {
      for (Employee member in widget.employees) {
        member.isSelected = value == true ? true : false;
      }

      _updateSelectedMembers();

      _updateTextField();
    });
  }

  void _toggleMemberSelection(int index, bool? value) {
    setState(() {
      widget.employees[index].isSelected = value == true ? true : false;

      _updateSelectedMembers();

      _updateTextField();
    });
  }

  void _updateSelectedMembers() {
    _selectedMembers =
        widget.employees.where((member) => member.isSelected).toList();
     getSelectedList().clear();
    getSelectedList().addAll(_selectedMembers);
  }

  void _updateTextField() {
    if (_selectedMembers.isEmpty) {
      widget.textController.text = '';
    } else {
      widget.textController.text = _selectedMembers
          .map((member) =>
              AppLocalizations.appLang == 'en' ? member.nameEn : member.nameAr)
          .join(', ');
    }
  }

  @override
  Widget build(BuildContext context) {
    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return DraggableScrollableSheet(
        initialChildSize: widget.contentHasCheckBox ? 0.9 : 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.6,
        expand: false,
        builder: (context, scrollController) => BlocBuilder<CEOMeetingsBloc,
                CEOMeetingsState>(
            buildWhen: (previous, current) =>
                current is GetEmployeesListLoadingState ||
                current is GetEmployeesListSuccessState ||
                current is GetEmployeesListErrorState,
            builder: (context, state) {
              return state is GetEmployeesListErrorState ||
                      state is MoveToScreenState ||
                      state is ChangeFieldValueState ||
                      state is ChangeSearchTextFieldTypeState ||
                      state is GetEmployeesListSuccessState
                  ? Padding(
                      padding: EdgeInsets.only(bottom: keyboardHeight),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildBottomSheetTitle(context),
                          SizedBox(height: 16.h),
                          getSearchTextField(),
                          SizedBox(height: 16.h),
                          if (getSelectedList().isNotEmpty) ...[
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16.0),
                              child: SelectedMembersWidget(
                                selectedMembers: getSelectedList(),
                                onRemove: (index) {
                                  setState(() {
                                    widget.employees
                                        .firstWhere(
                                          (element) =>
                                              element.recId ==
                                              getSelectedList()[index].recId,
                                          orElse: () => Employee(recId: -1),
                                        )
                                        .isSelected = false;
                                    getSelectedList().removeAt(index);
                                    _updateSelectedMembers();
                                    _updateTextField();
                                    if (getSelectedList().isEmpty) {
                                      context.read<CEOMeetingsBloc>().add(
                                          GetEmployeesListEvent(
                                              searchKey: null,
                                              isSearchMode: true,
                                              employeesType:
                                                  widget.employeesType));
                                    }
                                  });
                                },
                              ),
                            ),
                            SizedBox(height: 5.h),
                          ],
                          _buildContentListView(),
                          SizedBox(height: 5.h),
                          Align(
                              alignment: Alignment.center,
                              child: _buildLoadMoreButton()),
                          SizedBox(height: 10.h),
                          widget.contentHasCheckBox
                              ? CancelButtonComponent()
                              : const SizedBox.shrink(),
                          widget.contentHasCheckBox
                              ? SubmitButtonComponent(
                                  backGroung: primaryColor,
                                  text: AppLocalizations.of(context)
                                      .translate('Add'),
                                  isLoading: false,
                                  onPressed: () {
                                    widget.onMembersSelected
                                        ?.call(_selectedMembers);
                                    Navigator.pop(context);
                                  },
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    )
                  : Center(
                      child: CircularProgressIndicator(),
                    );
            }));
  }

  Padding getSearchTextField() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
        buildWhen: (previous, current) =>
            current is ChangeSearchTextFieldTypeState,
        builder: (context, state) => NewCustomTextFieldComponent(
          type: TextFieldType.search,
          controller: searchController,
          suffixWidget: state is ChangeSearchTextFieldTypeState
              ? IconButton(
                  style: IconButton.styleFrom(
                    backgroundColor: primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4.sp),
                    ),
                  ),
                  onPressed: () => context.read<CEOMeetingsBloc>().add(
                      GetEmployeesListEvent(
                          searchKey: searchController.text,
                          isSearchMode: true,
                          employeesType: widget.employeesType)),
                  icon: Icon(Icons.search, color: Colors.white))
              : null,
          labelText: AppLocalizations.of(context)
              .translate('Search by Empolyee Name or ID'),
          onChange: (p0) => context
              .read<CEOMeetingsBloc>()
              .add(ChangeSearchTextFieldTypeEvent()),
          onRemovePressed: () => context.read<CEOMeetingsBloc>().add(
              GetEmployeesListEvent(
                  searchKey: null,
                  isSearchMode: true,
                  employeesType: widget.employeesType)),
        ),
      ),
    );
  }

  Widget _buildBottomSheetTitle(BuildContext context) {
    return Center(
      child: Text.rich(
        TextSpan(
          children: <TextSpan>[
            TextSpan(
              text: widget.title,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 16.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentListView() {
    return Expanded(
      child: Column(
        children: [
          widget.contentHasCheckBox
              ? _buildSelectAllCheckbox(context)
              : const SizedBox.shrink(),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                if (index < widget.employees.length) {
                  return widget.contentHasCheckBox
                      ? _buildMemberCheckbox(context, index)
                      : _buildMemberContent(context, index);
                } else {
                  return SizedBox.shrink();
                }
              },
              itemCount: widget.employees.length,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectAllCheckbox(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: CheckboxListTile(
        checkColor: Colors.white,
        activeColor: primaryColor,
        side: BorderSide(width: 2.0, color: Colors.black),
        checkboxShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(3.0.sp),
        ),
        contentPadding: EdgeInsets.zero,
        value: _areAllMembersSelected(),
        onChanged: _toggleSelectAll,
        title: Text(
          "${AppLocalizations.of(context).translate("All")} (${widget.employees.length})",
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildMemberContent(BuildContext context, int memberIndex) {
    if (memberIndex >= widget.employees.length) return const SizedBox.shrink();

    return InkWell(
      onTap: () {
        // Select this member and deselect all others
        setState(() {
          // First deselect all members
          for (var member in widget.employees) {
            member.isSelected = false;
          }

          // Then select only this one
          widget.employees[memberIndex].isSelected = true;

          // Update selected members list
          _updateSelectedMembers();

          // Update text field immediately
          _updateTextField();

          // Close the bottom sheet after selection
          Navigator.pop(context);
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 25.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                AppLocalizations.appLang == 'en'
                    ? widget.employees[memberIndex].nameEn ?? ''
                    : widget.employees[memberIndex].nameAr ?? '',
                style: FontUtilities.getTextStyle(
                  TextType.medium,
                  size: 14.sp,
                  fontWeight: FontWeight.w400,
                )),
            Text("#${100000 + memberIndex}",
                style: FontUtilities.getTextStyle(
                  TextType.regular,
                  size: 14.sp,
                  fontWeight: FontWeight.w400,
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberCheckbox(BuildContext context, int index) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 2.0),
      child: CheckboxListTile(
        checkColor: Colors.white,
        activeColor: primaryColor,
        side: BorderSide(width: 2.0, color: Colors.black),
        checkboxShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(3.0.sp),
        ),
        contentPadding: EdgeInsets.zero,
        value: widget.employees[index].isSelected == true,
        onChanged: (value) => _toggleMemberSelection(index, value),
        title: Text(
          AppLocalizations.appLang == 'en'
              ? widget.employees[index].nameEn ?? ''
              : widget.employees[index].nameAr ?? '',
          style: FontUtilities.getTextStyle(
            TextType.medium,
            size: 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        subtitle: Text(
          "#${100000 + index}",
          style: FontUtilities.getTextStyle(
            TextType.regular,
            size: 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return BlocBuilder<CEOMeetingsBloc, CEOMeetingsState>(
        buildWhen: (previous, current) =>
            current is GetEmployeesListNextPageLoadingState ||
            current is GetEmployeesListSuccessState,
        builder: (context, state) {
          if (state is GetEmployeesListNextPageLoadingState) {
            return CircularProgressIndicator();
          } else if (state is GetEmployeesListSuccessState) {
            return (state.employeeResponse.allElmEmploye?.length ?? 0) <
                    context.read<CEOMeetingsBloc>().top
                ? SizedBox()
                : getMoreButton();
          } else {
            return getMoreButton();
          }
        });
  }

  List<Employee> getSelectedList() {
    switch (widget.employeesType) {
      case EmployeesType.requester:
        return context.read<CEOMeetingsBloc>().selectedRequestersEmployees;
      case EmployeesType.attendees:
        return context.read<CEOMeetingsBloc>().selectedAttendeesEmployees;
      case EmployeesType.noteTaker:
        return context.read<CEOMeetingsBloc>().selectedNoteTakersEmployees;
    }
  }

  Widget getMoreButton() {
    return ElevatedButton(
      onPressed: () => context.read<CEOMeetingsBloc>().add(
          GetEmployeesListNextPageEvent(
              searchKey: searchController.text,
              isSearchMode: true,
              employeesType: widget.employeesType)),
      style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          padding: EdgeInsets.all(12.sp),
          shape: CircleBorder()),
      child: Icon(
        Icons.arrow_downward_outlined,
        size: 24.sp,
        color: Colors.white,
      ),
    );
  }
}
