abstract class NewAllowanceState {}

class NewAllowanceInitialState extends NewAllowanceState {}

class BeneficialEmployeeValueChangelState extends NewAllowanceState {}

class NumberOfMonthsValueChangelState extends NewAllowanceState {}

class StartDateValueChangelState extends NewAllowanceState {}

class EndDateValueChangelState extends NewAllowanceState {}

class CalculateEndDateState extends NewAllowanceState {}

class AddEmployeeState extends NewAllowanceState {}

class ActionTappedState extends NewAllowanceState{}

class EmployeeAllowanceLoadingState extends NewAllowanceState{}

class EmployeeAllowanceErrorState extends NewAllowanceState{}

class EmployeeAllowanceSuccessState extends NewAllowanceState{}

class SubmitAllowanceLoadingState extends NewAllowanceState{}

class SubmitAllowanceErrorState extends NewAllowanceState{}

class SubmitAllowanceSuccessState extends NewAllowanceState{}
