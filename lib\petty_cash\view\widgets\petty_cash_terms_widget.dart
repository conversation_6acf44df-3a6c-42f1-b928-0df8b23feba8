import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/name_icon_mapping.dart';

class PettyCashTermsWidget extends StatefulWidget {
  const PettyCashTermsWidget({super.key});

  @override
  State<PettyCashTermsWidget> createState() => _PettyCashTermsWidgetState();
}

class _PettyCashTermsWidgetState extends State<PettyCashTermsWidget> {
  List<String> terms1 = [];
  List<String> terms2 = [];
  @override
  void didChangeDependencies() {
    terms1 = getPettyCashTerms(context, 7, 0);
    terms2 = getPettyCashTerms(context, 8, 7);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(12.0).w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 45.w,
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              width: 3,
                              strokeAlign: BorderSide.strokeAlignCenter,
                              color: const Color(0xFF919191),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Icon(
                        getIconFromCss("fa-solid fa-shield-halved"),
                        color: primaryColor,
                        size: 36.sp,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        AppLocalizations.of(context)
                            .translate('petty_cash_terms_title'),
                        textAlign: TextAlign.center,
                        style: FontUtilities.getTextStyle(
                          TextType.medium,
                          size: 16.sp,
                          fontWeight: FontWeight.w700,
                          textColor: textMain,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 12.h),
                _getDescText(AppLocalizations.of(context)
                    .translate('petty_cash_terms_desc_1')),
                SizedBox(height: 12.h),
                ...terms1.map(_getDescText),
                SizedBox(height: 18.h),
                _getDescText(AppLocalizations.of(context)
                    .translate('petty_cash_terms_desc_2')),
                ...terms2.map(_getDescText),
                SizedBox(height: 18.h),
                _getDescText(AppLocalizations.of(context)
                    .translate('petty_cash_terms_end_desc')),
                SizedBox(height: 24.h),
              ],
            ),
          ),
        ),
        SubmitButtonComponent(
          backGroung: primaryColor,
          text: AppLocalizations.of(context).translate("closee"),
          isLoading: false,
          dividerColor: grey,
          onPressed: () {
            Navigation.popScreen(context);
          },
        ),
      ],
    );
  }

  Text _getDescText(String text) => Text(
        text,
        style: FontUtilities.getTextStyle(
          TextType.medium,
          size: 14.sp,
          fontWeight: FontWeight.w400,
          textColor: secondTextColor,
        ),
      );

  List<String> getPettyCashTerms(
      BuildContext context, int count, int startFrom) {
    return List.generate(count, (index) {
      String key = 'petty_cash_terms_${startFrom + (index + 1)}';
      return AppLocalizations.of(context).translate(key);
    });
  }
}
