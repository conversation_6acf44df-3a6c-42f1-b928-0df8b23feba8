part of 'localization_bloc.dart';

sealed class LocalizationEvent extends Equatable {
  const LocalizationEvent();

  @override
  List<Object> get props => [];
}

class LocalizationChangeEvent extends LocalizationEvent {}

class LocalizationChangeToEnglishEvent extends LocalizationEvent {}

class LocalizationChangeToArabicEvent extends LocalizationEvent {}

class LocalizationToggleEvent extends LocalizationEvent {}

abstract class LocaleEvent{}

class ChangeLocale extends LocaleEvent {
  final LocaleCaller localeCaller;

  ChangeLocale(this.localeCaller);
}
