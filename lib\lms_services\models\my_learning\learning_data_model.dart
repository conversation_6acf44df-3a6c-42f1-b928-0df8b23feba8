import 'dart:convert';

import 'package:eeh/lms_services/models/my_learning/dashbord_kpi.dart';
import 'package:eeh/lms_services/models/my_learning/item_detail.dart';

class LearningData {
  final DashboardKpi dashboardKpi;
  final List<ItemDetail> itemDetails;

  LearningData({
    required this.dashboardKpi,
    required this.itemDetails,
  });

  factory LearningData.fromJsonString(String jsonString) {
    final dynamic decodedJson = jsonDecode(jsonString);

    // Handle case where the JSON string contains a List at root level
    if (decodedJson is List && decodedJson.isNotEmpty) {
      final Map<String, dynamic> json = decodedJson[0] as Map<String, dynamic>;
      return LearningData.fromJson(json);
    } else if (decodedJson is Map<String, dynamic>) {
      return LearningData.fromJson(decodedJson);
    } else {
      throw Exception('Invalid JSON format for LearningData: expected Map or List, got ${decodedJson.runtimeType}');
    }
  }

  factory LearningData.fromJson(Map<String, dynamic> json) {
    return LearningData(
      dashboardKpi: DashboardKpi.fromJson(json['dashboard_kpi']),
      itemDetails: (json['item_details'] as List)
          .map((item) => ItemDetail.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dashboard_kpi': dashboardKpi.toJson(),
      'item_details': itemDetails.map((item) => item.toJson()).toList(),
    };
  }
}
