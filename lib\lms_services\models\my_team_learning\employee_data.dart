class EmployeeData {
  final String total;
  final String year;
  final String courses;
  final String? overdue;
  final String planned;
  final String quizzes;
  final String completed;
  final String? inProgress;
  final String managerId;
  final String employeeId;
  final String? numOfEmployees;
  final String? positionAr;
  final String? positionEn;
  final String certificates;
  final String? departmentAr;
  final String? departmentEn;
  final String? managedSectors;

  EmployeeData({
    required this.total,
    required this.year,
    required this.courses,
    this.overdue,
    required this.planned,
    required this.quizzes,
    required this.completed,
    this.inProgress,
    required this.managerId,
    required this.employeeId,
    this.numOfEmployees,
    this.positionAr,
    this.positionEn,
    required this.certificates,
    this.departmentAr,
    this.departmentEn,
    this.managedSectors,
  });

  factory EmployeeData.fromJson(Map<String, dynamic> json) {
    return EmployeeData(
      total: json['total']?.toString() ?? '0',
      year: json['v_year']?.toString() ?? '',
      courses: json['courses']?.toString() ?? '0',
      overdue: json['overdue']?.toString(),
      planned: json['planned']?.toString() ?? '0',
      quizzes: json['quizzes']?.toString() ?? '0',
      completed: json['completed']?.toString() ?? '0',
      inProgress: json['inprogress']?.toString(),
      managerId: json['manager_id']?.toString() ?? '',
      employeeId: json['employee_id']?.toString() ?? '',
      numOfEmployees: json['num_of_emps']?.toString(),
      positionAr: json['position_ar']?.toString(),
      positionEn: json['position_en']?.toString(),
      certificates: json['certificates']?.toString() ?? '0',
      departmentAr: json['department_ar']?.toString(),
      departmentEn: json['department_en']?.toString(),
      managedSectors: json['managed_sectors']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'v_year': year,
      'courses': courses,
      'overdue': overdue,
      'planned': planned,
      'quizzes': quizzes,
      'completed': completed,
      'inprogress': inProgress,
      'manager_id': managerId,
      'employee_id': employeeId,
      'num_of_emps': numOfEmployees,
      'position_ar': positionAr,
      'position_en': positionEn,
      'certificates': certificates,
      'department_ar': departmentAr,
      'department_en': departmentEn,
      'managed_sectors': managedSectors,
    };
  }

  // Helper methods to get numeric values
  int get totalCount => int.tryParse(total) ?? 0;
  int get coursesCount => int.tryParse(courses) ?? 0;
  int get overdueCount => int.tryParse(overdue ?? '0') ?? 0;
  int get plannedCount => int.tryParse(planned) ?? 0;
  int get quizzesCount => int.tryParse(quizzes) ?? 0;
  int get completedCount => int.tryParse(completed) ?? 0;
  int get inProgressCount => int.tryParse(inProgress ?? '0') ?? 0;
  int get certificatesCount => int.tryParse(certificates) ?? 0;
  int get numOfEmployeesCount => int.tryParse(numOfEmployees ?? '0') ?? 0;

  // Helper method to get completion percentage
  double get completionPercentage {
    if (totalCount == 0) return 0.0;
    return (completedCount / totalCount) * 100;
  }
}
