
import 'package:eeh/tender_request/model/tender_request_model.dart';

const String department="Department";
const String projectw="Project WBS";

final List<BudgetTypeModel> staticBudgetTypes = [
  BudgetTypeModel(
    nameAr: "الإدارة",
    nameEn: department,
    id: department,
    requiredFields: ["costCenter"],
  ),
  BudgetTypeModel(
    nameAr: "مشروع WBS",
    nameEn: "Project WBS",
    id:projectw,
    requiredFields: ["project"],
  ),

];

final List<PaymentTypeModel> staticPaymentTypes = [
  PaymentTypeModel(
    nameAr: "تحويل بنكي",
    nameEn: "Bank Transfer",
    requiredFields: ["beneficiaryName", "bank", "iban"],
  ),
  PaymentTypeModel(
    nameAr: "شيك بنكي",
    nameEn: "Bank Cheque",
    requiredFields: ["beneficiaryName"],
  ),
  PaymentTypeModel(
    nameAr: "رقم سداد",
    nameEn: "SADAD Number",
    requiredFields: ["sadadNumber"],
  ),

];



final List<CustomerNameModel> staticCustomerNameModel = [
  CustomerNameModel(
    nameAr: "احمد",
    nameEn: "Ahmed",

  ),

  CustomerNameModel(
    nameAr: "علي",
    nameEn: "Ali",

  ),

];

final List<ProjectModel> staticProjectModel = [
  ProjectModel(
    nameAr: "مشروع",
    nameEn: "eml",

  ),

  ProjectModel(
    nameAr: "مشروع",
    nameEn: "Wbs",

  ),

];