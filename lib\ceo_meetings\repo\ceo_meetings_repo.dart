import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/network/api_utilities.dart';
import 'package:eeh/shared/network/endpoints.dart';
import 'package:eeh/shared/utility/secure_storage.dart';

abstract class ICEOMeetingsRepo {
  Future<NetworkResponse> getElmCEOMembers();

  Future<NetworkResponse> getDropdownListData();

  Future<NetworkResponse> getEmployeesList(Map<String, dynamic> payload);

  Future<NetworkResponse> submitRequest(Map<String, dynamic> requestPayload);
}

class CEOMeetingsRepo implements ICEOMeetingsRepo {
  CEOMeetingsRepo._internal();

  static final CEOMeetingsRepo _instance = CEOMeetingsRepo._internal();

  static CEOMeetingsRepo get instance => _instance;

  @override
  Future<NetworkResponse> getElmCEOMembers() async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await <PERSON><PERSON><PERSON><PERSON><PERSON>().apiCall(genericObject,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        body: {
          "group_name": ""
        },
        headers: {
          'formName': 'Elm_Group_Memebers Form',
          'moduleName': 'Elm_Group_Memebers',
          'appKey': 'CEO',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> getDropdownListData() async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        body: {
          "optimizedUdasValueMap": ""
        },
        headers: {
          'formName': 'Get Dropdown list data Form',
          'moduleName': 'Get Dropdown list data',
          'appKey': 'CEO',
          'Content-Type': 'application/json'
        });
    return response;
  }

  @override
  Future<NetworkResponse> submitRequest(
      Map<String, dynamic> requestPayload) async {
    String sessionToken =
        await SecureStorageService().readSecureData(key: "user_session") ?? '';
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        body: requestPayload,
        requestType: RequestType.post,
        sessionToken: sessionToken,
        headers: {
          'formName': 'Service Data Form',
          'moduleName': 'Service Data',
          'appKey': 'CEO',
          'Content-Type': 'application/json'
        });
    return response;
  }
  
  @override
  Future<NetworkResponse> getEmployeesList(Map<String, dynamic> payload) async{
    String sessionToken =
        await SecureStorageService().readSecureData(key: 'user_session') ?? "";
    NetworkResponse<dynamic> response = await ApiHelper().apiCall(genericObject,
        requestType: RequestType.post,
        sessionToken: sessionToken,

        body: payload,
        headers: {
          'formName': 'All Elm Employee Form',
          'moduleName': 'All Elm Employee',
          'appKey': 'CEO',
          'Content-Type': 'application/json'
        });
    return response;
  }
}
