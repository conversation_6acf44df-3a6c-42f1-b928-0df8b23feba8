// import 'package:eeh/admin_service/Model/service%20list%20models/service_list_parent_model.dart';

// class EsmServiceResponse extends IEsmServiceResponse {
//   EsmServiceResponse({
//     super.createdBy,
//     super.createdById,
//     super.createdDate,
//     super.recId,
//     super.esmServices,
//     super.esmTypes,
//     super.esmProviders,
//     super.esmAttachments,
//   });

//   factory EsmServiceResponse.fromJson(Map<String, dynamic> json) {
//     return EsmServiceResponse(
//       createdBy: json['createdBy'],
//       createdById: json['createdById'],
//       createdDate: json['createdDate'],
//       recId: json['recId'],
//       esmServices: (json['esm_services'] as List<dynamic>?)
//           ?.map((e) => EsmService.fromJson(e as Map<String, dynamic>))
//           .toList(),
//       esmTypes: (json['esm_types'] as List<dynamic>?)
//           ?.map((e) => EsmType.fromJson(e as Map<String, dynamic>))
//           .toList(),
//       esmProviders: (json['esm_provider'] as List<dynamic>?)
//           ?.map((e) => EsmProvider.fromJson(e as Map<String, dynamic>))
//           .toList(),
//       esmAttachments: (json['esmattachments'] as List<dynamic>?)
//           ?.map((e) => EsmAttachment.fromJson(e as Map<String, dynamic>))
//           .toList(),
//     );
//   }
// }

// class EsmService extends Service {
//   EsmService({
//     super.nameen,
//     super.esmCategory,
//     super.esmTypeName,
//     super.defaultPriority,
//     super.esmTypeKey,
//     super.provNameAr,
//     super.provName,
//     super.esmTypeNameAr,
//     super.provKey,
//     super.namear,
//     super.recid,
//     super.esmSrevsKey,
//   });

//   factory EsmService.fromJson(Map<String, dynamic> json) {
//     return EsmService(
//       nameen: json['esm_srvs_name'],
//       esmCategory: json['esm_category'],
//       esmTypeName: json['esm_type_name'],
//       defaultPriority: json['defaultpriority'],
//       esmTypeKey: json['esm_type_key'],
//       provNameAr: json['prov_name_ar'],
//       provName: json['prov_name'],
//       esmTypeNameAr: json['esm_type_name_ar'],
//       provKey: json['prov_key'],
//       namear: json['esm_srvs_name_ar'],
//       recid: json['recid'],
//       esmSrevsKey: json['esm_servise_key'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'esm_srvs_name': nameen,
//       'esm_category': esmCategory,
//       'esm_type_name': esmTypeName,
//       'defaultpriority': defaultPriority,
//       'esm_type_key': esmTypeKey,
//       'prov_name_ar': provNameAr,
//       'prov_name': provName,
//       'esm_type_name_ar': esmTypeNameAr,
//       'prov_key': provKey,
//       'esm_srvs_name_ar': namear,
//       'recid': recid,
//       'esm_servise_key': esmSrevsKey,
//     };
//   }
// }

// class EsmType extends Type {
//   EsmType({
//     super.nameen,
//     super.provName,
//     super.namear,
//     super.provKey,
//     super.esmTypeKey,
//     super.provNameAr,
//     super.recid,
//   });

//   factory EsmType.fromJson(Map<String, dynamic> json) {
//     return EsmType(
//       nameen: json['esm_type_name'],
//       provName: json['prov_name'],
//       namear: json['esm_type_name_ar'],
//       provKey: json['prov_key'],
//       esmTypeKey: json['esm_type_key'],
//       provNameAr: json['prov_name_ar'],
//       recid: json['recid'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'esm_type_name': nameen,
//       'prov_name': provName,
//       'esm_type_name_ar': namear,
//       'prov_key': provKey,
//       'esm_type_key': esmTypeKey,
//       'prov_name_ar': provNameAr,
//       'recid': recid,
//     };
//   }
// }

// class EsmProvider extends Provider {
//   EsmProvider({
//     super.nameen,
//     super.provKey,
//     super.namear,
//     super.recid,
//   });

//   factory EsmProvider.fromJson(Map<String, dynamic> json) {
//     return EsmProvider(
//       nameen: json['prov_name'],
//       provKey: json['prov_key'],
//       namear: json['prov_name_ar'],
//       recid: json['recid'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'prov_name': nameen,
//       'prov_key': provKey,
//       'prov_name_ar': namear,
//       'recid': recid,
//     };
//   }
// }