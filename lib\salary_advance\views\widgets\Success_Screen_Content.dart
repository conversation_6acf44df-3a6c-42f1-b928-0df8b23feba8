
import 'package:flutter/cupertino.dart';

import '../../../l10n/app_localizations.dart';
import '../../../success_view/success_screen_view.dart';
import '../../models/submit_response_model.dart';


class SuccessScreenContentWidgetS extends StatelessWidget {
  final SalaryAdvanceSubmitResponse? submitResponse;
  const SuccessScreenContentWidgetS(
      {super.key, required this.submitResponse});

  @override
  Widget build(BuildContext context) {
    return submitResponse == null
        ? SizedBox.shrink()
        : SingleChildScrollView(
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderText(AppLocalizations.of(context).translate('Request Type')),
            buildDetailsText(submitResponse?.requestType ?? ''),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate('Request ID')),
            buildDetailsText(submitResponse?.display_recId.toString() ?? ''),
            buildDivider(),

            buildHeaderText(AppLocalizations.of(context).translate("Reason")),
            buildDetailsText(submitResponse?.reason ?? ''),
            buildDivider(),
          ],
        ),
      ),
    );
  }
}
