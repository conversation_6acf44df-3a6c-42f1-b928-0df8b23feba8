import 'package:eeh/allowance_services/model/submit_allowance_response_model.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';

class SuccessScreenContentWidget extends StatelessWidget {
  final SubmitAllowanceResponse? submitResponse;
  const SuccessScreenContentWidget(
      {super.key, required this.submitResponse});

  @override
  Widget build(BuildContext context) {
    return submitResponse == null
        ? SizedBox.shrink()
        : SingleChildScrollView(
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildHeaderText(AppLocalizations.of(context).translate('Request Type')),
                buildDetailsText(AppLocalizations.appLang == 'en' ? (submitResponse?.requestType??'') : (submitResponse?.requestTypeAr??'')),
                buildDivider(),

                buildHeaderText(AppLocalizations.of(context).translate('Request ID')),
                buildDetailsText(submitResponse?.displayRecId.toString() ?? ''),
                buildDivider(),

                buildHeaderText(AppLocalizations.of(context).translate("Number of Employees")),
                buildDetailsText(submitResponse?.numOfEmps.toString() ?? ''),
                buildDivider(),
              ],
            ),
        );
  }
}
