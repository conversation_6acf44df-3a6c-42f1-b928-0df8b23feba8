import 'dart:convert';

import 'package:eeh/login/bloc/login_events.dart';
import 'package:eeh/login/models/get_emp_info_response.dart';
import 'package:eeh/shared/app_constants.dart';
import 'package:eeh/shared/network/api_helper.dart';
import 'package:eeh/shared/utility/local_notification_utility.dart';
import 'package:eeh/shared/utility/mangers/encreption_util.dart';
import 'package:eeh/shared/utility/methods.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/utility/secure_storage.dart';
import 'package:eeh/shared/utility/shared_pref.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../l10n/app_localizations.dart';
import '../../service_details_revamp/details_layout_revamp.dart';
import '../../shared/widgets/toast.dart';
import '../../shared/work_widget/work_item_layout.dart';
import '../login_repo.dart';
import '../models/login_user_info.dart';
import '../models/main_login_info.dart';
import '../widgets/biometric.dart';
import 'login_states.dart';

class LoginBLoc extends Bloc<LoginEvent, LoginState> {
  final storage = SecureStorageService();
  bool isUsernameValid = false;
  bool isPasswordValid = false;
  bool isSecurePassword = true;
  bool isRememberMe = false;
  bool shouldSaveCredentials = false;
  bool isBiometricsLogin = false;
  late User user;
  late GetEmployeeInfoResponse empInfoResponse;
  final LocalAuthentication auth = LocalAuthentication();
  late BuildContext ctx;
  String? deviceToken;

  LoginBLoc() : super(LoginInitialState()) {
    on<LoginInitialEvent>((event, emit) async {
      isRememberMe = await SharedPref.get(key: "isRememberMe") ?? false;
      String session = await storage.readSecureData(key: "user_session") ?? "";
      // await LocalNotificationService.initialize();
      // await initFirebaseConfiguration();
      emit(LoginInitialState());
      if (isRememberMe && session.isNotEmpty && session != '') {
        await handleLoginWithBiometrics(emit);
      } else {
        isRememberMe = false;
      }
    });
    on<LoginLoadingEvent>((event, emit) async {
      emit(LoginLoadingState());
      await login(event.username, event.password, emit);
    });
    on<LoadEmpInfoEvent>((event, emit) async {
      emit(LoginLoadingState());
      await getEmpInfo(emit);
    });
    on<UsernameValidatorEvent>((event, emit) async {
      isUsernameValid = event.isUsernameValid;
      emit(UsernameValidatorState());
    });
    on<PasswordValidatorEvent>((event, emit) async {
      isPasswordValid = event.isPasswordValid;
      emit(PasswordValidatorState());
    });
    on<SecurePasswordEvent>((event, emit) async {
      isSecurePassword = !event.isSecurePassword;
      emit(SecurePasswordState());
    });
    on<KeepMeLoggedEvent>((event, emit) async {
      isRememberMe = event.isSelected;
      if (isRememberMe) {
        shouldSaveCredentials =
            (await showBiometricForm(event.context) ?? false);
      }
      // await storage.write(
      //     key: "isRememberMe",
      //     value: (isRememberMe && shouldSaveCredentials).toString());
      await SharedPref.set(
          key: "isRememberMe", value: (isRememberMe && shouldSaveCredentials));

      checkClearingUserCredentials();
      emit(KeepMeLoggedState());
    });
  }

  LoginPayload loginPayload = LoginPayload(
      loginUserInfo: LoginUserInfo(
        companyName: "NTG",
        loginUserName: "",
        sessionLanguage: AppLocalizations.appLang,
      ),
      password: '',
      deviceToken: '');

  login(String loginUserName, String password, emitter) async {
    try {
      password = EncryptionUtil().encryptLoginPassword(password);
      await storage.writeSecureData(key: "user_name", value: loginUserName);
      loginPayload.loginUserInfo.loginUserName = loginUserName;
      loginPayload.password = password;
      loginPayload.deviceToken =
          await FirebaseMessaging.instance.getToken() ?? '';
      await LoginRepo().getUserData(loginPayload).then((response) async {
        await onResponse(response, emitter);
      });
    } on FirebaseException {
      showMessage("No Internet Connection", MessageType.error);
      emitter(LoginErrorState());
    }
  }

  onResponse(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) async {
      await onLoginSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(LoginErrorState());
    });
  }

  onLoginSuccess(data, emitter) async {
    // var val =
    //     '{    "MainLoginInfo": {        "fullName": "mthassan",        "Mail": "<EMAIL>",        "isExcludeFilterRegion": false,        "WorkingDays": "1,2,3,4,5",        "hourcost": 100.0,        "WorkingHours": 9.0,        "NumberAvilableReports": 0,        "EmpStatus": 1,        "authUserBy": 0,        "BranchName": "Egypt",        "userType": 0,        "loginType": 1,        "loginUserName": "mthassan",        "sessionLanguage": "en",        "isTwoFAEnabled": true,        "branchName": "Egypt",        "excludeFilterRegion": false,        "numberAvilableReports": 0,        "empStatus": 1,        "workingHours": 9.0,        "workingDays": "1,2,3,4,5",        "mail": "<EMAIL>"    },    "ServerTimeZoneOffset": 0,    "managerInfo": {},    "privilegeInfo": {        "haveAdminPrivilege": false,        "haveSuperAdminPrivilege": false,        "manageAllUsersPrivilege": false,        "manageAppsPrivilege": false,        "manageMyUsersPrivilege": false,        "manageAllAppsPrivilege": false,        "managePrivateAppsPrivilege": false,        "manageRepositoriesPrivilege": false,        "manageLicensePrivilege": false,        "manageSystemPropertiesPrivilege": false,        "grantPrivilegesPrivilege": false,        "manageGroupsPrivilege": false,        "viewPersonalProfilePrivilege": false    },    "employeeId": "1088",    "UserGroups": [        {            "RecID": 0,            "GroupID": 4,            "Group_Name": "Ldap users",            "UserID": 1088,            "userPermissionLevel": 0,            "isdefault": false,            "groupID": 4,            "userID": 1088,            "recID": 0,            "group_Name": "Ldap users"        }    ],    "sendOTPToken": "U2FsdGVkX19hfy=++TJI8s9A37Kgnn6YJi+JfhYzJNSPARf13iQTSVkn2fcpq9jn2sUbzSadzSwpndiPOne143Jg==&!:U2FsdGVkX1=++=++20dacUaBeoNCNr1yZJLHzCkT8AN31rOQBXUnOPIvVGKcbTyxRwSysfOf6l9IL80Xb67NItcqHw==3rhsb4",    "checkOTPToken": "U2FsdGVkX1+sWITJ3nrvUvn7amLaSC5fnfhtimGs2Zcpi0rn1PE0n9j1llDb2LsO9ePH7KCUik2dyV5RceTsfg==&!:U2FsdGVkX1+cd+lmpG0B7eag9JNwJlEsL0Kz3iHTQZXLsaO0HZPQ9c85FmXbW=++FN2d4170",    "otpStatus": "Pending OTP verification",    "mainLoginInfo": {        "fullName": "mthassan",        "Mail": "<EMAIL>",        "isExcludeFilterRegion": false,        "WorkingDays": "1,2,3,4,5",        "hourcost": 100.0,        "WorkingHours": 9.0,        "NumberAvilableReports": 0,        "EmpStatus": 1,        "authUserBy": 0,        "BranchName": "Egypt",        "userType": 0,        "loginType": 1,        "loginUserName": "mthassan",        "sessionLanguage": "en",        "isTwoFAEnabled": true,        "branchName": "Egypt",        "excludeFilterRegion": false,        "numberAvilableReports": 0,        "empStatus": 1,        "workingHours": 9.0,        "workingDays": "1,2,3,4,5",        "mail": "<EMAIL>"    },    "userGroups": [        {            "RecID": 0,            "GroupID": 4,            "Group_Name": "Ldap users",            "UserID": 1088,            "userPermissionLevel": 0,            "isdefault": false,            "groupID": 4,            "userID": 1088,            "recID": 0,            "group_Name": "Ldap users"        }    ],    "serverTimeZoneOffset": 0}';
    // Map<String, dynamic> valueMap = json.decode(val);
    user = userFromJson(data);
    emitter(LoginSuccessState());
    isSecurePassword = true;
  }

  saveUserData() async {
    await storage.writeSecureData(
        key: "user_name", value: loginPayload.loginUserInfo.loginUserName);
  }

  Future setDataWhenLoggedInSuccess() async {
    if (user.sendOTPToken.isNotEmpty && user.checkOTPToken.isNotEmpty) {
      await storage.writeSecureData(key: "check_otp", value: user.checkOTPToken);
      await storage.writeSecureData(key: "send_otp", value: user.sendOTPToken);
      await storage.writeSecureData(
          key: "user_login_response", value: user.toJson().toString());
    }
    if (shouldSaveCredentials) {
      saveUserData();
    }
  }

  checkClearingUserCredentials() async {
    if (!isRememberMe) {
      await FlutterSecureStorage().delete(
          key: "user_name",
          iOptions:
              IOSOptions(accessibility: KeychainAccessibility.first_unlock));
    }
  }

  Future<bool> shouldLocallyAuthenticate() async {
    // return false;
    return /* !kDebugMode&&*/ (await auth.canCheckBiometrics ||
        await auth.isDeviceSupported());
  }

  handleLoginWithBiometrics(emitter) async {
    emitter(LoginLoadingWithBiometricsState());
    // await onLocalAuthSuccess(true, emitter);
    if (await shouldLocallyAuthenticate()) {
      await auth
          .authenticate(
        localizedReason: 'Please authenticate to Login',
      )
          .then((value) async {
        await onLocalAuthSuccess(value, emitter);
      }).catchError((e) {
        emitter(LoginErrorState());
      });
    } else {
      emitter(LoginErrorState());
    }
  }

  Future<void> onLocalAuthSuccess(bool isAuth, emitter) async {
    if (isAuth) {
      final String username = await storage.readSecureData(key: "user_name") ?? "";
      if (username.isNotEmpty) {
        isBiometricsLogin = true;
        performSuccessVibration();
        await getEmpInfo(emitter);
        await addDeviceToken(emitter);
      }
    } else {
      isBiometricsLogin = false;
      emitter(LoginErrorState());
    }
  }

  Future addDeviceToken(Emitter<LoginState> emit) async {
    String userSession = await storage.readSecureData(key: 'user_session') ?? '';
    String deviceToken = await FirebaseMessaging.instance.getToken() ?? '';
    await LoginRepo()
        .addDeviceToken(userSession, deviceToken)
        .then((response) => onAddDeviceTokenResponse(response, emit));
  }

  void onAddDeviceTokenResponse(NetworkResponse response, Emitter emitter) {
    response.maybeWhen(ok: (data) {
      emitter(AddDeviceTokenSuccess());
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(AddDeviceTokenError());
    });
  }

  getEmpInfo(emitter) async {
    GetEmployeeInfoResponse? getEmployeeInfoResponse;
    var userSession = await storage.readSecureData(key: 'user_session');
    String? empInfo =
        await  storage.readSecureData(key: "emp_info_response");
    if (empInfo != null) {
      Map<String, dynamic> jsonMap = jsonDecode(empInfo);
      getEmployeeInfoResponse = GetEmployeeInfoResponse.fromJson(jsonMap);
    }
    await LoginRepo()
        .getEmployeeInfo(
            getEmployeeInfoResponse?.employeeemail ?? '', userSession ?? '')
        .then((response) => onGetEmpInfoResponse(response, emitter))
        .whenComplete(() async {
      await storage.writeSecureData(
          key: "emp_info_response",
          value: jsonEncode(empInfoResponse.toJson()));
    });
  }

  onGetEmpInfoResponse(NetworkResponse response, emitter) {
    response.maybeWhen(ok: (data) {
      onGetEmpInfoSuccess(data, emitter);
    }, onError: (error) {
      onGetEmpInfoError(error, emitter);
    });
  }

  onGetEmpInfoSuccess(data, emitter) async {
    empInfoResponse = GetEmployeeInfoResponse.fromJson(data);
    dynamic notificationValue = await SharedPref.get(key: 'notificationData');
    bool isAppOpenedFromNotification = notificationValue == null ? false : true;
    emitter(LoadEmpInfoSuccessState(isAppOpenedFromNotification));
  }

  onGetEmpInfoError(error, emitter) {
    showMessage(error.toString(), MessageType.error);
    emitter(LoadEmpInfoErrorState());
  }

  Future<void> initFirebaseConfiguration() async {
    await FirebaseMessaging.instance.deleteToken();
    final status = await Permission.notification.status;
    if (status.isGranted) {
      await FirebaseMessaging.instance.requestPermission();
      deviceToken = await FirebaseMessaging.instance.getToken();

      FirebaseMessaging.onMessage.listen((message) async {
        await LocalNotificationService.showNotification(message);
      });

      FirebaseMessaging.onMessageOpenedApp.listen((message) async {
        int? taskId = int.tryParse(message.data['taskId']);
        String? taskTableName = message.data['taskTableName'];
        int? requestId = int.tryParse(message.data['recId']);
        String? requestType = message.data['requesttype'];

        if (requestType == null) {
          requestType = message.data['udaTableName'];
        }
        String? notificationReason = message.data['notificationreason'];
        bool isRequest = false;
        print("Notification Message :");
        if (kDebugMode) {
          print(message.data);
        }
        if (requestId != null && taskId != null && taskId != 0) {
          isRequest = false;
        } else if (requestId != null && taskId == 0) {
          isRequest = true;
        }
        if (navigatorKey.currentWidget != null &&
            navigatorKey.currentContext != null) {
          Navigation.navigateToScreen(
              navigatorKey.currentContext!,
              DetailsLayoutRevamp(
                isNotification: true,
                taskTableName: taskTableName,
                workType:
                    isRequest ? WorkType.requestDetails : WorkType.taskDetails,
                notificationReason: notificationReason,
                requestId: requestId.toString(),
                taskId: taskId.toString(),
                requestType: requestType ?? '',
              ));
        }
      });
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
    } else {
      return;
    }
  }

  @pragma('vm:entry-point')
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage remoteMessage) async {}
}
