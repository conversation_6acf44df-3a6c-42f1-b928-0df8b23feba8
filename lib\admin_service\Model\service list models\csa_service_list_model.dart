// import 'package:eeh/admin_service/Model/service%20list%20models/service_list_parent_model.dart';
// class CSAServiceResponse extends IEsmServiceResponse {
//   CSAServiceResponse({
//     super.createdBy,
//     super.createdById,
//     super.createdDate,
//     super.recId,
//     super.esmServices,
//     super.esmTypes,
//     super.esmProviders,
//     super.esmAttachments,
//   });

//   factory CSAServiceResponse.fromJson(Map<String, dynamic> json) {
//     return CSAServiceResponse(
//       createdBy: json['createdBy'],
//       createdById: json['createdById'],
//       createdDate: json['createdDate'],
//       recId: json['recId'],
//       esmServices: (json['HSS_services'] as List<dynamic>?)
//           ?.map((e) => CSAService.fromJson(e as Map<String, dynamic>))
//           .toList(),
//       esmTypes: (json['HSS_types'] as List<dynamic>?)
//           ?.map((e) => CSAType.fromJson(e as Map<String, dynamic>))
//           .toList(),
//       esmProviders: (json['HSS_provider'] as List<dynamic>?)
//           ?.map((e) => CSAProvider.fromJson(e as Map<String, dynamic>))
//           .toList(),
//       esmAttachments: (json['esmattachments'] as List<dynamic>?)
//           ?.map((e) => EsmAttachment.fromJson(e as Map<String, dynamic>))
//           .toList(),
//     );
//   }
// }

// class CSAService extends Service {
//   CSAService({
//     super.nameen,
//     super.esmCategory,
//     super.esmTypeName,
//     super.defaultPriority,
//     super.esmTypeKey,
//     super.provNameAr,
//     super.provName,
//     super.esmTypeNameAr,
//     super.provKey,
//     super.namear,
//     super.recid,
//     super.esmSrevsKey,
//   });

//   factory CSAService.fromJson(Map<String, dynamic> json) {
//     return CSAService(
//       nameen: json['hss_srvs_name'],
//       esmCategory: json['hss_category'],
//       esmTypeName: json['hss_type_name'],
//       defaultPriority: json['defaultpriority'],
//       esmTypeKey: json['hss_type_key'],
//       provNameAr: json['prov_name_ar'],
//       provName: json['prov_name'],
//       esmTypeNameAr: json['hss_type_name_ar'],
//       provKey: json['prov_key'],
//       namear: json['hss_srvs_name_ar'],
//       recid: json['recid'],
//       esmSrevsKey: json['hss_servise_key'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'hss_srvs_name': nameen,
//       'hss_category': esmCategory,
//       'hss_type_name': esmTypeName,
//       'defaultpriority': defaultPriority,
//       'hss_type_key': esmTypeKey,
//       'prov_name_ar': provNameAr,
//       'prov_name': provName,
//       'hss_type_name_ar': esmTypeNameAr,
//       'prov_key': provKey,
//       'hss_srvs_name_ar': namear,
//       'recid': recid,
//       'hss_servise_key': esmSrevsKey,
//     };
//   }
// }

// class CSAType extends Type {
//   CSAType({
//     super.nameen,
//     super.provName,
//     super.namear,
//     super.provKey,
//     super.esmTypeKey,
//     super.provNameAr,
//     super.recid,
//   });

//   factory CSAType.fromJson(Map<String, dynamic> json) {
//     return CSAType(
//       nameen: json['hss_type_name'],
//       provName: json['prov_name'],
//       namear: json['hss_type_name_ar'],
//       provKey: json['prov_key'],
//       esmTypeKey: json['hss_type_key'],
//       provNameAr: json['prov_name_ar'],
//       recid: json['recid'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'hss_type_name': nameen,
//       'prov_name': provName,
//       'hss_type_name_ar': namear,
//       'prov_key': provKey,
//       'hss_type_key': esmTypeKey,
//       'prov_name_ar': provNameAr,
//       'recid': recid,
//     };
//   }
// }

// class CSAProvider extends Provider {
//   CSAProvider({
//     super.nameen,
//     super.provKey,
//     super.namear,
//     super.recid,
//   });

//   factory CSAProvider.fromJson(Map<String, dynamic> json) {
//     return CSAProvider(
//       nameen: json['prov_name'],
//       provKey: json['prov_key'],
//       namear: json['prov_name_ar'],
//       recid: json['recid'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'prov_name': nameen,
//       'prov_key': provKey,
//       'prov_name_ar': namear,
//       'recid': recid,
//     };
//   }
// }