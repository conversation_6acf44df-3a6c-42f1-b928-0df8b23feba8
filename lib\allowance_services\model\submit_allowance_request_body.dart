class SupmitAllowanceBody {
  List<Employees>? employeesallowa;
  String numOfEmps;
  String? cancelComment;

  SupmitAllowanceBody({required this.employeesallowa, required this.numOfEmps,this.cancelComment});

  factory SupmitAllowanceBody.fromJson(Map<String, dynamic> json) {
    return SupmitAllowanceBody(
      employeesallowa: (json['employeesallowa'] as List<dynamic>?)
          ?.map((e) => Employees.fromJson(e as Map<String, dynamic>))
          .toList(),
      numOfEmps: json['numofemps'],
      cancelComment: json['cancelcomment'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employeesallowa': employeesallowa?.map((e) => e.toJson()).toList(),
      'numofemps': numOfEmps,
      'cancelcomment':cancelComment,
    };
  }
}

class Employees {
  int? employeeId;
  String? employeeNameAr;
  String? employeeNameEn;
  String? startDate;
  String? endDate;
  int? eligibleAmount;
  int? numberOfMonths;
  String? comments;

  Employees({
    this.employeeId,
    this.employeeNameAr,
    this.employeeNameEn,
    this.startDate,
    this.endDate,
    this.eligibleAmount,
    this.numberOfMonths,
    this.comments,
  });

  factory Employees.fromJson(Map<String, dynamic> json) {
    return Employees(
      employeeId: json['employeeid'],
      employeeNameAr: json['employeename_ar'],
      employeeNameEn: json['employeename_en'],
      startDate: json['startdate'],
      endDate: json['enddate'],
      eligibleAmount: (json['eligibleamount']),
      numberOfMonths: json['numberofmonths'],
      comments: json['comments'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employeeid': employeeId,
      'employeename_ar': employeeNameAr,
      'employeename_en': employeeNameEn,
      'startdate': startDate,
      'enddate': endDate,
      'eligibleamount': eligibleAmount,
      'numberofmonths': numberOfMonths,
      'comments': comments,
    };
  }
}
