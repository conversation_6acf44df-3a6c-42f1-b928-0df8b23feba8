import 'package:eeh/admin_service/utils/field_type_constant.dart';
import 'package:eeh/admin_service/view/layout/admin_service_layout.dart';
import 'package:eeh/attendance_complaint/view/attendance_complaint_screen.dart';
import 'package:eeh/business_card/view/business_card_view.dart';
import 'package:eeh/allowance_services/cancel_allowance/view/cancel_allowance_view.dart';
import 'package:eeh/allowance_services/extend_allowance/view/extend_allowance_view.dart';
import 'package:eeh/allowance_services/new_allowance/view/new_allowance_view_.dart';
import 'package:eeh/department_lunch/view/department_lunch_screen.dart';
import 'package:eeh/iqama_services/view/iqama_cancellation/iqama_cancellation_employee_family_screen.dart';
import 'package:eeh/iqama_services/view/iqama_cancellation/iqama_cancellation_employee_screen.dart';
import 'package:eeh/iqama_services/view/iqama_issue/iqama_issue_for_employee_screen.dart';
import 'package:eeh/iqama_services/view/iqama_printing/iqama_printing_screen.dart';
import 'package:eeh/iqama_services/view/iqama_renewal/iqama_renewal_screen.dart';
import 'package:eeh/issu_%D9%80Exit_Re_Entry_Visa/view/issueExitReEntryVisa_screen.dart';
import 'package:eeh/leave_request/view/leave_request_screen.dart';
import 'package:eeh/lms_services/views/my_learning_view.dart';
import 'package:eeh/lms_services/views/my_team_learning_view.dart';
import 'package:eeh/loan_preClosure/views/loan_preClosure_screen.dart';
import 'package:eeh/my_team_timesheet_service/presentation/my_team_timesheet_screen.dart';
import 'package:eeh/passport/presentation/screens/passport_update_request_view.dart';
import 'package:eeh/petty_cash/view/petty_cash_screen.dart';
import 'package:eeh/school_allowance/school_allowance_screen.dart';
import 'package:eeh/timesheet_service/view/screens/my_time_sheet_screen.dart';
import 'package:eeh/travel_requset/presentation/travel_request_layout.dart';
import 'package:eeh/education/update_education_screen.dart';
import 'package:flutter/material.dart';

import '../../ceo_meetings/view/ceo_meetings_screen.dart';
import '../../general_expenses_request_ui/view/general_expenses_ui.dart';
import '../../iban_number/view/ibanRequestScreen.dart';
import '../../letter_of_guarantee/view/letter_of_guarantee_screen.dart';
import '../../mazaya_request/view/mazaya_request.dart';
import '../../personal_loan/views/personal_loan_view.dart';
import '../../my_project_hours_service/view/screens/project_hours_view.dart';
import '../../salary_advance/views/salary_advance_screen.dart';
import '../../tender_request/views/tender_request_screen.dart';
import '../../visa_services/presentation/views/cancel_re_entry_visa/cancel_re_entry_visa.dart';
import '../../visa_services/presentation/views/extend_exit_re_entry_visa/extend_exit_re_entry_visa.dart';
import 'app_routes.dart';

class RouteGenerator {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.leaveRequest:
        return buildRoute(const LeavesRequestScreen(), settings: settings);
      case AppRoutes.businessCardUpdate:
        return buildRoute(const BusinessCardView(), settings: settings);
      case AppRoutes.generalExpense:
        return buildRoute(
            const ExpensesScreen(
              serviceId: '',
            ),
            settings: settings);
      case AppRoutes.iqamaPrinting:
        return buildRoute(const IqamaPrintingScreen(), settings: settings);
      case AppRoutes.iqamaRenew:
        return buildRoute(const IqamaRenewalScreen(), settings: settings);
      case AppRoutes.mazaya:
        return buildRoute(
            const MazayaRequest(
              requestID: '',
              isEditMode: false,
            ),
            settings: settings);
      case AppRoutes.passportUpdate:
        return buildRoute(PassportUpdateRequestView(), settings: settings);

      case AppRoutes.tenderRequest:
        return buildRoute(TenderRequestView(), settings: settings);
      case AppRoutes.schoolAllowance:
        return buildRoute(SchoolAllowanceScreen(), settings: settings);
      case AppRoutes.updateIBAN:
        return buildRoute(const IBANUpdate(), settings: settings);
      case AppRoutes.departmentLunch:
        return buildRoute(const DepartmentLunchScreen(), settings: settings);
      case AppRoutes.travel:
        return buildRoute(const TravelRequestLayout(), settings: settings);
      case AppRoutes.iqamaCancellationForEmployee:
        return buildRoute(const IqamaCancellationEmployeeScreen(),
            settings: settings);
      case AppRoutes.salaryadvance:
        return buildRoute(const SalaryAdvanceScreen(), settings: settings);
      case AppRoutes.loanPreClosure:
        return buildRoute(const LoanPreClosureScreen(), settings: settings);
      case AppRoutes.iqamaCancellationForEmployeeFamily:
        return buildRoute(const IqamaCancellationEmployeeFamilyScreen(),
            settings: settings);
      case AppRoutes.iqamaIssueForEmployee:
        return buildRoute(const IqamaIssueForEmployeeScreen(),
            settings: settings);
      case AppRoutes.updateEducation:
        return buildRoute(const UpdateEducationScreen(), settings: settings);
      case AppRoutes.cancelReEntryVisa:
        return buildRoute(const CancelReEntryVisaScreen(), settings: settings);
      case AppRoutes.extendReEntryVisa:
        return buildRoute(const ExtendExitReEntryVisaScreen(),
            settings: settings);
      case AppRoutes.issueExitVisa:
        return buildRoute(const IssueExitReEntryVisaScreen(),
            settings: settings);
      case AppRoutes.adminFacitily:
      case AppRoutes.letterAndCertificate:
      case AppRoutes.hRAdmin:
      case AppRoutes.supportService:
      case AppRoutes.marketing:
      case AppRoutes.cyberSecurity:
      case AppRoutes.enterpriseArchitecture:
        return buildRoute(AdminServiceLayout(tableName: getTabelName(settings)),
            settings: settings);
      case AppRoutes.projects:
        return buildRoute(const ProjectHoursView(), settings: settings);
      case AppRoutes.attendanceComplaint:
        return buildRoute(const AttendanceComplaintScreen(),
            settings: settings);
      case AppRoutes.timeSheet:
        return buildRoute(const MyTimeSheetScreen(), settings: settings);
      case AppRoutes.myTeamTimeSheet:
        return buildRoute(const MyTeamTimeSheetScreen(), settings: settings);
      case AppRoutes.personalLoan:
        return buildRoute(const PersonalLoanScreen(), settings: settings);
      case AppRoutes.pettyCash:
        return buildRoute(const PettyCashScreen(), settings: settings);
      case AppRoutes.lettersGuarantee:
        return buildRoute(const LetterOfGuaranteeScreen(), settings: settings);
      case AppRoutes.ceoMeetings:
        return buildRoute(const CEOMeetingsScreen(), settings: settings);
      case AppRoutes.newCommunication:  
        return buildRoute(const NewAllowanceView(), settings: settings);
      case AppRoutes.extendCommunication:
        return buildRoute(const ExtendAllowanceView(), settings: settings);
      case AppRoutes.cancelCommunication:
        return buildRoute(const CancelAllowanceView(), settings: settings);
      case AppRoutes.newGas:
        return buildRoute(const NewAllowanceView(isGas: true,), settings: settings);
      case AppRoutes.extendGas:
        return buildRoute(const ExtendAllowanceView(isGas: true,), settings: settings);
      case AppRoutes.cancelGas:
        return buildRoute(const CancelAllowanceView(isGas: true,), settings: settings);       
      case AppRoutes.myLearning:
        return buildRoute(const MyLearningView(), settings: settings);       
      case AppRoutes.myTeamLearning:
        return buildRoute(const MyTeamLearningView(), settings: settings);       
      default:
        return null;
    } 
  }

  static String getTabelName(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.adminFacitily:
        return TableName.esmTableName;
      case AppRoutes.letterAndCertificate:
        return TableName.lcrTableName;
      case AppRoutes.hRAdmin:
        return TableName.cSAtableName;
      case AppRoutes.supportService:
        return TableName.supTableName;
      case AppRoutes.marketing:
        return TableName.ketTableName;
      case AppRoutes.cyberSecurity:
        return TableName.cysTableName;
      case AppRoutes.enterpriseArchitecture:
        return TableName.earTableName;
      default:
        throw Exception('Invalid route name: ${settings.name}');
    }
  }

  static MaterialPageRoute buildRoute(Widget child,
      {required RouteSettings settings}) {
    return MaterialPageRoute(
        settings: settings, builder: (BuildContext context) => child);
  }

  // static Route<dynamic> _errorRoute() {
  //   return MaterialPageRoute(builder: (_) {
  //     return Scaffold(
  //       appBar: AppBar(
  //         title: const Text(
  //           'ERROR!!',
  //           style: TextStyle(
  //             color: Colors.black,
  //             fontWeight: FontWeight.bold,
  //           ),
  //         ),
  //         centerTitle: true,
  //       ),
  //       body: Center(
  //         child: SingleChildScrollView(
  //           child: Column(
  //             children: [
  //               SizedBox(
  //                 height: 100.0,
  //                 // width: 200.0,
  //                 child: Image.asset('assets/images/logo/logo.jpeg'),
  //               ),
  //               const Text(
  //                 'Seems the route you\'ve navigated to doesn\'t exist!!',
  //                 style: TextStyle(
  //                   fontSize: 16.0,
  //                   fontWeight: FontWeight.w600,
  //                 ),
  //                 textAlign: TextAlign.center,
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //     );
  //   });
  // }
}
