import 'package:eeh/ceo_meetings/bloc/ceo_meetings_bloc.dart';
import 'package:flutter/material.dart';

abstract class CEOMeetingsEvent {}

class CEOMeetingsInitialEvent extends CEOMeetingsEvent {}

class FavoriteServiceValueChangeEvent extends CEOMeetingsEvent {}

class SubmitRequestEvent extends CEOMeetingsEvent {}

class GetElmCEOMembersRequestEvent extends CEOMeetingsEvent {}

class GetDropdownListDataRequestEvent extends CEOMeetingsEvent {}

class GetEmployeesListEvent extends CEOMeetingsEvent {
  String? searchKey;
  bool? isSearchMode;
  EmployeesType employeesType;
  GetEmployeesListEvent({required this.searchKey, required this.employeesType, this.isSearchMode});
}

class GetEmployeesListNextPageEvent extends CEOMeetingsEvent {
  String? searchKey;
  bool? isSearchMode;
  EmployeesType employeesType;
  GetEmployeesListNextPageEvent({required this.searchKey, required this.employeesType, this.isSearchMode});
}

class ChangeFieldValueEvent extends CEOMeetingsEvent {}

class MoveToScreenEvent extends CEOMeetingsEvent {}

class ChangeSearchTextFieldTypeEvent extends CEOMeetingsEvent {}

class UploadAttachmentFilesEvent extends CEOMeetingsEvent {
  BuildContext context;
  UploadAttachmentFilesEvent(this.context);
}