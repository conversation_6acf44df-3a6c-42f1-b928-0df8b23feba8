class SubmitRequestResponse {
  final String? createdBy;
  final String? createdById;
  final String? createdDate;
  final int? recId;
  final Map<String, dynamic>? meetingParameters;
  final String? elmWebUrl;
  final String? requesterEmail;
  final String? requesterNameEn;
  final String? display_recId;
  final dynamic loanPreClosureA;
  final dynamic totalLoan;
  final String? requestStatus;
  final dynamic startDate;
  final String? employeeId;
  final String? rejectedByEn;
  final String? withdrawReason;
  final String? resultMessage;
  final String? reqStatusAr;
  final String? requesterNameAr;
  final String? resultMessageAr;
  final String? myTypeId;
  final dynamic reqCardData;
  final String? resultMessageEn;
  final String? notes;
  final String? exception;
  final String? serviceKey;
  final String? settlementDate;
  final String? rejectedByAr;
  final String? requestType;
  final dynamic tableName;
  final String? rejectionReason;
  final dynamic requestDetails;
  final dynamic remainingAmount;
  final String? authKey;
  final List<dynamic>? nextApprovalList;
  final dynamic code;
  final List<ApprovalProcess>? approvalProcess;
  final dynamic endDate;
  final String? approvedByEn;
  final String? taskWebUrl;
  final String? approvedByAr;
  final dynamic loanId;
  final String? requestTypeAr;
  final String? createdDateFormatted;
  final dynamic withdrawFlag;
  final String? pendingRequest;
  final String? userId;
  final dynamic paid;
  final List<dynamic>? attachments;

  SubmitRequestResponse({
    this.createdBy,
    this.createdById,
    this.createdDate,
    this.recId,
    this.meetingParameters,
    this.elmWebUrl,
    this.requesterEmail,
    this.display_recId,
    this.requesterNameEn,
    this.loanPreClosureA,
    this.totalLoan,
    this.requestStatus,
    this.startDate,
    this.employeeId,
    this.rejectedByEn,
    this.withdrawReason,
    this.resultMessage,
    this.reqStatusAr,
    this.requesterNameAr,
    this.resultMessageAr,
    this.myTypeId,
    this.reqCardData,
    this.resultMessageEn,
    this.notes,
    this.exception,
    this.serviceKey,
    this.settlementDate,
    this.rejectedByAr,
    this.requestType,
    this.tableName,
    this.rejectionReason,
    this.requestDetails,
    this.remainingAmount,
    this.authKey,
    this.nextApprovalList,
    this.code,
    this.approvalProcess,
    this.endDate,
    this.approvedByEn,
    this.taskWebUrl,
    this.approvedByAr,
    this.loanId,
    this.requestTypeAr,
    this.createdDateFormatted,
    this.withdrawFlag,
    this.pendingRequest,
    this.userId,
    this.paid,
    this.attachments,
  });

  factory SubmitRequestResponse.fromJson(Map<String, dynamic> json) {
    return SubmitRequestResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      meetingParameters: json['meetingParameters'],
      display_recId: json['display_recid'],
      elmWebUrl: json['elm_web_url'],
      requesterEmail: json['requesteremail'],
      requesterNameEn: json['requesternameen'],
      loanPreClosureA: json['loanpreclosurea'],
      totalLoan: json['totalloan'],
      requestStatus: json['requeststatus'],
      startDate: json['startdate'],
      employeeId: json['employeeid'],
      rejectedByEn: json['rejectedbyen'],
      withdrawReason: json['withdrawreason'],
      resultMessage: json['resultmessage'],
      reqStatusAr: json['req_status_ar'],
      requesterNameAr: json['requesternamear'],
      resultMessageAr: json['resultmessagear'],
      myTypeId: json['mytypeid'],
      reqCardData: json['req_card_data'],
      resultMessageEn: json['resultmessageen'],
      notes: json['notes'],
      exception: json['exception'],
      serviceKey: json['service_key'],
      settlementDate: json['settlementdate'],
      rejectedByAr: json['rejectedbyar'],
      requestType: json['request_type'],
      tableName: json['table_name'],
      rejectionReason: json['rejectionreason'],
      requestDetails: json['request_details'],
      remainingAmount: json['remainingamount'],
      authKey: json['authkey'],
      nextApprovalList: json['nextapprovallis'],
      code: json['code'],
      approvalProcess: (json['approvalproce'] as List?)
          ?.map((e) => ApprovalProcess.fromJson(e))
          .toList(),
      endDate: json['enddate'],
      approvedByEn: json['approved_by_en'],
      taskWebUrl: json['task_web_url'],
      approvedByAr: json['approved_by_ar'],
      loanId: json['loanid'],
      requestTypeAr: json['request_type_ar'],
      createdDateFormatted: json['createddate'],
      withdrawFlag: json['withdrawflag'],
      pendingRequest: json['pending_request'],
      userId: json['userid'],
      paid: json['paid'],
      attachments: json['attachments'],
    );
  }
}

class ApprovalProcess {
  final Template? template;
  final dynamic predecessors;
  final int? taskSeq;
  final dynamic newPlannedStartDate;
  final dynamic description;
  final String? taskTypeName;
  final String? plannedEndDate;
  final int? duration;
  final int? taskTypeId;
  final dynamic parentSequence;
  final int? urgency;
  final int? processId;
  final String? processName;
  final dynamic ccList;
  final dynamic dueDate;
  final dynamic actualCost;
  final int? recId;
  final dynamic msPriorityValue;
  final bool? noWait;
  final dynamic minStartDate;
  final dynamic ccListId;
  final String? plannedStartDate;
  final int? plannedEfforts;
  final List<dynamic>? assignedEmpsGrpsToTask;
  final int? primeId;
  final String? taskName;
  final int? typeId;
  final dynamic primeTypeId;
  final dynamic assignee;
  final int? objectId;

  ApprovalProcess({
    this.template,
    this.predecessors,
    this.taskSeq,
    this.newPlannedStartDate,
    this.description,
    this.taskTypeName,
    this.plannedEndDate,
    this.duration,
    this.taskTypeId,
    this.parentSequence,
    this.urgency,
    this.processId,
    this.processName,
    this.ccList,
    this.dueDate,
    this.actualCost,
    this.recId,
    this.msPriorityValue,
    this.noWait,
    this.minStartDate,
    this.ccListId,
    this.plannedStartDate,
    this.plannedEfforts,
    this.assignedEmpsGrpsToTask,
    this.primeId,
    this.taskName,
    this.typeId,
    this.primeTypeId,
    this.assignee,
    this.objectId,
  });

  factory ApprovalProcess.fromJson(Map<String, dynamic> json) {
    return ApprovalProcess(
      template: json['template'] != null
          ? Template.fromJson(json['template'])
          : null,
      predecessors: json['predecessors'],
      taskSeq: json['taskSeq'],
      newPlannedStartDate: json['new_planned_start_date'],
      description: json['description'],
      taskTypeName: json['taskTypeName'],
      plannedEndDate: json['plannedEndDate'],
      duration: json['duration'],
      taskTypeId: json['taskTypeId'],
      parentSequence: json['parentSequence'],
      urgency: json['urgency'],
      processId: json['processId'],
      processName: json['processName'],
      ccList: json['ccList'],
      dueDate: json['due_Date'],
      actualCost: json['actual_Cost'],
      recId: json['recId'],
      msPriorityValue: json['msPriorityValue'],
      noWait: json['noWait'],
      minStartDate: json['min_Start_Date'],
      ccListId: json['ccListID'],
      plannedStartDate: json['plannedStartDate'],
      plannedEfforts: json['plannedEfforts'],
      assignedEmpsGrpsToTask: json['assignedEmpsGrpsToTask'],
      primeId: json['PRIME_ID'],
      taskName: json['taskName'],
      typeId: json['typeId'],
      primeTypeId: json['PRIME_TYPE_ID'],
      assignee: json['assignee'],
      objectId: json['objectid'],
    );
  }
}

class Template {
  final int? createdById;
  final String? createdBy;
  final int? createdDate;
  final String? companyName;
  final String? uuid;
  final int? recId;
  final String? templateName;
  final int? typeId;

  Template({
    this.createdById,
    this.createdBy,
    this.createdDate,
    this.companyName,
    this.uuid,
    this.recId,
    this.templateName,
    this.typeId,
  });

  factory Template.fromJson(Map<String, dynamic> json) {
    return Template(
      createdById: json['createdById'],
      createdBy: json['createdBy'],
      createdDate: json['createdDate'],
      companyName: json['companyName'],
      uuid: json['uuid'],
      recId: json['recId'],
      templateName: json['templateName'],
      typeId: json['typeId'],
    );
  }
}
