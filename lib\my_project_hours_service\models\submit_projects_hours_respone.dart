class SubmitProjectsHoursResponse {
  String? createdBy;
  String? createdById;
  String? createdDate;
  int? recId;
  String? startdate;
  List<ProjectSummary>? projectSummary;
  String? weekRecid;
  String? endDate;
  String? employeeId;

  SubmitProjectsHoursResponse(
      {this.createdBy,
        this.createdById,
        this.createdDate,
        this.recId,
        this.startdate,
        this.projectSummary,
        this.weekRecid,
        this.endDate,
        this.employeeId});

  SubmitProjectsHoursResponse.fromJson(Map<String, dynamic> json) {
    createdBy = json['createdBy'];
    createdById = json['createdById'];
    createdDate = json['createdDate'];
    recId = json['recId'];
    startdate = json['startdate'];
    if (json['project_summary'] != null) {
      projectSummary = <ProjectSummary>[];
      json['project_summary'].forEach((v) {
        projectSummary!.add(new ProjectSummary.fromJson(v));
      });
    }
    weekRecid = json['week_recid'];
    endDate = json['end_date'];
    employeeId = json['employee_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdBy'] = this.createdBy;
    data['createdById'] = this.createdById;
    data['createdDate'] = this.createdDate;
    data['recId'] = this.recId;
    data['startdate'] = this.startdate;
    if (this.projectSummary != null) {
      data['project_summary'] =
          this.projectSummary!.map((v) => v.toJson()).toList();
    }
    data['week_recid'] = this.weekRecid;
    data['end_date'] = this.endDate;
    data['employee_id'] = this.employeeId;
    return data;
  }
}

class ProjectSummary {
  String? descriptionen;
  String? loggedhours;
  String? projectownerid;
  String? eligiblehours;
  String? wbscode;
  String? descriptionar;
  int? recid;

  ProjectSummary(
      {this.descriptionen,
        this.loggedhours,
        this.projectownerid,
        this.eligiblehours,
        this.wbscode,
        this.descriptionar,
        this.recid});

  ProjectSummary.fromJson(Map<String, dynamic> json) {
    descriptionen = json['descriptionen'];
    loggedhours = json['loggedhours'];
    projectownerid = json['projectownerid'];
    eligiblehours = json['eligiblehours'];
    wbscode = json['wbscode'];
    descriptionar = json['descriptionar'];
    recid = json['recid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['descriptionen'] = this.descriptionen;
    data['loggedhours'] = this.loggedhours;
    data['projectownerid'] = this.projectownerid;
    data['eligiblehours'] = this.eligiblehours;
    data['wbscode'] = this.wbscode;
    data['descriptionar'] = this.descriptionar;
    data['recid'] = this.recid;
    return data;
  }
}
