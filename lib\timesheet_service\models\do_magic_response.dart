class DoMagicResponse {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final dynamic coveredHours;
  final List<OverlappedDay> overlappedDays;

  DoMagicResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    this.coveredHours,
    required this.overlappedDays,
  });

  factory DoMagicResponse.fromJson(Map<String, dynamic> json) {
    return DoMagicResponse(
      createdBy: json['createdBy'],
      createdById: json['createdById'],
      createdDate: json['createdDate'],
      recId: json['recId'],
      coveredHours: json['covered_hours'],
      overlappedDays: (json['overlapped_days'] as List)
          .map((e) => OverlappedDay.fromJson(e))
          .toList(),
    );
  }
}

class OverlappedDay {
  final String overlappedDays;
  final int recid;

  OverlappedDay({
    required this.overlappedDays,
    required this.recid,
  });

  factory OverlappedDay.fromJson(Map<String, dynamic> json) {
    return OverlappedDay(
      overlappedDays: json['overlapped_days'],
      recid: json['recid'],
    );
  }
}
