import 'package:eeh/admin_service/bloc/admin_bloc.dart';
import 'package:eeh/admin_service/bloc/admin_event.dart';
import 'package:eeh/admin_service/bloc/admin_state.dart';
import 'package:eeh/admin_service/utils/field_type_constant.dart';
import 'package:eeh/admin_service/utils/dynamic_screen_utils.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ServiceDataView extends StatefulWidget {
 final AdminBloc bloc;
const ServiceDataView({super.key,required this.bloc});

  @override
  State<ServiceDataView> createState() => _ServiceDataViewState();
}

class _ServiceDataViewState extends State<ServiceDataView> {
  String selectedService = '';
  DynamicScreenUtils dynamicScreenUtils =DynamicScreenUtils(); 
   
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AdminBloc, AdminServiceState>( 
      bloc: widget.bloc,
      listenWhen: (previous, current) => current is EsmServiceDetailsSuccessState,
      listener: (context, state) {},
      buildWhen: (previous, current) => 
      current is EsmServiceDetailsSuccessState||
      current is ChangeSwitchState||
      current is FilesUpdatedState,
      builder: (context, state) {
        if(state is FilesUpdatedState||state is ChangeSwitchState){
          widget.bloc.add(ChangeSubmitColorEvent());
        }
        selectedService =  (AppLocalizations.appLang =='en'? widget.bloc.selectedEsmService?.nameen :  widget.bloc.selectedEsmService?.namear)??'';
       if( state is EsmServiceDetailsLoadingState){
        return const Center(child: CircularProgressIndicator(),);
       }else{
        return getBody();
       }
      },
    );
  }

 getBody() {
    return SizedBox(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: lightGreyColor,
              borderRadius: BorderRadius.circular(9),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width: 250.w,
                  child: Text(
                    '${AppLocalizations.of(context).translate('Service')} : '
                    '$selectedService',
                    style:  FontUtilities.getTextStyle(TextType.medium,
                      size:12.sp,fontWeight:FontWeight.w400),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10.h,),
          Form(
            onChanged: (){
              widget.bloc.add(ChangeSubmitColorEvent());
            },
            key: widget.bloc.dynamicFormKey,
            child: SizedBox(
              height:MediaQuery.of(context).size.height- 400.h,
              child: ListView.builder(
                addAutomaticKeepAlives: true,
                physics: const BouncingScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: (context, index) => buildWidget(index),
                itemCount: widget.bloc.genericServiceDetailsResponse?.serviceMetadat
                    ?.serviceMetadata?.length??0,
              ),
            ),
          ),
        ],
      ),
    );
  }


Widget buildWidget(int index) {
    final item = widget.bloc.genericServiceDetailsResponse?.serviceMetadat?.serviceMetadata?[index];
    switch (item?.filedType) {
      case FieldType.textfield:
        return dynamicScreenUtils.getTextField(item, context,
          (v) {widget.bloc.updateBlocData(item,v);});

      case FieldType.note:
        return dynamicScreenUtils.getNote(item, context,
          (v) {widget.bloc.updateBlocData(item,v);});

      case FieldType.numeric:
      case FieldType.number:
        return dynamicScreenUtils.getNumircTextField(item, context,
          (v) {widget.bloc.updateBlocData(item,v);});
      
      case FieldType.currency:
        return dynamicScreenUtils.getCurrencyTextField(item, context,
          (v) {widget.bloc.updateBlocData(item,v);});

      case FieldType.dropDown:
        return dynamicScreenUtils.getDropDown(item,context,
          (selectedValue){widget.bloc.updateDropDownBlocData(item,selectedValue);},widget.bloc.tableName??'');

      case FieldType.attchment:
        return dynamicScreenUtils.uplodeFileWidget(context, 
          () => widget.bloc.add(FilesUpdatedEvent()),item);

      case FieldType.date:
        return dynamicScreenUtils.getDateWidget(item,
          (v) {widget.bloc.updateBlocData(item,v);},context);

      case FieldType.time:
        return dynamicScreenUtils.getTimeWidget(item,
          (v) {widget.bloc.updateBlocData(item,v);},context);

      case FieldType.template:
        return dynamicScreenUtils.getAttachedFiles(
            item, widget.bloc.genericServiceDetailsResponse?.esmAttachments ?? []);
      
      case FieldType.instruction:
        return dynamicScreenUtils.getInstruction(item);

      case FieldType.toggle:
        return dynamicScreenUtils.getSwitch(widget.bloc.switchValue??false,
          (v) {widget.bloc.add(ChangeSwitchEvent(item: item, value: v));},item);

      case FieldType.terms:
       widget.bloc.isTermsMandatory = item?.mandatory??false;
        return dynamicScreenUtils.getTermsConditions(widget.bloc.termSwitchValue??false,
          (v) {widget.bloc.add(ChangeSwitchEvent(item: item, value: v));}, item);
      default:
        return SizedBox();
    }
  }
}
