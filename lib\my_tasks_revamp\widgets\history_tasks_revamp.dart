import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/utility/methods.dart';
import 'package:eeh/shared/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../shared/app_constants.dart';
import '../../shared/network/api_helper.dart';
import '../../shared/styles/colors.dart';
import '../../shared/work_widget/models/work_model.dart';
import '../../shared/work_widget/work_item_layout.dart';
import '../bloc/my_task_bloc_revamp.dart';
import '../bloc/tasks_events.dart';
import '../bloc/tasks_states.dart';

class HistoryTaskViewRevamp extends StatefulWidget {
  const HistoryTaskViewRevamp({super.key});

  @override
  State<HistoryTaskViewRevamp> createState() => _HistoryTaskViewRevampState();
}

class _HistoryTaskViewRevampState extends State<HistoryTaskViewRevamp> {
  @override
  final ScrollController _scrollController = ScrollController();
  bool _isLastItemVisible = false;
  String searchText = '';
  SortType sortType = SortType.desc;

  @override
  void initState() {
    super.initState();
    TasksBlocRevamp.instance(context).sortType = SortType.desc;
    sortType = TasksBlocRevamp.instance(context).sortType;
    searchText = TasksBlocRevamp.instance(context).searchController.text;
    TasksBlocRevamp.instance(context).filteredHistoryServices?.clear();
    TasksBlocRevamp.instance(context).clearPageNumber();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent) {
        setState(() {
          _isLastItemVisible = true;
        });
      } else {
        setState(() {
          _isLastItemVisible = false;
        });
      }
    });
  }
  @override
  void dispose() {
    _scrollController.dispose();
   // TasksBlocRevamp.instance(context).clearPageNumber();
    super.dispose();
  }

  Future<void> _handleRefresh() async {
    TasksBlocRevamp.instance(context).clearPageNumber();
    TasksBlocRevamp.instance(context).add(HistoryTasksEvent(searchKey: '', sortType: TasksBlocRevamp.instance(context).sortType.name));
  }
  void _scrollDown() {
    _scrollController.animateTo(
      _scrollController.position.extentBefore + 4,
      duration: Duration(seconds: 10),
      curve: Curves.fastOutSlowIn,
    );
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
        onRefresh: _handleRefresh,
        child: BlocBuilder<TasksBlocRevamp, TasksState>(
            buildWhen: (current, _) => (current is HistoryTasksLoadingState ||
                current is HistoryTasksSuccessState ||
                current is HistoryTasksLoadingState ||
                current is SearchInTasksLoadingState ||
                current is SearchInTasksSuccessState ||
                current is SearchInTasksErrorState ||
                current is TaskActionLoadingState ||
                current is TaskActionSuccessState ||
                current is TasksInitialState ||
                current is SubmitSuccessState ||
                current is  SubmitErrorState ||
                current is TaskActionErrorState ||
                current is CheckInCheckOutSuccessState||
                current is CheckInCheckOutErrorState||
                current is SubmitTemplatesSuccessState||
                current is SubmitTemplatesErrorState||
                current is HistoryTasksErrorState),
            builder: (context, state) {
              return (state is HistoryTasksLoadingState ||
                      state is TasksInitialState ||
                      state is SearchInTasksLoadingState ||
                      state is TaskActionLoadingState)
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : (state is HistoryTasksErrorState ||
                          state is SearchInTasksErrorState ||
                          state is TaskActionErrorState)
                      ? const SizedBox()
                      : ((TasksBlocRevamp.instance(context)
                                      .filteredHistoryServices
                                      ?.length ??
                                  0) >
                              0
                          ? Column(
                            children: [
                              Expanded(
                                child: ListView.separated(
                                  controller: _scrollController,
                                  padding: EdgeInsets.symmetric(horizontal: 8),
                                    itemCount: TasksBlocRevamp.instance(context)
                                            .filteredHistoryServices
                                            ?.length ??
                                        0,
                                    separatorBuilder: (context, index) {
                                      return SizedBox(
                                        height: 10.h,
                                      );
                                    },
                                    itemBuilder: (context, index) {
                                      if (index <=
                                          (TasksBlocRevamp.instance(context)
                                                  .filteredHistoryServices
                                                  ?.length ??
                                              0)) {
                                        final task = TasksBlocRevamp.instance(context)
                                            .filteredHistoryServices?[index];
                                        return WorkItemLayout(
                                          workModel: WorkModel(
                                            tableName: task?.tableName,
                                            discussionRequestId:
                                                task?.requestid?.toString() ?? '',
                                            workType: WorkType.myTasks,
                                            requestID:
                                                task?.requestid?.toString() ?? '',
                                            workItems:
                                                task?.serviceCardListItems ?? [],
                                            actionList: task?.actionList ?? '',
                                            serviceStatusAR: (task?.statusGroupAr??'').isNotEmpty?task?.statusGroupAr: task?.requeststatusAr,
                                            serviceStatusEn: (task?.statusGroupEn??'').isNotEmpty?task?.statusGroupEn: task?.requeststatus,
                                            createdDate: task?.serviceDate,
                                            priorityColor: task?.priorityColor??'',
                                            cardImage:
                                                "$baseURL${task?.requesterimage}",
                                            mutableContext: task?.mutableContext,
                                            nextApprovalEmail: task?.nextapprovalmail,
                                            actionListItems: task?.actionListItems??[],
                                            slaAr: task?.slaAr??'',
                                            slaEn: task?.slaEn??'',
                                            nextApprovalNameAr:
                                                task?.nextapprovalnameAr,
                                            nextApprovalNameEn:
                                                task?.nextapprovalname,
                                            employeeid: task?.nextAprEmpID,
                                            serviceRate: task?.serviceRate??'',
                                            requesterNameAr: task?.requesterNameAr,
                                            requesterNameEn: task?.requesterNameEn,
                                            requesterEmail: task?.requesterEmail,
                                            requestType: task?.requestType,
                                            statusColor: task?.statusColor,
                                            internalDiscussionFlag: task?.internalDiscussionFlag,
                                          ),
                                        );
                                      } else {
                                        return SizedBox.shrink();
                                      }
                                    },
                                  ),
                              ),
                              BlocConsumer<TasksBlocRevamp, TasksState>(
                                  listener: (context, state) => (),
                                  listenWhen: (previous, current) =>
                                  current is PendingTasksSuccessState,
                                  buildWhen: (previous, current) =>
                                  current is SubmitLoadingState ||
                                      current is SubmitSuccessState ||
                                      current is SubmitErrorState,
                                  builder: (context, state) => Column(
                                    children: [
                                      Center(
                                        child: state is SubmitLoadingState
                                            ? Padding(
                                          padding:
                                          const EdgeInsets.only(
                                              bottom: 8.0),
                                          child:
                                          const CircularProgressIndicator(),
                                        )
                                            : _isLastItemVisible
                                            ? Column(
                                          children: [
                                            !TasksBlocRevamp.instance(
                                                context)
                                                .lastPage
                                                ? MaterialButton(
                                              onPressed:
                                                  () {
                                                TasksBlocRevamp.instance(
                                                    context)
                                                    .incrementPageNumber();
                                                TasksBlocRevamp.instance(
                                                    context)
                                                    .add(
                                                    HistoryTasksEvent(searchKey: searchText, sortType: TasksBlocRevamp.instance(context).sortType.name));
                                                _scrollDown();
                                              },
                                              color:
                                              primaryColor,
                                              textColor:
                                              Colors
                                                  .white,
                                              padding:
                                              EdgeInsets.all(
                                                  10),
                                              shape:
                                              CircleBorder(),
                                              child: Icon(
                                                Icons
                                                    .arrow_downward_sharp,
                                                size: 24,
                                              ),
                                            )
                                                : SizedBox(),
                                            SizedBox(
                                              height: 6,
                                            )
                                          ],
                                        )
                                            : SizedBox(),
                                      ),
                                    ],
                                  )),

                              // _isLastItemVisible? Column(
                              //   children: [
                              //     !TasksBlocRevamp.instance(context).lastPage ? MaterialButton(
                              //       onPressed: () {
                              //         TasksBlocRevamp.instance(context).incrementPageNumber();
                              //         TasksBlocRevamp.instance(context).add(HistoryTasksEvent());
                              //         _scrollDown();
                              //       },
                              //       color: primaryColor,
                              //       textColor: Colors.white,
                              //       child: Icon(
                              //         Icons.arrow_downward_sharp,
                              //         size: 24,
                              //       ),
                              //       padding: EdgeInsets.all(10),
                              //       shape: CircleBorder(),
                              //     ):SizedBox(),
                              //     SizedBox(height: 6)
                              //   ],
                              // ):SizedBox(),

                            ],
                          )
                          : state is HistoryTasksSuccessState ||
                  state is SubmitSuccessState
                              ? getEmptyListView(
                                  context,
                                  TasksBlocRevamp.instance(context)
                                      .searchController
                                      .text
                                      .isNotEmpty)
                              : Center(
                                  child: CircularProgressIndicator(),
                                ));
            }));
  }
}
