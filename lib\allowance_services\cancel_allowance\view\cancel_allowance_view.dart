import 'package:eeh/allowance_services/cancel_allowance/bloc/cancel_allowance_bloc.dart';
import 'package:eeh/allowance_services/cancel_allowance/bloc/cancel_allowance_event.dart';
import 'package:eeh/allowance_services/cancel_allowance/bloc/cancel_allowance_state.dart';
import 'package:eeh/allowance_services/utils.dart';
import 'package:eeh/allowance_services/widget/allowance_card.dart';
import 'package:eeh/allowance_services/widget/success_screen_content.dart';
import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_event.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/cancel_button_component.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CancelAllowanceView extends StatefulWidget {
  final bool isGas;
  const CancelAllowanceView({super.key, this.isGas = false});

  @override
  State<CancelAllowanceView> createState() => _CancelAllowanceViewState();
}

class _CancelAllowanceViewState extends State<CancelAllowanceView> {
  late CancelAllowanceBloc bloc;
  AllowanceUtils utils = AllowanceUtils();

  @override
  void dispose() {
    bloc.commentController.dispose();
    bloc.searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CancelAllowanceBloc>(
          create: (context) => CancelAllowanceBloc()
            ..add(CancelAllowanceInitialEvent(widget.isGas)),
        ),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
      ],
      child: Scaffold(
        appBar: getAppBarWidget(),
        body: BlocConsumer<CancelAllowanceBloc, CancelAllowanceState>(
          listener: (context, state) {
            bloc.add(EmployeeAllowanceEvent());
          },
          listenWhen: (previous, current) =>
              current is CancelAllowanceInitialState,
          buildWhen: (previous, current) =>
              current is CancelAllowanceInitialState ||
              current is EmployeeAllowanceSuccessState||
              current is SearchEmployeeState,
          builder: (context, state) {
            bloc = context.read<CancelAllowanceBloc>();
            if (state is CancelAllowanceInitialState ||
                state is EmployeeAllowanceLoadingState) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else {
              return getBody();
            }
          },
        ),
      ),
    );
  }

  PreferredSize getAppBarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: widget.isGas
          ? FavoriteComponent(
              title: AppLocalizations.of(context)
                  .translate('Cancel Gas Allowance'),
              id: '44',
            )
          : FavoriteComponent(
              title: AppLocalizations.of(context)
                  .translate('Cancel Communication Allowance'),
              id: '43',
            ),
    );
  }

  getBody() {
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: Stack(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height - 230.h,
            child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  utils.searchTextField(bloc.searchController, context,
                      (v) => bloc.add(SearchEmployeeEvent(v))),
                  utils.getEmplyeeAllowanceNumber(
                      context, bloc.allowanceListResponse?.employees?.length),
                  utils.emptyView(bloc.filterList.isEmpty, context,bloc.searchController.text.trim().isNotEmpty), 
                  if(bloc.filterList.isNotEmpty)   
                  getSelectAllWidget(),
                  getTextCancelAllowance(),
                  getAllowanceCardWidget(),
                  if(bloc.filterList.isNotEmpty)
                  utils.getCommentsWidget(context, bloc.commentController),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: getSubmitButton(),
          ),
        ],
      ),
    );
  }

  getAllowanceCardWidget() {
    return BlocBuilder<CancelAllowanceBloc, CancelAllowanceState>(
        buildWhen: (previous, current) =>
            current is SelectAllItemState ||
            current is CheckItemState ||
            current is SearchEmployeeState,
        builder: (context, state) {
          final item = bloc.filterList;
          return Padding(
            padding: const EdgeInsets.all(15.0),
            child: SizedBox(
              width: double.infinity,
              height: ((item.length) > 1)
                  ? 400.h
                  : (item.isEmpty)
                      ? 0.h
                      : 250.h,
              child: ListView.builder(
                  itemBuilder: (context, index) => AllowanceCard(
                        actionType: ActionType.check,
                        employeeEn: item[index].employeenameen ?? '',
                        employeeAr: item[index].employeenamear ?? '',
                        numOfMonths: item[index].numberofmonths.toString(),
                        targetAmount: item[index].eligibleamount.toString(),
                        startDate: item[index].startdate ?? '',
                        endDate: item[index].enddate ?? '',
                        comment: item[index].comment ?? '',
                        isCheck: item[index].isChecked,
                        onCheckBoxChanged: (value) {
                          bloc.add(CheckItemEvent(index: index, value: value));
                        },
                      ),
                  itemCount: item.length),
            ),
          );
        });
  }

  getSelectAllWidget() {
    return BlocBuilder<CancelAllowanceBloc, CancelAllowanceState>(
      buildWhen: (previous, current) =>
          current is SelectAllItemState ||
          current is CheckItemState ||
          current is SearchEmployeeState,
      builder: (context, state) => Row(
        children: [
          Checkbox(
            checkColor: primaryColor,
            fillColor: WidgetStateProperty.resolveWith(
              (states) => Colors.transparent,
            ),
            side: WidgetStateBorderSide.resolveWith(
              (states) => states.contains(WidgetState.selected)
                  ? BorderSide(
                      width: 1.8,
                      color: primaryColor,
                      strokeAlign: 0.1,
                    )
                  : BorderSide(width: 2.0, color: Colors.black),
            ),
            value: bloc.isSelectAll,
            onChanged: (newValue) {
              bloc.add(SelectAllItemEvent(isSelected: newValue ?? false));
            },
          ),
          Text(
            AppLocalizations.of(context).translate('Select All'),
            style: FontUtilities.getTextStyle(
              TextType.regular,
              size: 13.sp,
            ),
          ),
        ],
      ),
    );
  }

  getTextCancelAllowance() {
    return BlocBuilder<CancelAllowanceBloc, CancelAllowanceState>(
      buildWhen: (previous, current) =>
          current is SelectAllItemState || current is CheckItemState,
      builder: (context, state) => Visibility(
        visible: bloc.checkedList.isNotEmpty,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 15.0,
          ),
          child: Row(
            spacing: 4.w,
            children: [
              Text(
                (bloc.checkedList.length.toString()) +
                    AppLocalizations.of(context)
                        .translate('employees selected'),
                style: FontUtilities.getTextStyle(
                  TextType.regular,
                  size: 13.sp,
                  textColor: primaryColor,
                ),
              ),
              Text(
                  AppLocalizations.of(context)
                      .translate('to cancel their allowances.'),
                  style: FontUtilities.getTextStyle(
                    TextType.regular,
                    size: 13.sp,
                  )),
            ],
          ),
        ),
      ),
    );
  }

  Widget getSubmitButton() {
    return BlocConsumer<CancelAllowanceBloc, CancelAllowanceState>(
        listenWhen: (previous, current) =>
            current is SubmitAllowanceSuccessState,
        listener: (context, state) {
          if (state is SubmitAllowanceSuccessState) {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => SuccessScreen(
                          title: AppLocalizations.of(context)
                              .translate("Request Submitted Successfully"),
                          contentWidget: SuccessScreenContentWidget(
                            submitResponse: bloc.submitResponse,
                          ),
                        )));
          }
        },
        buildWhen: (previous, current) =>
            current is SubmitAllowanceLoadingState ||
            current is SubmitAllowanceErrorState ||
            current is SubmitAllowanceSuccessState ||
            current is SelectAllItemState ||
            current is CheckItemState,
        builder: (context, state) {
          return Column(
            children: [
              CancelButtonComponent(),
              SubmitButtonComponent(
                text: AppLocalizations.of(context).translate('Submit'),
                onPressed: () {
                  if (bloc.checkedList.isNotEmpty) {
                    bloc.add(SubmitAllowanceEvent());
                  }
                },
                backGroung: (bloc.checkedList.isNotEmpty)
                    ? primaryColor
                    : primaryColor.withOpacity(0.2),
                isLoading: state is SubmitAllowanceLoadingState,
              ),
            ],
          );
        });
  }
}
