import 'package:eeh/l10n/app_localizations.dart';
import 'package:eeh/petty_cash/bloc/petty_cash_bloc.dart';
import 'package:eeh/petty_cash/bloc/petty_cash_events.dart';
import 'package:eeh/petty_cash/bloc/petty_cash_states.dart';
import 'package:eeh/petty_cash/repo/petty_cash_repo.dart';
import 'package:eeh/petty_cash/view/widgets/petty_cash_success_screen_content.dart';
import 'package:eeh/petty_cash/view/widgets/petty_cash_terms_widget.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_bloc.dart';
import 'package:eeh/shared/attachment_uda/bloc/attach_event.dart';
import 'package:eeh/shared/attachment_uda/widget/attachment_component.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_bloc.dart';
import 'package:eeh/shared/favorite_component/bloc/favorite_event.dart';
import 'package:eeh/shared/favorite_component/view/favorite_component.dart';
import 'package:eeh/shared/styles/colors.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/utility/navigation_utility.dart';
import 'package:eeh/shared/widgets/currency_field_component.dart';
import 'package:eeh/shared/widgets/important_note.dart';
import 'package:eeh/shared/widgets/new_custom_note_widget.dart';
import 'package:eeh/shared/widgets/new_textformfield_component.dart';
import 'package:eeh/shared/widgets/submit_button_component.dart';
import 'package:eeh/shared/widgets/validator_component.dart';
import 'package:eeh/success_view/success_screen_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class PettyCashScreen extends StatefulWidget {
  const PettyCashScreen({super.key});

  @override
  State<PettyCashScreen> createState() => _PettyCashScreenState();
}

class _PettyCashScreenState extends State<PettyCashScreen> {
  late PettyCashRequestBloc _bloc;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<PettyCashRequestBloc>(
          create: (context) =>
              PettyCashRequestBloc(pettyCashRepo: PettyCashRequestRepo.instance)
                ..add(PettyCashInitialEvent()),
        ),
        BlocProvider<FavoriteBloc>(
          create: (context) => FavoriteBloc()..add(FavoriteInitialEvent()),
        ),
        BlocProvider<AttachBloc>(
          create: (BuildContext context) {
            return AttachBloc()..add(AttachInitialEvent());
          },
        )
      ],
      child: Scaffold(
          appBar: _getAppbarWidget(),
          body: BlocBuilder<PettyCashRequestBloc, PettyCashRequestState>(
              buildWhen: (previous, current) =>
                  current is PettyCashInitialSuccessState ||
                  current is PettyCashInitialErrorState,
              builder: (context, state) {
                _bloc = context.read<PettyCashRequestBloc>();

                if (state is PettyCashInitialState) {
                  return Center(child: CircularProgressIndicator());
                } else if (state is PettyCashInitialErrorState) {
                  return SizedBox.shrink();
                } else {
                  return SizedBox(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    child: Stack(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height - 190.h,
                          child: SingleChildScrollView(
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 17.0.w),
                              child: _getBody(),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: _getButtons(),
                        ),
                      ],
                    ),
                  );
                }
              })),
    );
  }

  Widget _getBody() {
    return Column(
      children: [
        SizedBox(
          height: 22.h,
        ),
        NewCustomTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("petty_cash_usage"),
          isMandatory: true,
          bottomSheetBody: _getDraggableWidget(
            height: 0.5.h,
            title: AppLocalizations.of(context).translate("petty_cash_usage"),
            bottomSheetBody: _getBottomsheetBody(
              items: _bloc.usageItems
                  .map(
                    (e) => AppLocalizations.appLang == 'en'
                        ? e.cashUsageNameEn ?? ''
                        : e.cashUsageNameAr ?? '',
                  )
                  .toList(),
              onTap: (index, value) => _bloc.onUsageValueSelected(index),
            ),
          ),
          controller: _bloc.cashUsageController,
          type: TextFieldType.bottomSheet,
        ),
        NewCustomTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("budget_type"),
          isMandatory: true,
          controller: _bloc.budgetTypeController,
          bottomSheetBody: _getDraggableWidget(
            height: 0.2.h,
            title: AppLocalizations.of(context).translate("budget_type"),
            bottomSheetBody: _getBottomsheetBody(
              items: [
                AppLocalizations.of(context).translate("department"),
                AppLocalizations.of(context).translate("Project WBS")
              ],
              onTap: (index, value) {
                _bloc.budgetTypeController.text = value;
                _bloc.projectController.text = '';
                _bloc.add(ChangeBudgetTypeEvent());
              },
            ),
          ),
          type: TextFieldType.bottomSheet,
        ),
        getProjectTextFieldWidget(),
        CurrencyTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("cash_amount"),
          isMandatory: true,
          isSARComponentHasBrackets: true,
          controller: _bloc.cashAmountController,
          sARComponentSize: 14.sp,
          sarColor: secondTextColor,
          onChange: (p0) => _bloc.add(UpdatefieldValueEvent()),
        ),
        NewCustomTextFieldComponent(
            labelText:
                AppLocalizations.of(context).translate("expected_closing_date"),
            isMandatory: true,
            controller: _bloc.closingDateController,
            dateRangePickerController: _bloc.dateRangePickerController,
            type: TextFieldType.calendar,
            minTime: DateTime.now(),
            onDateChange: () {
              _bloc.closingDateController.text = DateFormat('yyyy-MM-dd')
                  .format(_bloc.dateRangePickerController.selectedDate!);
                  _bloc.add(UpdatefieldValueEvent());
            }),
        NewCustomTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("iban_number"),
          isMandatory: true,
          isDemmed: true,
          controller: _bloc.ibanNumberController,
          type: TextFieldType.normal,
        ),
        NewCustomTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("bank_name"),
          isMandatory: true,
          isDemmed: true,
          controller: _bloc.bankNameController,
          type: TextFieldType.normal,
        ),
        NewCustomTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("cost center"),
          isMandatory: true,
          isDemmed: true,
          controller: _bloc.costCenterController,
          type: TextFieldType.normal,
        ),
        SizedBox(
          height: 22.h,
        ),
        CustomNoteWidget(
          labelText: AppLocalizations.of(context).translate("Purpose"),
          hintText: AppLocalizations.of(context).translate("wrtie_purpose"),
          isMandatory: true,
          noteController: _bloc.purposeController,
          onChange: (p0) => _bloc.add(UpdatefieldValueEvent()),
        ),
        SizedBox(
          height: 22.h,
        ),
        _getAttachmentWidget(),
        SizedBox(
          height: 22.h,
        ),
        _getValidator(context),
        SizedBox(
          height: 22.h,
        ),
        _getImportantNotes(context)
      ],
    );
  }

  Widget getProjectTextFieldWidget() {
    return BlocBuilder<PettyCashRequestBloc, PettyCashRequestState>(
      buildWhen: (previous, current) =>
          current is ProjectWbsListLoadingState ||
          current is ProjectWbsListErrorState ||
          current is ProjectWbsListSuccessState ||
          current is ChangeBudgetTypeState,
      builder: (context, state) => Visibility(
        visible: _bloc.budgetTypeController.text ==
            AppLocalizations.of(context).translate("Project WBS"),
        child: NewCustomTextFieldComponent(
          labelText: AppLocalizations.of(context).translate("Project"),
          bottomSheetHasSearch: true,
          controller: _bloc.projectController,
          keyboardType: TextInputType.none,
          isReadOnly: true,
          isMandatory: true,
          type: TextFieldType.bottomSheet,
          bottomSheetHeight: 0.85,
          isLoading: state is ProjectWbsListLoadingState,
          bottomSheetText: AppLocalizations.of(context).translate("Project"),
          bottomSheetContentList: state is ProjectWbsListSuccessState
              ? state.projectWpsResponse.projectWps
              : [],
          onSelect: (v) {
            _bloc.selectedProject = v;
            _bloc.add(UpdatefieldValueEvent());
          },
        ),
      ),
    );
  }

  Widget _getAttachmentWidget() {
    return Padding(
      padding: EdgeInsets.only(bottom: 5.sp),
      child: AttachUI(
        isRequired: false,
        label: AppLocalizations.of(context).translate('File'),
        endPoint: '',
        serviceType: 'PermanentPettyCashRequest',
        subTitle: AppLocalizations.of(context).translate('ClickToUpload'),
        onFilesUpdated: () {
          _bloc.add(FilesUpdatedEvent());
        },
        color: Color(0xffCB2027),
      ),
    );
  }

  Widget _getValidator(BuildContext context) {
    return BlocBuilder<PettyCashRequestBloc, PettyCashRequestState>(
      buildWhen: (previous, current) => current is ChangeValidatorValueState,
      builder: (context, state) => ValidatorComponent(
        isMandatory: true,
        onChange: (value) {
          _bloc.add(ChangeValidatorValueEvent(value: value));
        },
        value: _bloc.isPolicyAccepted,
        preText: AppLocalizations.of(context).translate("I read and accept"),
        postText: AppLocalizations.of(context).translate("petty_cash_policy"),
        postTextCallback: () {
          getTermsAndConditionsBottomSheet(context);
        },
      ),
    );
  }

  Widget _getImportantNotes(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 18.0.sp),
      child: ImportantNote(
        title: AppLocalizations.of(context).translate("Important Notes:"),
        notes: [
          AppLocalizations.of(context).translate("petty_cash_note"),
        ],
      ),
    );
  }

  getTermsAndConditionsBottomSheet(BuildContext context) {
    return showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
      builder: (bottomSheetContext) => DraggableScrollableSheet(
          initialChildSize: 0.85,
          maxChildSize: 1,
          minChildSize: 0.5,
          expand: false,
          builder: (context, scrollController) => PettyCashTermsWidget()),
    );
  }

  Widget _getBottomsheetBody(
      {required List<String> items, required Function(int, dynamic) onTap}) {
    return Expanded(
      child: Column(
        children: [
          SizedBox(
            height: 8.h,
          ),
          Expanded(
            child: ListView.builder(
                itemBuilder: (context, index) {
                  return ListTile(
                    onTap: () {
                      onTap(index, items[index]);
                      Navigation.popScreen(context);
                    },
                    title: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 25.0.w),
                      child: Text(
                        items[index],
                        style: FontUtilities.getTextStyle(
                          TextType.regular,
                          size: 14.sp,
                        ),
                      ),
                    ),
                  );
                },
                itemCount: items.length),
          )
        ],
      ),
    );
  }

  Widget _getDraggableWidget(
      {required Widget bottomSheetBody,
      required double height,
      required String title}) {
    return DraggableScrollableSheet(
        initialChildSize: height,
        maxChildSize: 1,
        minChildSize: 0.1,
        expand: false,
        builder: (context, scrollController) => Column(
              children: [
                Center(
                  child: Text(
                    title,
                    style: FontUtilities.getTextStyle(
                      TextType.medium,
                      size: 16.sp,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                bottomSheetBody,
              ],
            ));
  }

  Widget _getButtons() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // CancelButtonComponent(),
          BlocConsumer<PettyCashRequestBloc, PettyCashRequestState>(
            listener: (context, state) =>
                state is UploadAttachmentFilesSuccessState
                    ? Navigation.navigateToScreen(
                        context,
                        SuccessScreen(
                          contentWidget: PettyCashSuccessScreenContentWidget(
                            requestData: _bloc.pettyCashRequest,
                          ),
                          title: AppLocalizations.of(context)
                              .translate("Request Submitted Successfully"),
                        ))
                    : _bloc.add(UploadAttachmentFilesEvent(context)),
            listenWhen: (previous, current) =>
                current is UploadAttachmentFilesSuccessState ||
                current is SubmitRequestSuccessState,
            buildWhen: (previous, current) =>
                current is UpdatefieldValueState ||
                current is UploadAttachmentFilesSuccessState ||
                current is UploadAttachmentLoadingState ||
                current is SubmitRequestLoadingState ||
                current is SubmitRequestSuccessState ||
                current is ChangeBudgetTypeState ||
                current is ChangeValidatorValueState ||
                current is FilesUpdatedState ||
                current is SubmitRequestErrorState,
            builder: (context, state) => SubmitButtonComponent(
                text: AppLocalizations.of(context).translate('Submit'),
                onPressed: () {
                  if (_bloc.isButtonEnable(context)) {
                    if (state is! UploadAttachmentLoadingState &&
                        state is! SubmitRequestLoadingState) {
                      _bloc.add(SubmitRequestEvent());
                    } else {
                      return;
                    }
                  }
                },
                backGroung: _bloc.isButtonEnable(context)
                    ? primaryColor
                    : disablePrimaryColor,
                isLoading: state is SubmitRequestLoadingState ||
                    state is UploadAttachmentLoadingState),
          ),
        ]);
  }

  PreferredSize _getAppbarWidget() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: FavoriteComponent(
        id: "38",
        title: AppLocalizations.of(context).translate("permanent_petty_cash"),
      ),
    );
  }
}
