import 'package:flutter/material.dart';

class Navigation {
  static Future navigateToScreen(BuildContext context, Widget screen) async {
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen, fullscreenDialog: false),
    );
  }

  static Future navigateToScreenWithTransition(
      BuildContext context, Widget screen) async {
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen, fullscreenDialog: true),
    );
  }

  static Future replaceScreen(BuildContext context, Widget screen) async {
    await Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => screen, fullscreenDialog: true),
    );
  }


  static Future replaceScreenAndRemoveUntill(
      BuildContext context, Widget screen) async {
    await Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => screen, fullscreenDialog: true),
      (Route<dynamic> route) => false,
    );
  }

  static void popScreen(BuildContext context) {
    Navigator.pop(context);
  }

  static Future navigateFromBottomToTop(
    BuildContext context,
    Widget screen,
  ) async {
    await Navigator.push(
      context,
      PageRouteBuilder(
        transitionDuration: Duration(milliseconds: 300),
        reverseTransitionDuration: Duration(milliseconds: 300),
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: Offset(0.0, 1.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  static Future navigateFromTopToBottom(
    BuildContext context,
    Widget screen,
  ) async {
    await Navigator.pushAndRemoveUntil(
      context,
      PageRouteBuilder(
        transitionDuration: Duration(milliseconds: 300),
        reverseTransitionDuration: Duration(milliseconds: 300),
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: Offset(0.0, -1.0), // Start from top
              end: Offset.zero, // End at the original position
            ).animate(animation),
            child: child,
          );
        },
      ),
      (_) => false,
    );
  }
}
