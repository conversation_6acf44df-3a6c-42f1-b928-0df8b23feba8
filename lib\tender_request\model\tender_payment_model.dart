
import '../../shared/widgets/general_text_form_field.dart';

class TenderPaymentResponse {
  final String createdBy;
  final String createdById;
  final String createdDate;
  final int recId;
  final List<TenderPayment> tenderPayment;
  final List<CostCenter> costCenter;

  TenderPaymentResponse({
    required this.createdBy,
    required this.createdById,
    required this.createdDate,
    required this.recId,
    required this.tenderPayment,
    required this.costCenter,
  });

  factory TenderPaymentResponse.fromJson(Map<String, dynamic> json) {
    return TenderPaymentResponse(
      createdBy: json['createdBy'] ?? '',
      createdById: json['createdById'] ?? '',
      createdDate: json['createdDate'] ?? '',
      recId: json['recId'] ?? 0,
      tenderPayment: (json['tender_payment'] as List<dynamic>?)
          ?.map((e) => TenderPayment.fromJson(e))
          .toList() ??
          [],
      costCenter: (json['cost_center'] as List)
          .map((e) => CostCenter.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createdBy': createdBy,
      'createdById': createdById,
      'createdDate': createdDate,
      'recId': recId,
      'tender_payment': tenderPayment.map((e) => e.toJson()).toList(),
      'cost_center': costCenter.map((e) => e.toJson()).toList(),
    };
  }
}

class CostCenter {
  final int recId;
  final String costCenterCode;

  CostCenter({
    required this.recId,
    required this.costCenterCode,
  });

  factory CostCenter.fromJson(Map<String, dynamic> json) {
    return CostCenter(
      recId: json['recid'],
      costCenterCode: json['cost_center_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recid': recId,
      'cost_center_code': costCenterCode,
    };
  }
}

class TenderPayment extends GeneralSheetContent{
  final String nameAr;
  final String id;
  final String nameEn;
  final int recId;

  TenderPayment({
    required this.nameAr,
    required this.id,
    required this.nameEn,
    required this.recId,
  }) {
    nameen = nameEn;
    namear = nameAr;
  }

  factory TenderPayment.fromJson(Map<String, dynamic> json) {
    return TenderPayment(
      nameAr: json['namear'] ?? '',
      id: json['id'] ?? '',
      nameEn: json['nameen'] ?? '',
      recId: json['recid'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'namear': nameAr,
      'id': id,
      'nameen': nameEn,
      'recid': recId,
    };
  }
}
