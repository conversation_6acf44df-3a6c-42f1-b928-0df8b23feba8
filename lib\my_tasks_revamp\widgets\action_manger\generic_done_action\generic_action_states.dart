import 'generic_popup_metaData_model.dart';

abstract class GenericActionState{}
class GenericActionInitState extends GenericActionState{}
class GenericActionContentLoadingState extends GenericActionState{}
class GenericActionContentSuccessState extends GenericActionState{
  final GenericActionMetaData genericActionMetaData;
  GenericActionContentSuccessState({required this.genericActionMetaData}){
    genericActionMetaData.actionMetadata?.popupMetaData?.sort(
            (a, b) =>(a.mobileDisplayRow ?? 0).compareTo(b.mobileDisplayRow?? 0));
  }
}
class GenericActionContentErrorState extends GenericActionState{}

class SubmitGenericActionLoadingState extends GenericActionState{}
class SubmitGenericActionSuccessState extends GenericActionState{}
class SubmitGenericActionDoneState extends GenericActionState{}
class SubmitGenericActionErrorState extends GenericActionState{}
class ChangeSwitchState extends GenericActionState{}
