import 'package:eeh/admin_service/components/Numirc_component/bloc/Numirc_event.dart';
import 'package:eeh/admin_service/components/numirc_component/bloc/numirc_bloc.dart';
import 'package:eeh/shared/style.dart';
import 'package:eeh/shared/utility/font_utility.dart';
import 'package:eeh/shared/widgets/sar_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CurrencyTextfieldComponent extends StatefulWidget {
  final Function(String)? onChanged;
  final FormFieldValidator? validator;
  final String labelText;
  final bool isMandatory;

  const CurrencyTextfieldComponent({
    super.key,
    required this.onChanged,
    required this.validator,
    required this.labelText,
    this.isMandatory = false,
  });

  @override
  State<CurrencyTextfieldComponent> createState() =>
      _CurrencyTextfieldComponentState();
}

class _CurrencyTextfieldComponentState extends State<CurrencyTextfieldComponent>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
        create: (context) => NumircBloc()..add(NumircInitialEvent()),
        child: buildWidget());
  }

  buildWidget() {
    return TextFormField(
      onChanged: widget.onChanged,
      validator: widget.validator,
      style: FontUtilities.getTextStyle(TextType.regular,
          size: 12.sp, fontWeight: FontWeight.w400),
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
      ],
      decoration: InputDecoration(
        enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Color(0xff949494))),
        label: _getLabel(),
      ),
    );
  }

  _getLabel() {
    return Row(
      children: [
        Text(
          widget.labelText,
          style: FontUtilities.getTextStyle(TextType.medium,
              size: 12.sp, fontWeight: FontWeight.w500),
        ),
        SizedBox(
          width: 5.w,
        ),
        SARComponent(
          hasBrackets: false,
        ),
        Text(widget.isMandatory ? ' *' : '', style: mandatoryStyle),
      ],
    );
  }
}
