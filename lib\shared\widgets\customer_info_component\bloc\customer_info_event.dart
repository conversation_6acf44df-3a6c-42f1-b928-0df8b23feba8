
abstract class CustomerInfoEvent{}

class CustomerListInitialEvent extends CustomerInfoEvent{
}

class CustomerListEvent extends CustomerInfoEvent{
  String appKey;
  bool isInitialState;
  String searchTerm;
  CustomerListEvent(this.appKey, this.searchTerm, this.isInitialState);
}
class SearchEvent extends CustomerInfoEvent {
  String appKey;
  String searchTerm;
  SearchEvent(this.appKey,this.searchTerm);
}


