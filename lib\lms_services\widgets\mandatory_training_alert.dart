// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:eeh/shared/widgets/work_alert_widget.dart';
// import 'package:eeh/lms_services/utils/lms_utils.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:font_awesome_flutter/name_icon_mapping.dart';

// import '../../../../l10n/app_localizations.dart';
// import '../../../../shared/styles/colors.dart';
// import '../../../../shared/utility/font_utility.dart';
// import '../../../../shared/utility/navigation_utility.dart';
// import '../../../../shared/widgets/cancel_button_component.dart';
// import '../../../../shared/widgets/submit_button_component.dart';
// import 'detail_row.dart';

// class MandatoryTrainingBottomSheet {
//   static Future<void> show({
//     required BuildContext context,
//     // required GetCoursesResponseModel getCoursesResponseModel,
//   }) async {
//     // Check if there are trainings to display
//     // final trainings = getCoursesResponseModel.value ?? [];
//     // if (trainings.isEmpty) {
//     //   if (kDebugMode) {
//     //     debugPrint('No trainings to display.');
//     //   }
//     //   return;
//     // }

//     // Categorize trainings
//     final List<dynamic> pastTrainings = [];
//     final List<dynamic> ongoingTrainings = [];
//     // final List<Value> mandatoryTrainings = [];
//     final List<dynamic> plannedTrainings = [];

//     final DateTime today = DateTime.now();

//     // for (final training in trainings) {
//     //   DateTime? startDate;
//     //   DateTime? endDate;

//     //   if (training.startDate != null) {
//     //     try {
//     //       startDate = DateTime.parse(training.startDate.toString());
//     //     } catch (e) {
//     //       if (kDebugMode) {
//     //         debugPrint('Invalid start date format: $e');
//     //       }
//     //     }
//     //   }

//     //   if (training.endDate != null) {
//     //     try {
//     //       endDate = DateTime.parse(training.endDate.toString());
//     //     } catch (e) {
//     //       if (kDebugMode) {
//     //         debugPrint('Invalid end date format: $e');
//     //       }
//     //     }
//     //   }
//     //   if (training.course?.isRequired == true) {
//     //     // mandatoryTrainings.add(training);
//     //   }

//     //   if (startDate != null && endDate != null) {
//     //     if (endDate.isBefore(today)) {
//     //       // Past: both start and end date less than today
//     //       pastTrainings.add(training);
//     //     } else if (startDate.isBefore(today) && endDate.isAfter(today)) {
//     //       // Ongoing: start date less than today and end date is greater
//     //       ongoingTrainings.add(training);
//     //     } else if (startDate.isAfter(today)) {
//     //       // Planned: both start and end date is greater than today
//     //       plannedTrainings.add(training);
//     //     }
//     //   } else {
//     //     // If dates are missing, default to ongoing
//     //     ongoingTrainings.add(training);
//     //   }
//     // }

//     // Calculate if any course is expired
//     bool hasExpiredCourse = false;
//     // for (final training in trainings) {
//     //   final dueDateString = training.dueDate;
//     //   if (dueDateString != null) {
//     //     try {
//     //       DateTime dueDate = DateTime.parse(dueDateString);
//     //       if (dueDate.isBefore(DateTime.now())) {
//     //         hasExpiredCourse = true;
//     //         break;
//     //       }
//     //     } catch (e) {
//     //       if (kDebugMode) {
//     //         debugPrint('Invalid date format: $e');
//     //       }
//     //     }
//     //   }
//     // }

//     return showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       backgroundColor: Colors.transparent,
//       isDismissible: !hasExpiredCourse,
//       enableDrag: !hasExpiredCourse,
//       builder: (BuildContext context) {
//         return DraggableScrollableSheet(
//           initialChildSize: 0.85,
//           minChildSize: 0.5,
//           maxChildSize: 0.95,
//           expand: false,
//           builder: (context, scrollController) {
//             return Container(
//               padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.only(
//                   topLeft: Radius.circular(40.r),
//                   topRight: Radius.circular(40.r),
//                 ),
//               ),
//               child: getBottomSheetContent(
//                 context,
//                 // mandatoryTrainings,
//                 hasExpiredCourse,
//                 scrollController,
//               ),
//             );
//           },
//         );
//       },
//     );
//   }

//   static Widget getBottomSheetContent(
//     BuildContext context,
//     // List<Value> mandatoryTrainings,
//     bool hasExpiredCourse,
//     ScrollController scrollController,
//   ) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Container(
//           margin: EdgeInsets.symmetric(vertical: 8.h),
//           width: 40.w,
//           height: 4.h,
//           decoration: BoxDecoration(
//             color: Colors.grey[300],
//             borderRadius: BorderRadius.circular(2.r),
//           ),
//         ),
//         Container(
//           width: double.infinity,
//           padding: EdgeInsets.symmetric(vertical: 8.h),
//           child: getHeaders(context),
//         ),
//         Expanded(
//           child: getMandatoryCourses(
//             context,
//             // mandatoryTrainings,
//             scrollController,
//           ),
//         ),
//         getButton(hasExpiredCourse, context),
//         SizedBox(height: MediaQuery.of(context).padding.bottom),
//       ],
//     );
//   }

//   static Widget getButton(bool hasExpiredCourse, BuildContext context) {
//     return SizedBox(
//       width: double.infinity,
//       child: TextButton(
//         onPressed: () => Navigation.popScreen(context),
//         style: TextButton.styleFrom(
//           foregroundColor: Colors.white,
//           backgroundColor: Colors.white,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(0.0),
//           ),
//         ),
//         child: Text(AppLocalizations.of(context).translate('Skip'),
//             style: TextStyle(
//                 fontSize: 18.sp,
//                 fontWeight: FontWeight.w400,
//                 color: primaryDarkBlue)),
//       ),
//     );
//   }

//   static Widget getMandatoryCourses(
//     BuildContext context,
//     List<dynamic> mandatoryTrainings,
//     ScrollController parentScrollController,
//   ) {
//     return Padding(
//       padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 8.h),
//       child: _buildTrainingsList(
//           context, mandatoryTrainings, parentScrollController),
//     );
//   }

//   static Column getHeaders(BuildContext context) {
//     return Column(
//       children: [
//         Text(
//           AppLocalizations.of(context)
//               .translate("Complete Your Courses/Exams Before the Deadline"),
//           style: FontUtilities.getTextStyle(
//             TextType.regular,
//             size: 16.sp,
//             fontWeight: FontWeight.w500,
//             textColor: textMain,
//           ),
//           textAlign: TextAlign.center,
//         ),
//         Text(
//           AppLocalizations.of(context).translate(
//               "You have multiple courses that must be finalized before the specified due dates. Please complete them on time."),
//           style: FontUtilities.getTextStyle(
//             TextType.regular,
//             size: 14.sp,
//             fontWeight: FontWeight.w400,
//             textColor: secondTextColor,
//           ),
//           textAlign: TextAlign.center,
//         ),
//       ],
//     );
//   }

//   static Widget _buildTrainingsList(
//     BuildContext context,
//     List<dynamic> trainings,
//     ScrollController parentScrollController,
//   ) {
//     if (trainings.isEmpty) {
//       return Center(
//         child: Padding(
//           padding: EdgeInsets.symmetric(vertical: 20.h),
//           child: Text(
//             AppLocalizations.of(context)
//                 .translate("No trainings in this category"),
//             style: FontUtilities.getTextStyle(
//               TextType.medium,
//               size: 14.sp,
//               textColor: textMain,
//             ),
//             textAlign: TextAlign.center,
//           ),
//         ),
//       );
//     }

//     return ListView.separated(
//       controller: parentScrollController,
//       padding: EdgeInsets.zero,
//       itemCount: trainings.length,
//       itemBuilder: (context, index) {
//         final training = trainings[index];
//         bool isExpired = false;
//         if (training.dueDate != null) {
//           try {
//             DateTime dueDate = DateTime.parse(training.dueDate!);
//             isExpired = dueDate.isBefore(DateTime.now());
//           } catch (e) {
//             if (kDebugMode) {
//               debugPrint('Invalid date format: $e');
//             }
//           }
//         }
//         return getTrainingCard(context, training, isExpired);
//       },
//       separatorBuilder: (context, index) => SizedBox(height: 16.h),
//     );
//   }

//   static Widget getTrainingCard(
//     BuildContext context,
//     // Value trainingItem,
//     bool isExpired,
//   ) {
//     return Container(
//       margin: EdgeInsets.zero,
//       padding: EdgeInsets.all(16.h),
//       decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(8.r),
//           border: Border.all(
//             color: borderCardColor,
//             width: 1.5.w,
//           )),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           getCourseImageAndTitle(context, trainingItem,),
//           SizedBox(height: 12.h),
//           getCourseDetailsCards(context, trainingItem,),
//           SizedBox(height: 12.h),
//           getGoToCoursePageButton(context, trainingItem,),
//         ],
//       ),
//     );
//   }

//   static ElevatedButton getGoToCoursePageButton(
//       BuildContext context, Value trainingItem) {
//     return ElevatedButton(
//       onPressed: () {
//         Navigation.navigateToScreen(
//             context, SimpleWebView(url: trainingItem.course?.url ?? ''));
//       },
//       style: ElevatedButton.styleFrom(
//         backgroundColor: primaryColor,
//         foregroundColor: Colors.white,
//         padding: EdgeInsets.all(12).w,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(8.r),
//         ),
//       ),
//       child: Text(
//         AppLocalizations.of(context).translate("Go to Course Page"),
//         style: FontUtilities.getTextStyle(
//           TextType.medium,
//           size: 12.sp,
//           fontWeight: FontWeight.w500,
//           textColor: Colors.white,
//         ),
//       ),
//     );
//   }

//   static Widget getCourseDetailsCards(BuildContext context, trainingItem) {
//     return SingleChildScrollView(
//       scrollDirection: Axis.horizontal,
//       child: Row(
//         children: [
//           DetailRow(
//               label: trainingItem.course.courseType,
//               icon: getIconFromCss("fa-solid fa-airplay"),
//               onPressed: () {}),
//           SizedBox(width: 8.w),
//           trainingItem.dueDate != null
//               ? DetailRow(
//                   label:
//                       "Due Date ${LmsUtils.formatDate(trainingItem.dueDate)}",
//                   icon: getIconFromCss("fa-regular fa-calendar"),
//                   onPressed: () {})
//               : SizedBox.shrink(),
//         ],
//       ),
//     );
//   }

//   static Row getCourseImageAndTitle(BuildContext context, Value trainingItem) {
//     return Row(
//       children: [
//         Expanded(
//           child: Text(
//             AppLocalizations.of(context)
//                 .translate(trainingItem.course?.title ?? ''),
//             style: FontUtilities.getTextStyle(
//               TextType.medium,
//               size: 14.sp,
//               fontWeight: FontWeight.w700,
//               textColor: textMain,
//             ),
//           ),
//         ),
//         SizedBox(
//           width: 12.w,
//         ),
//         ClipOval(
//           child: CachedNetworkImage(
//             imageUrl: trainingItem.course?.imageUrl ?? "",
//             height: 100.h,
//             width: 100.h, // Make width equal to height for perfect circle
//             fit:
//                 BoxFit.cover, // Changed to cover for better circular appearance
//             placeholder: (context, url) => Container(
//               height: 100.h,
//               width: 100.h,
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: Colors.grey[200],
//               ),
//               child: Center(
//                 child: CircularProgressIndicator(
//                   color: primaryColor,
//                 ),
//               ),
//             ),
//             errorWidget: (context, url, error) => Container(
//               height: 100.h,
//               width: 100.h,
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: Colors.grey[200],
//               ),
//               child: Icon(
//                 Icons.image_not_supported_outlined,
//                 color: Colors.grey[400],
//                 size: 40.sp,
//               ),
//             ),
//           ),
//         )
//       ],
//     );
//   }
// }
