import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_events.dart';
import 'package:eeh/home/<USER>/Reports/reports_bloc/reports_states.dart';

import 'package:eeh/shared/favorite_component/models/all_favorite_response_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../services_screen/models/service_model.dart';
import '../../../../services_screen/models/services_response.dart';
import '../../../../services_screen/repo/services_repo.dart';
import '../../../../shared/widgets/toast.dart';

const String reportsCategoryKey="SREP";
class ReportsBloc extends Bloc<ReportsEvent, ReportsState> {
  List<ServiceModel> services = [];
  List<Elmservice> filteredList = [];
  ServicesResponse? servicesResponse;
  ServicesRepo repo = ServicesRepo();
  ResponseModel? response;
  List<ServiceModel?> favoriteServices = [];
  late BuildContext ctx;
  ReportsBloc() : super(ReportsInitialState()) {
    on<ReportsInitialEvent>((event, emit) {
/*      if (event.screenType == ServiceScreenType.mainScreen) {
        services = getServicesList(event.context);
      } else {
        services = event.services;
      }
      filteredList = services;*/
      emit(ReportsInitialState());
    });

    on<ServiceSearchEvent>((event, emit) {
      getSearch(emit, event.query);
    });
    on<ReportsLoaderEvent>((event, emit) async {
      emit(ReportsLoadingState());
      await fetchServicesList(emit);
    });

  }
  fetchServicesList(emitter) async {
    await repo
        .getServicesList()
        .then((response) => _fetchServicesListResponse(response, emitter));
  }

  _fetchServicesListResponse(response, emitter) {
    response.maybeWhen(ok: (data) {
      onFetchServicesListSuccess(data, emitter);
    }, onError: (error) {
      showMessage(error.toString(), MessageType.error);
      emitter(ReportsErrorState());
    });
  }

  onFetchServicesListSuccess(data, emitter) {
    servicesResponse = ServicesResponse.fromJson(data);
    if (servicesResponse != null) {
      mapParentAndSubService();
    }
    emitter(ReportsSuccessState());
  }

  mapParentAndSubService() {
    servicesResponse?.servicescategor?.removeWhere((reportCategory) {
      return !isReport(reportCategory);
    });
    servicesResponse?.servicescategor?.forEach((service) {
      for (Elmservice elmService in (servicesResponse?.elmservices ?? [])) {
        if ((service.categoryKey ?? '')
            .contains(elmService.serviceCategoryKey ?? '')&&isMobService(elmService)) {
          service.servicesSubCategory.add(elmService);

        }
      }
    });
  }

  getSearch(emitter, String query) {
    if (query.isNotEmpty) {
      servicesResponse?.servicescategor?.forEach((cat) {
        if(!isReport(cat)) {
          return;
        }
        final suggestions =
        cat.servicesSubCategory.where((Elmservice elmService) {
          final serviceTitleEn =
          elmService.serviceNameEn.toString().toLowerCase();
          final serviceTitleAr =
          elmService.serviceNameAr.toString().toLowerCase();
          final input = query.toLowerCase();
          return serviceTitleAr.contains(input) || serviceTitleEn.contains(input);
        }).toList();
        filteredList = suggestions ?? [];
      });
    } else {
      filteredList = [];
    }
    emitter(ReportsSearchState());
  }

}
